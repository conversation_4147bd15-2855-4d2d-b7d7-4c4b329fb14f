var Yi=Object.defineProperty;var pi=St=>{throw TypeError(St)};var Ki=(St,d,lt)=>d in St?Yi(St,d,{enumerable:!0,configurable:!0,writable:!0,value:lt}):St[d]=lt;var Kt=(St,d,lt)=>Ki(St,typeof d!="symbol"?d+"":d,lt),Re=(St,d,lt)=>d.has(St)||pi("Cannot "+lt);var t=(St,d,lt)=>(Re(St,d,"read from private field"),lt?lt.call(St):d.get(St)),Q=(St,d,lt)=>d.has(St)?pi("Cannot add the same private member more than once"):d instanceof WeakSet?d.add(St):d.set(St,lt),et=(St,d,lt,c)=>(Re(St,d,"write to private field"),c?c.call(St,lt):d.set(St,lt),lt),z=(St,d,lt)=>(Re(St,d,"access private method"),lt);var he=(St,d,lt,c)=>({set _(x){et(St,d,x,lt)},get _(){return t(St,d,c)}});import{g as getAugmentedNamespace,a as getDefaultExportFromCjs}from"./vendor-BEryHLmj.js";function _mergeNamespaces(St,d){for(var lt=0;lt<d.length;lt++){const c=d[lt];if(typeof c!="string"&&!Array.isArray(c)){for(const x in c)if(x!=="default"&&!(x in St)){const ct=Object.getOwnPropertyDescriptor(c,x);ct&&Object.defineProperty(St,x,ct.get?ct:{enumerable:!0,get:()=>c[x]})}}}return Object.freeze(Object.defineProperty(St,Symbol.toStringTag,{value:"Module"}))}function commonjsRequire(St){throw new Error('Could not dynamically require "'+St+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var pdf$2={exports:{}};const __viteBrowserExternal={},__viteBrowserExternal$1=Object.freeze(Object.defineProperty({__proto__:null,default:__viteBrowserExternal},Symbol.toStringTag,{value:"Module"})),require$$5=getAugmentedNamespace(__viteBrowserExternal$1);(function(module,exports){(function(d,lt){module.exports=d.pdfjsLib=lt()})(globalThis,()=>(()=>{var __webpack_modules__=[,(St,d)=>{var qt;Object.defineProperty(d,"__esModule",{value:!0}),d.VerbosityLevel=d.Util=d.UnknownErrorException=d.UnexpectedResponseException=d.TextRenderingMode=d.RenderingIntentFlag=d.PromiseCapability=d.PermissionFlag=d.PasswordResponses=d.PasswordException=d.PageActionEventType=d.OPS=d.MissingPDFException=d.MAX_IMAGE_SIZE_TO_CACHE=d.LINE_FACTOR=d.LINE_DESCENT_FACTOR=d.InvalidPDFException=d.ImageKind=d.IDENTITY_MATRIX=d.FormatError=d.FeatureTest=d.FONT_IDENTITY_MATRIX=d.DocumentActionEventType=d.CMapCompressionType=d.BaseException=d.BASELINE_FACTOR=d.AnnotationType=d.AnnotationReplyType=d.AnnotationPrefix=d.AnnotationMode=d.AnnotationFlag=d.AnnotationFieldFlag=d.AnnotationEditorType=d.AnnotationEditorPrefix=d.AnnotationEditorParamsType=d.AnnotationBorderStyleType=d.AnnotationActionEventType=d.AbortException=void 0,d.assert=C,d.bytesToString=ht,d.createValidAbsoluteUrl=Y,d.getModificationDate=wt,d.getUuid=Nt,d.getVerbosityLevel=W,d.info=q,d.isArrayBuffer=$,d.isArrayEqual=Tt,d.isNodeJS=void 0,d.normalizeUnicode=Ft,d.objectFromMap=yt,d.objectSize=_t,d.setVerbosityLevel=nt,d.shadow=S,d.string32=pt,d.stringToBytes=dt,d.stringToPDFString=ut,d.stringToUTF8String=vt,d.unreachable=rt,d.utf8StringToString=At,d.warn=j;const lt=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser");d.isNodeJS=lt;const c=[1,0,0,1,0,0];d.IDENTITY_MATRIX=c;const x=[.001,0,0,.001,0,0];d.FONT_IDENTITY_MATRIX=x;const ct=1e7;d.MAX_IMAGE_SIZE_TO_CACHE=ct;const V=1.35;d.LINE_FACTOR=V;const mt=.35;d.LINE_DESCENT_FACTOR=mt;const B=mt/V;d.BASELINE_FACTOR=B;const R={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};d.RenderingIntentFlag=R;const g={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};d.AnnotationMode=g;const N="pdfjs_internal_editor_";d.AnnotationEditorPrefix=N;const O={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};d.AnnotationEditorType=O;const v={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};d.AnnotationEditorParamsType=v;const b={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};d.PermissionFlag=b;const E={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};d.TextRenderingMode=E;const f={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};d.ImageKind=f;const h={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};d.AnnotationType=h;const m={GROUP:"Group",REPLY:"R"};d.AnnotationReplyType=m;const I={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};d.AnnotationFlag=I;const A={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};d.AnnotationFieldFlag=A;const r={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};d.AnnotationBorderStyleType=r;const l={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};d.AnnotationActionEventType=l;const s={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};d.DocumentActionEventType=s;const a={O:"PageOpen",C:"PageClose"};d.PageActionEventType=a;const o={ERRORS:0,WARNINGS:1,INFOS:5};d.VerbosityLevel=o;const L={NONE:0,BINARY:1};d.CMapCompressionType=L;const n={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};d.OPS=n;const _={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};d.PasswordResponses=_;let F=o.WARNINGS;function nt(bt){Number.isInteger(bt)&&(F=bt)}function W(){return F}function q(bt){F>=o.INFOS&&console.log(`Info: ${bt}`)}function j(bt){F>=o.WARNINGS&&console.log(`Warning: ${bt}`)}function rt(bt){throw new Error(bt)}function C(bt,tt){bt||rt(tt)}function U(bt){switch(bt==null?void 0:bt.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function Y(bt,tt=null,st=null){if(!bt)return null;try{if(st&&typeof bt=="string"){if(st.addDefaultProtocol&&bt.startsWith("www.")){const zt=bt.match(/\./g);(zt==null?void 0:zt.length)>=2&&(bt=`http://${bt}`)}if(st.tryConvertEncoding)try{bt=vt(bt)}catch{}}const kt=tt?new URL(bt,tt):new URL(bt);if(U(kt))return kt}catch{}return null}function S(bt,tt,st,kt=!1){return Object.defineProperty(bt,tt,{value:st,enumerable:!kt,configurable:!0,writable:!1}),st}const e=function(){function tt(st,kt){this.constructor===tt&&rt("Cannot initialize BaseException."),this.message=st,this.name=kt}return tt.prototype=new Error,tt.constructor=tt,tt}();d.BaseException=e;class i extends e{constructor(tt,st){super(tt,"PasswordException"),this.code=st}}d.PasswordException=i;class u extends e{constructor(tt,st){super(tt,"UnknownErrorException"),this.details=st}}d.UnknownErrorException=u;class T extends e{constructor(tt){super(tt,"InvalidPDFException")}}d.InvalidPDFException=T;class P extends e{constructor(tt){super(tt,"MissingPDFException")}}d.MissingPDFException=P;class k extends e{constructor(tt,st){super(tt,"UnexpectedResponseException"),this.status=st}}d.UnexpectedResponseException=k;class G extends e{constructor(tt){super(tt,"FormatError")}}d.FormatError=G;class it extends e{constructor(tt){super(tt,"AbortException")}}d.AbortException=it;function ht(bt){(typeof bt!="object"||(bt==null?void 0:bt.length)===void 0)&&rt("Invalid argument for bytesToString");const tt=bt.length,st=8192;if(tt<st)return String.fromCharCode.apply(null,bt);const kt=[];for(let zt=0;zt<tt;zt+=st){const Gt=Math.min(zt+st,tt),D=bt.subarray(zt,Gt);kt.push(String.fromCharCode.apply(null,D))}return kt.join("")}function dt(bt){typeof bt!="string"&&rt("Invalid argument for stringToBytes");const tt=bt.length,st=new Uint8Array(tt);for(let kt=0;kt<tt;++kt)st[kt]=bt.charCodeAt(kt)&255;return st}function pt(bt){return String.fromCharCode(bt>>24&255,bt>>16&255,bt>>8&255,bt&255)}function _t(bt){return Object.keys(bt).length}function yt(bt){const tt=Object.create(null);for(const[st,kt]of bt)tt[st]=kt;return tt}function K(){const bt=new Uint8Array(4);return bt[0]=1,new Uint32Array(bt.buffer,0,1)[0]===1}function Z(){try{return new Function(""),!0}catch{return!1}}class p{static get isLittleEndian(){return S(this,"isLittleEndian",K())}static get isEvalSupported(){return S(this,"isEvalSupported",Z())}static get isOffscreenCanvasSupported(){return S(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get platform(){return typeof navigator>"u"?S(this,"platform",{isWin:!1,isMac:!1}):S(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){var tt,st;return S(this,"isCSSRoundSupported",(st=(tt=globalThis.CSS)==null?void 0:tt.supports)==null?void 0:st.call(tt,"width: round(1.5px, 1px)"))}}d.FeatureTest=p;const M=[...Array(256).keys()].map(bt=>bt.toString(16).padStart(2,"0"));class X{static makeHexColor(tt,st,kt){return`#${M[tt]}${M[st]}${M[kt]}`}static scaleMinMax(tt,st){let kt;tt[0]?(tt[0]<0&&(kt=st[0],st[0]=st[1],st[1]=kt),st[0]*=tt[0],st[1]*=tt[0],tt[3]<0&&(kt=st[2],st[2]=st[3],st[3]=kt),st[2]*=tt[3],st[3]*=tt[3]):(kt=st[0],st[0]=st[2],st[2]=kt,kt=st[1],st[1]=st[3],st[3]=kt,tt[1]<0&&(kt=st[2],st[2]=st[3],st[3]=kt),st[2]*=tt[1],st[3]*=tt[1],tt[2]<0&&(kt=st[0],st[0]=st[1],st[1]=kt),st[0]*=tt[2],st[1]*=tt[2]),st[0]+=tt[4],st[1]+=tt[4],st[2]+=tt[5],st[3]+=tt[5]}static transform(tt,st){return[tt[0]*st[0]+tt[2]*st[1],tt[1]*st[0]+tt[3]*st[1],tt[0]*st[2]+tt[2]*st[3],tt[1]*st[2]+tt[3]*st[3],tt[0]*st[4]+tt[2]*st[5]+tt[4],tt[1]*st[4]+tt[3]*st[5]+tt[5]]}static applyTransform(tt,st){const kt=tt[0]*st[0]+tt[1]*st[2]+st[4],zt=tt[0]*st[1]+tt[1]*st[3]+st[5];return[kt,zt]}static applyInverseTransform(tt,st){const kt=st[0]*st[3]-st[1]*st[2],zt=(tt[0]*st[3]-tt[1]*st[2]+st[2]*st[5]-st[4]*st[3])/kt,Gt=(-tt[0]*st[1]+tt[1]*st[0]+st[4]*st[1]-st[5]*st[0])/kt;return[zt,Gt]}static getAxialAlignedBoundingBox(tt,st){const kt=this.applyTransform(tt,st),zt=this.applyTransform(tt.slice(2,4),st),Gt=this.applyTransform([tt[0],tt[3]],st),D=this.applyTransform([tt[2],tt[1]],st);return[Math.min(kt[0],zt[0],Gt[0],D[0]),Math.min(kt[1],zt[1],Gt[1],D[1]),Math.max(kt[0],zt[0],Gt[0],D[0]),Math.max(kt[1],zt[1],Gt[1],D[1])]}static inverseTransform(tt){const st=tt[0]*tt[3]-tt[1]*tt[2];return[tt[3]/st,-tt[1]/st,-tt[2]/st,tt[0]/st,(tt[2]*tt[5]-tt[4]*tt[3])/st,(tt[4]*tt[1]-tt[5]*tt[0])/st]}static singularValueDecompose2dScale(tt){const st=[tt[0],tt[2],tt[1],tt[3]],kt=tt[0]*st[0]+tt[1]*st[2],zt=tt[0]*st[1]+tt[1]*st[3],Gt=tt[2]*st[0]+tt[3]*st[2],D=tt[2]*st[1]+tt[3]*st[3],ft=(kt+D)/2,Ct=Math.sqrt((kt+D)**2-4*(kt*D-Gt*zt))/2,Dt=ft+Ct||1,Ot=ft-Ct||1;return[Math.sqrt(Dt),Math.sqrt(Ot)]}static normalizeRect(tt){const st=tt.slice(0);return tt[0]>tt[2]&&(st[0]=tt[2],st[2]=tt[0]),tt[1]>tt[3]&&(st[1]=tt[3],st[3]=tt[1]),st}static intersect(tt,st){const kt=Math.max(Math.min(tt[0],tt[2]),Math.min(st[0],st[2])),zt=Math.min(Math.max(tt[0],tt[2]),Math.max(st[0],st[2]));if(kt>zt)return null;const Gt=Math.max(Math.min(tt[1],tt[3]),Math.min(st[1],st[3])),D=Math.min(Math.max(tt[1],tt[3]),Math.max(st[1],st[3]));return Gt>D?null:[kt,Gt,zt,D]}static bezierBoundingBox(tt,st,kt,zt,Gt,D,ft,Ct){const Dt=[],Ot=[[],[]];let Pt,w,y,H,at,ot,gt,Et;for(let Ht=0;Ht<2;++Ht){if(Ht===0?(w=6*tt-12*kt+6*Gt,Pt=-3*tt+9*kt-9*Gt+3*ft,y=3*kt-3*tt):(w=6*st-12*zt+6*D,Pt=-3*st+9*zt-9*D+3*Ct,y=3*zt-3*st),Math.abs(Pt)<1e-12){if(Math.abs(w)<1e-12)continue;H=-y/w,0<H&&H<1&&Dt.push(H);continue}gt=w*w-4*y*Pt,Et=Math.sqrt(gt),!(gt<0)&&(at=(-w+Et)/(2*Pt),0<at&&at<1&&Dt.push(at),ot=(-w-Et)/(2*Pt),0<ot&&ot<1&&Dt.push(ot))}let It=Dt.length,Mt;const xt=It;for(;It--;)H=Dt[It],Mt=1-H,Ot[0][It]=Mt*Mt*Mt*tt+3*Mt*Mt*H*kt+3*Mt*H*H*Gt+H*H*H*ft,Ot[1][It]=Mt*Mt*Mt*st+3*Mt*Mt*H*zt+3*Mt*H*H*D+H*H*H*Ct;return Ot[0][xt]=tt,Ot[1][xt]=st,Ot[0][xt+1]=ft,Ot[1][xt+1]=Ct,Ot[0].length=Ot[1].length=xt+2,[Math.min(...Ot[0]),Math.min(...Ot[1]),Math.max(...Ot[0]),Math.max(...Ot[1])]}}d.Util=X;const J=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function ut(bt){if(bt[0]>="ï"){let st;if(bt[0]==="þ"&&bt[1]==="ÿ"?st="utf-16be":bt[0]==="ÿ"&&bt[1]==="þ"?st="utf-16le":bt[0]==="ï"&&bt[1]==="»"&&bt[2]==="¿"&&(st="utf-8"),st)try{const kt=new TextDecoder(st,{fatal:!0}),zt=dt(bt);return kt.decode(zt)}catch(kt){j(`stringToPDFString: "${kt}".`)}}const tt=[];for(let st=0,kt=bt.length;st<kt;st++){const zt=J[bt.charCodeAt(st)];tt.push(zt?String.fromCharCode(zt):bt.charAt(st))}return tt.join("")}function vt(bt){return decodeURIComponent(escape(bt))}function At(bt){return unescape(encodeURIComponent(bt))}function $(bt){return typeof bt=="object"&&(bt==null?void 0:bt.byteLength)!==void 0}function Tt(bt,tt){if(bt.length!==tt.length)return!1;for(let st=0,kt=bt.length;st<kt;st++)if(bt[st]!==tt[st])return!1;return!0}function wt(bt=new Date){return[bt.getUTCFullYear().toString(),(bt.getUTCMonth()+1).toString().padStart(2,"0"),bt.getUTCDate().toString().padStart(2,"0"),bt.getUTCHours().toString().padStart(2,"0"),bt.getUTCMinutes().toString().padStart(2,"0"),bt.getUTCSeconds().toString().padStart(2,"0")].join("")}class jt{constructor(){Q(this,qt,!1);this.promise=new Promise((tt,st)=>{this.resolve=kt=>{et(this,qt,!0),tt(kt)},this.reject=kt=>{et(this,qt,!0),st(kt)}})}get settled(){return t(this,qt)}}qt=new WeakMap,d.PromiseCapability=jt;let Bt=null,Xt=null;function Ft(bt){return Bt||(Bt=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,Xt=new Map([["ﬅ","ſt"]])),bt.replaceAll(Bt,(tt,st,kt)=>st?st.normalize("NFKC"):Xt.get(kt))}function Nt(){if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.randomUUID)=="function")return crypto.randomUUID();const bt=new Uint8Array(32);if(typeof crypto<"u"&&typeof(crypto==null?void 0:crypto.getRandomValues)=="function")crypto.getRandomValues(bt);else for(let tt=0;tt<32;tt++)bt[tt]=Math.floor(Math.random()*255);return ht(bt)}const $t="pdfjs_internal_id_";d.AnnotationPrefix=$t},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{var St,lt,c,x,re,_e,mt,B,R,g,N,O,v,b,E,Ae,h,m,De,A,r;Object.defineProperty(exports,"__esModule",{value:!0}),exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultFilterFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0,Object.defineProperty(exports,"SVGGraphics",{enumerable:!0,get:function(){return _displaySvg.SVGGraphics}}),exports.build=void 0,exports.getDocument=getDocument,exports.version=void 0;var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(3),_display_utils=__w_pdfjs_require__(6),_font_loader=__w_pdfjs_require__(9),_displayNode_utils=__w_pdfjs_require__(10),_canvas=__w_pdfjs_require__(11),_worker_options=__w_pdfjs_require__(14),_message_handler=__w_pdfjs_require__(15),_metadata=__w_pdfjs_require__(16),_optional_content_config=__w_pdfjs_require__(17),_transport_stream=__w_pdfjs_require__(18),_displayFetch_stream=__w_pdfjs_require__(19),_displayNetwork=__w_pdfjs_require__(22),_displayNode_stream=__w_pdfjs_require__(23),_displaySvg=__w_pdfjs_require__(24),_xfa_text=__w_pdfjs_require__(25);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DELAYED_CLEANUP_TIMEOUT=5e3,DefaultCanvasFactory=_util.isNodeJS?_displayNode_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;const DefaultCMapReaderFactory=_util.isNodeJS?_displayNode_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;const DefaultFilterFactory=_util.isNodeJS?_displayNode_utils.NodeFilterFactory:_display_utils.DOMFilterFactory;exports.DefaultFilterFactory=DefaultFilterFactory;const DefaultStandardFontDataFactory=_util.isNodeJS?_displayNode_utils.NodeStandardFontDataFactory:_display_utils.DOMStandardFontDataFactory;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;function getDocument(s){if(typeof s=="string"||s instanceof URL?s={url:s}:(0,_util.isArrayBuffer)(s)&&(s={data:s}),typeof s!="object")throw new Error("Invalid parameter in getDocument, need parameter object.");if(!s.url&&!s.data&&!s.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const a=new PDFDocumentLoadingTask,{docId:o}=a,L=s.url?getUrlProp(s.url):null,n=s.data?getDataProp(s.data):null,_=s.httpHeaders||null,F=s.withCredentials===!0,nt=s.password??null,W=s.range instanceof PDFDataRangeTransport?s.range:null,q=Number.isInteger(s.rangeChunkSize)&&s.rangeChunkSize>0?s.rangeChunkSize:DEFAULT_RANGE_CHUNK_SIZE;let j=s.worker instanceof PDFWorker?s.worker:null;const rt=s.verbosity,C=typeof s.docBaseUrl=="string"&&!(0,_display_utils.isDataScheme)(s.docBaseUrl)?s.docBaseUrl:null,U=typeof s.cMapUrl=="string"?s.cMapUrl:null,Y=s.cMapPacked!==!1,S=s.CMapReaderFactory||DefaultCMapReaderFactory,e=typeof s.standardFontDataUrl=="string"?s.standardFontDataUrl:null,i=s.StandardFontDataFactory||DefaultStandardFontDataFactory,u=s.stopAtErrors!==!0,T=Number.isInteger(s.maxImageSize)&&s.maxImageSize>-1?s.maxImageSize:-1,P=s.isEvalSupported!==!1,k=typeof s.isOffscreenCanvasSupported=="boolean"?s.isOffscreenCanvasSupported:!_util.isNodeJS,G=Number.isInteger(s.canvasMaxAreaInBytes)?s.canvasMaxAreaInBytes:-1,it=typeof s.disableFontFace=="boolean"?s.disableFontFace:_util.isNodeJS,ht=s.fontExtraProperties===!0,dt=s.enableXfa===!0,pt=s.ownerDocument||globalThis.document,_t=s.disableRange===!0,yt=s.disableStream===!0,K=s.disableAutoFetch===!0,Z=s.pdfBug===!0,p=W?W.length:s.length??NaN,M=typeof s.useSystemFonts=="boolean"?s.useSystemFonts:!_util.isNodeJS&&!it,X=typeof s.useWorkerFetch=="boolean"?s.useWorkerFetch:S===_display_utils.DOMCMapReaderFactory&&i===_display_utils.DOMStandardFontDataFactory&&U&&e&&(0,_display_utils.isValidFetchUrl)(U,document.baseURI)&&(0,_display_utils.isValidFetchUrl)(e,document.baseURI),J=s.canvasFactory||new DefaultCanvasFactory({ownerDocument:pt}),ut=s.filterFactory||new DefaultFilterFactory({docId:o,ownerDocument:pt}),vt=null;(0,_util.setVerbosityLevel)(rt);const At={canvasFactory:J,filterFactory:ut};if(X||(At.cMapReaderFactory=new S({baseUrl:U,isCompressed:Y}),At.standardFontDataFactory=new i({baseUrl:e})),!j){const wt={verbosity:rt,port:_worker_options.GlobalWorkerOptions.workerPort};j=wt.port?PDFWorker.fromPort(wt):new PDFWorker(wt),a._worker=j}const $={docId:o,apiVersion:"3.11.174",data:n,password:nt,disableAutoFetch:K,rangeChunkSize:q,length:p,docBaseUrl:C,enableXfa:dt,evaluatorOptions:{maxImageSize:T,disableFontFace:it,ignoreErrors:u,isEvalSupported:P,isOffscreenCanvasSupported:k,canvasMaxAreaInBytes:G,fontExtraProperties:ht,useSystemFonts:M,cMapUrl:X?U:null,standardFontDataUrl:X?e:null}},Tt={ignoreErrors:u,isEvalSupported:P,disableFontFace:it,fontExtraProperties:ht,enableXfa:dt,ownerDocument:pt,disableAutoFetch:K,pdfBug:Z,styleElement:vt};return j.promise.then(function(){if(a.destroyed)throw new Error("Loading aborted");const wt=_fetchDocument(j,$),jt=new Promise(function(Bt){let Xt;W?Xt=new _transport_stream.PDFDataTransportStream({length:p,initialData:W.initialData,progressiveDone:W.progressiveDone,contentDispositionFilename:W.contentDispositionFilename,disableRange:_t,disableStream:yt},W):n||(Xt=(Nt=>_util.isNodeJS?new _displayNode_stream.PDFNodeStream(Nt):(0,_display_utils.isValidFetchUrl)(Nt.url)?new _displayFetch_stream.PDFFetchStream(Nt):new _displayNetwork.PDFNetworkStream(Nt))({url:L,length:p,httpHeaders:_,withCredentials:F,rangeChunkSize:q,disableRange:_t,disableStream:yt})),Bt(Xt)});return Promise.all([wt,jt]).then(function([Bt,Xt]){if(a.destroyed)throw new Error("Loading aborted");const Ft=new _message_handler.MessageHandler(o,Bt,j.port),Nt=new WorkerTransport(Ft,a,Xt,Tt,At);a._transport=Nt,Ft.send("Ready",null)})}).catch(a._capability.reject),a}async function _fetchDocument(s,a){if(s.destroyed)throw new Error("Worker was destroyed");const o=await s.messageHandler.sendWithPromise("GetDocRequest",a,a.data?[a.data.buffer]:null);if(s.destroyed)throw new Error("Worker was destroyed");return o}function getUrlProp(s){if(s instanceof URL)return s.href;try{return new URL(s,window.location).href}catch{if(_util.isNodeJS&&typeof s=="string")return s}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(s){if(_util.isNodeJS&&typeof Buffer<"u"&&s instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(s instanceof Uint8Array&&s.byteLength===s.buffer.byteLength)return s;if(typeof s=="string")return(0,_util.stringToBytes)(s);if(typeof s=="object"&&!isNaN(s==null?void 0:s.length)||(0,_util.isArrayBuffer)(s))return new Uint8Array(s);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}const d=class d{constructor(){this._capability=new _util.PromiseCapability,this._transport=null,this._worker=null,this.docId=`d${he(d,St)._++}`,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){var a,o,L;this.destroyed=!0;try{(a=this._worker)!=null&&a.port&&(this._worker._pendingDestroy=!0),await((o=this._transport)==null?void 0:o.destroy())}catch(n){throw(L=this._worker)!=null&&L.port&&delete this._worker._pendingDestroy,n}this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}};St=new WeakMap,Q(d,St,0);let PDFDocumentLoadingTask=d;exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(a,o,L=!1,n=null){this.length=a,this.initialData=o,this.progressiveDone=L,this.contentDispositionFilename=n,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=new _util.PromiseCapability}addRangeListener(a){this._rangeListeners.push(a)}addProgressListener(a){this._progressListeners.push(a)}addProgressiveReadListener(a){this._progressiveReadListeners.push(a)}addProgressiveDoneListener(a){this._progressiveDoneListeners.push(a)}onDataRange(a,o){for(const L of this._rangeListeners)L(a,o)}onDataProgress(a,o){this._readyCapability.promise.then(()=>{for(const L of this._progressListeners)L(a,o)})}onDataProgressiveRead(a){this._readyCapability.promise.then(()=>{for(const o of this._progressiveReadListeners)o(a)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const a of this._progressiveDoneListeners)a()})}transportReady(){this._readyCapability.resolve()}requestDataRange(a,o){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(a,o){this._pdfInfo=a,this._transport=o,Object.defineProperty(this,"getJavaScript",{value:()=>((0,_display_utils.deprecated)("`PDFDocumentProxy.getJavaScript`, please use `PDFDocumentProxy.getJSActions` instead."),this.getJSActions().then(L=>{if(!L)return L;const n=[];for(const _ in L)n.push(...L[_]);return n}))})}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(a){return this._transport.getPage(a)}getPageIndex(a){return this._transport.getPageIndex(a)}getDestinations(){return this._transport.getDestinations()}getDestination(a){return this._transport.getDestination(a)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(a=!1){return this._transport.startCleanup(a||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{constructor(a,o,L,n=!1){Q(this,x);Q(this,lt,null);Q(this,c,!1);this._pageIndex=a,this._pageInfo=o,this._transport=L,this._stats=n?new _display_utils.StatTimer:null,this._pdfBug=n,this.commonObjs=L.commonObjs,this.objs=new PDFObjects,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:a,rotation:o=this.rotate,offsetX:L=0,offsetY:n=0,dontFlip:_=!1}={}){return new _display_utils.PageViewport({viewBox:this.view,scale:a,rotation:o,offsetX:L,offsetY:n,dontFlip:_})}getAnnotations({intent:a="display"}={}){const o=this._transport.getRenderingIntent(a);return this._transport.getAnnotations(this._pageIndex,o.renderingIntent)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){var a;return((a=this._transport._htmlForXfa)==null?void 0:a.children[this._pageIndex])||null}render({canvasContext:a,viewport:o,intent:L="display",annotationMode:n=_util.AnnotationMode.ENABLE,transform:_=null,background:F=null,optionalContentConfigPromise:nt=null,annotationCanvasMap:W=null,pageColors:q=null,printAnnotationStorage:j=null}){var i,u;(i=this._stats)==null||i.time("Overall");const rt=this._transport.getRenderingIntent(L,n,j);et(this,c,!1),z(this,x,_e).call(this),nt||(nt=this._transport.getOptionalContentConfig());let C=this._intentStates.get(rt.cacheKey);C||(C=Object.create(null),this._intentStates.set(rt.cacheKey,C)),C.streamReaderCancelTimeout&&(clearTimeout(C.streamReaderCancelTimeout),C.streamReaderCancelTimeout=null);const U=!!(rt.renderingIntent&_util.RenderingIntentFlag.PRINT);C.displayReadyCapability||(C.displayReadyCapability=new _util.PromiseCapability,C.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(u=this._stats)==null||u.time("Page Request"),this._pumpOperatorList(rt));const Y=T=>{var P,k;C.renderTasks.delete(S),(this._maybeCleanupAfterRender||U)&&et(this,c,!0),z(this,x,re).call(this,!U),T?(S.capability.reject(T),this._abortOperatorList({intentState:C,reason:T instanceof Error?T:new Error(T)})):S.capability.resolve(),(P=this._stats)==null||P.timeEnd("Rendering"),(k=this._stats)==null||k.timeEnd("Overall")},S=new InternalRenderTask({callback:Y,params:{canvasContext:a,viewport:o,transform:_,background:F},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:W,operatorList:C.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!U,pdfBug:this._pdfBug,pageColors:q});(C.renderTasks||(C.renderTasks=new Set)).add(S);const e=S.task;return Promise.all([C.displayReadyCapability.promise,nt]).then(([T,P])=>{var k;if(this.destroyed){Y();return}(k=this._stats)==null||k.time("Rendering"),S.initializeGraphics({transparency:T,optionalContentConfig:P}),S.operatorListChanged()}).catch(Y),e}getOperatorList({intent:a="display",annotationMode:o=_util.AnnotationMode.ENABLE,printAnnotationStorage:L=null}={}){var W;function n(){F.operatorList.lastChunk&&(F.opListReadCapability.resolve(F.operatorList),F.renderTasks.delete(nt))}const _=this._transport.getRenderingIntent(a,o,L,!0);let F=this._intentStates.get(_.cacheKey);F||(F=Object.create(null),this._intentStates.set(_.cacheKey,F));let nt;return F.opListReadCapability||(nt=Object.create(null),nt.operatorListChanged=n,F.opListReadCapability=new _util.PromiseCapability,(F.renderTasks||(F.renderTasks=new Set)).add(nt),F.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},(W=this._stats)==null||W.time("Page Request"),this._pumpOperatorList(_)),F.opListReadCapability.promise}streamTextContent({includeMarkedContent:a=!1,disableNormalization:o=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:a===!0,disableNormalization:o===!0},{highWaterMark:100,size(n){return n.items.length}})}getTextContent(a={}){if(this._transport._htmlForXfa)return this.getXfa().then(L=>_xfa_text.XfaText.textContent(L));const o=this.streamTextContent(a);return new Promise(function(L,n){function _(){F.read().then(function({value:W,done:q}){if(q){L(nt);return}Object.assign(nt.styles,W.styles),nt.items.push(...W.items),_()},n)}const F=o.getReader(),nt={items:[],styles:Object.create(null)};_()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const a=[];for(const o of this._intentStates.values())if(this._abortOperatorList({intentState:o,reason:new Error("Page was destroyed."),force:!0}),!o.opListReadCapability)for(const L of o.renderTasks)a.push(L.completed),L.cancel();return this.objs.clear(),et(this,c,!1),z(this,x,_e).call(this),Promise.all(a)}cleanup(a=!1){et(this,c,!0);const o=z(this,x,re).call(this,!1);return a&&o&&this._stats&&(this._stats=new _display_utils.StatTimer),o}_startRenderPage(a,o){var n,_;const L=this._intentStates.get(o);L&&((n=this._stats)==null||n.timeEnd("Page Request"),(_=L.displayReadyCapability)==null||_.resolve(a))}_renderPageChunk(a,o){for(let L=0,n=a.length;L<n;L++)o.operatorList.fnArray.push(a.fnArray[L]),o.operatorList.argsArray.push(a.argsArray[L]);o.operatorList.lastChunk=a.lastChunk,o.operatorList.separateAnnots=a.separateAnnots;for(const L of o.renderTasks)L.operatorListChanged();a.lastChunk&&z(this,x,re).call(this,!0)}_pumpOperatorList({renderingIntent:a,cacheKey:o,annotationStorageSerializable:L}){const{map:n,transfers:_}=L,nt=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:a,cacheKey:o,annotationStorage:n},_).getReader(),W=this._intentStates.get(o);W.streamReader=nt;const q=()=>{nt.read().then(({value:j,done:rt})=>{if(rt){W.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(j,W),q())},j=>{if(W.streamReader=null,!this._transport.destroyed){if(W.operatorList){W.operatorList.lastChunk=!0;for(const rt of W.renderTasks)rt.operatorListChanged();z(this,x,re).call(this,!0)}if(W.displayReadyCapability)W.displayReadyCapability.reject(j);else if(W.opListReadCapability)W.opListReadCapability.reject(j);else throw j}})};q()}_abortOperatorList({intentState:a,reason:o,force:L=!1}){if(a.streamReader){if(a.streamReaderCancelTimeout&&(clearTimeout(a.streamReaderCancelTimeout),a.streamReaderCancelTimeout=null),!L){if(a.renderTasks.size>0)return;if(o instanceof _display_utils.RenderingCancelledException){let n=RENDERING_CANCELLED_TIMEOUT;o.extraDelay>0&&o.extraDelay<1e3&&(n+=o.extraDelay),a.streamReaderCancelTimeout=setTimeout(()=>{a.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:a,reason:o,force:!0})},n);return}}if(a.streamReader.cancel(new _util.AbortException(o.message)).catch(()=>{}),a.streamReader=null,!this._transport.destroyed){for(const[n,_]of this._intentStates)if(_===a){this._intentStates.delete(n);break}this.cleanup()}}}get stats(){return this._stats}}lt=new WeakMap,c=new WeakMap,x=new WeakSet,re=function(a=!1){if(z(this,x,_e).call(this),!t(this,c)||this.destroyed)return!1;if(a)return et(this,lt,setTimeout(()=>{et(this,lt,null),z(this,x,re).call(this,!1)},DELAYED_CLEANUP_TIMEOUT)),!1;for(const{renderTasks:o,operatorList:L}of this._intentStates.values())if(o.size>0||!L.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),et(this,c,!1),!0},_e=function(){t(this,lt)&&(clearTimeout(t(this,lt)),et(this,lt,null))},exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{constructor(){Q(this,mt,new Set);Q(this,B,Promise.resolve())}postMessage(a,o){const L={data:structuredClone(a,o?{transfer:o}:null)};t(this,B).then(()=>{for(const n of t(this,mt))n.call(this,L)})}addEventListener(a,o){t(this,mt).add(o)}removeEventListener(a,o){t(this,mt).delete(o)}terminate(){t(this,mt).clear()}}mt=new WeakMap,B=new WeakMap,exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};exports.PDFWorkerUtil=PDFWorkerUtil;{if(_util.isNodeJS&&typeof commonjsRequire=="function")PDFWorkerUtil.isWorkerDisabled=!0,PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js";else if(typeof document=="object"){const s=(R=document==null?void 0:document.currentScript)==null?void 0:R.src;s&&(PDFWorkerUtil.fallbackWorkerSrc=s.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(s,a){let o;try{if(o=new URL(s),!o.origin||o.origin==="null")return!1}catch{return!1}const L=new URL(a,o);return o.origin===L.origin},PDFWorkerUtil.createCDNWrapper=function(s){const a=`importScripts("${s}");`;return URL.createObjectURL(new Blob([a]))}}const _PDFWorker=class _PDFWorker{constructor({name:s=null,port:a=null,verbosity:o=(0,_util.getVerbosityLevel)()}={}){var L;if(this.name=s,this.destroyed=!1,this.verbosity=o,this._readyCapability=new _util.PromiseCapability,this._port=null,this._webWorker=null,this._messageHandler=null,a){if((L=t(_PDFWorker,g))!=null&&L.has(a))throw new Error("Cannot use more than one PDFWorker per port.");(t(_PDFWorker,g)||et(_PDFWorker,g,new WeakMap)).set(a,this),this._initializeFromPort(a);return}this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(s){this._port=s,this._messageHandler=new _message_handler.MessageHandler("main","worker",s),this._messageHandler.on("ready",function(){}),this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!PDFWorkerUtil.isWorkerDisabled&&!_PDFWorker._mainThreadWorkerMessageHandler){let{workerSrc:s}=_PDFWorker;try{PDFWorkerUtil.isSameOrigin(window.location.href,s)||(s=PDFWorkerUtil.createCDNWrapper(new URL(s,window.location).href));const a=new Worker(s),o=new _message_handler.MessageHandler("main","worker",a),L=()=>{a.removeEventListener("error",n),o.destroy(),a.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},n=()=>{this._webWorker||L()};a.addEventListener("error",n),o.on("test",F=>{if(a.removeEventListener("error",n),this.destroyed){L();return}F?(this._messageHandler=o,this._port=a,this._webWorker=a,this._readyCapability.resolve(),o.send("configure",{verbosity:this.verbosity})):(this._setupFakeWorker(),o.destroy(),a.terminate())}),o.on("ready",F=>{if(a.removeEventListener("error",n),this.destroyed){L();return}try{_()}catch{this._setupFakeWorker()}});const _=()=>{const F=new Uint8Array;o.send("test",F,[F.buffer])};_();return}catch{(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){PDFWorkerUtil.isWorkerDisabled||((0,_util.warn)("Setting up fake worker."),PDFWorkerUtil.isWorkerDisabled=!0),_PDFWorker._setupFakeWorkerGlobal.then(s=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const a=new LoopbackPort;this._port=a;const o=`fake${PDFWorkerUtil.fakeWorkerId++}`,L=new _message_handler.MessageHandler(o+"_worker",o,a);s.setup(L,a);const n=new _message_handler.MessageHandler(o,o+"_worker",a);this._messageHandler=n,this._readyCapability.resolve(),n.send("configure",{verbosity:this.verbosity})}).catch(s=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${s.message}".`))})}destroy(){var s;this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),(s=t(_PDFWorker,g))==null||s.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(s){var o;if(!(s!=null&&s.port))throw new Error("PDFWorker.fromPort - invalid method signature.");const a=(o=t(this,g))==null?void 0:o.get(s.port);if(a){if(a._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return a}return new _PDFWorker(s)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(PDFWorkerUtil.fallbackWorkerSrc!==null)return _util.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.'),PDFWorkerUtil.fallbackWorkerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){var s;try{return((s=globalThis.pdfjsWorker)==null?void 0:s.WorkerMessageHandler)||null}catch{return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_util.isNodeJS&&typeof commonjsRequire=="function"){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}return await(0,_display_utils.loadScript)(this.workerSrc),window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}};g=new WeakMap,Q(_PDFWorker,g);let PDFWorker=_PDFWorker;exports.PDFWorker=PDFWorker;class WorkerTransport{constructor(a,o,L,n,_){Q(this,E);Q(this,N,new Map);Q(this,O,new Map);Q(this,v,new Map);Q(this,b,null);this.messageHandler=a,this.loadingTask=o,this.commonObjs=new PDFObjects,this.fontLoader=new _font_loader.FontLoader({ownerDocument:n.ownerDocument,styleElement:n.styleElement}),this._params=n,this.canvasFactory=_.canvasFactory,this.filterFactory=_.filterFactory,this.cMapReaderFactory=_.cMapReaderFactory,this.standardFontDataFactory=_.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=L,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=new _util.PromiseCapability,this.setupMessageHandler()}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}getRenderingIntent(a,o=_util.AnnotationMode.ENABLE,L=null,n=!1){let _=_util.RenderingIntentFlag.DISPLAY,F=_annotation_storage.SerializableEmpty;switch(a){case"any":_=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":_=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)(`getRenderingIntent - invalid intent: ${a}`)}switch(o){case _util.AnnotationMode.DISABLE:_+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:_+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:_+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE,F=(_&_util.RenderingIntentFlag.PRINT&&L instanceof _annotation_storage.PrintAnnotationStorage?L:this.annotationStorage).serializable;break;default:(0,_util.warn)(`getRenderingIntent - invalid annotationMode: ${o}`)}return n&&(_+=_util.RenderingIntentFlag.OPLIST),{renderingIntent:_,cacheKey:`${_}_${F.hash}`,annotationStorageSerializable:F}}destroy(){var L;if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=new _util.PromiseCapability,(L=t(this,b))==null||L.reject(new Error("Worker was destroyed during onPassword callback"));const a=[];for(const n of t(this,O).values())a.push(n._destroy());t(this,O).clear(),t(this,v).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const o=this.messageHandler.sendWithPromise("Terminate",null);return a.push(o),Promise.all(a).then(()=>{var n;this.commonObjs.clear(),this.fontLoader.clear(),t(this,N).clear(),this.filterFactory.destroy(),(n=this._networkStream)==null||n.cancelAllRequests(new _util.AbortException("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:a,loadingTask:o}=this;a.on("GetReader",(L,n)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=_=>{this._lastProgress={loaded:_.loaded,total:_.total}},n.onPull=()=>{this._fullReader.read().then(function({value:_,done:F}){if(F){n.close();return}(0,_util.assert)(_ instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),n.enqueue(new Uint8Array(_),1,[_])}).catch(_=>{n.error(_)})},n.onCancel=_=>{this._fullReader.cancel(_),n.ready.catch(F=>{if(!this.destroyed)throw F})}}),a.on("ReaderHeadersReady",L=>{const n=new _util.PromiseCapability,_=this._fullReader;return _.headersReady.then(()=>{var F;(!_.isStreamingSupported||!_.isRangeSupported)&&(this._lastProgress&&((F=o.onProgress)==null||F.call(o,this._lastProgress)),_.onProgress=nt=>{var W;(W=o.onProgress)==null||W.call(o,{loaded:nt.loaded,total:nt.total})}),n.resolve({isStreamingSupported:_.isStreamingSupported,isRangeSupported:_.isRangeSupported,contentLength:_.contentLength})},n.reject),n.promise}),a.on("GetRangeReader",(L,n)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const _=this._networkStream.getRangeReader(L.begin,L.end);if(!_){n.close();return}n.onPull=()=>{_.read().then(function({value:F,done:nt}){if(nt){n.close();return}(0,_util.assert)(F instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),n.enqueue(new Uint8Array(F),1,[F])}).catch(F=>{n.error(F)})},n.onCancel=F=>{_.cancel(F),n.ready.catch(nt=>{if(!this.destroyed)throw nt})}}),a.on("GetDoc",({pdfInfo:L})=>{this._numPages=L.numPages,this._htmlForXfa=L.htmlForXfa,delete L.htmlForXfa,o._capability.resolve(new PDFDocumentProxy(L,this))}),a.on("DocException",function(L){let n;switch(L.name){case"PasswordException":n=new _util.PasswordException(L.message,L.code);break;case"InvalidPDFException":n=new _util.InvalidPDFException(L.message);break;case"MissingPDFException":n=new _util.MissingPDFException(L.message);break;case"UnexpectedResponseException":n=new _util.UnexpectedResponseException(L.message,L.status);break;case"UnknownErrorException":n=new _util.UnknownErrorException(L.message,L.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}o._capability.reject(n)}),a.on("PasswordRequest",L=>{if(et(this,b,new _util.PromiseCapability),o.onPassword){const n=_=>{_ instanceof Error?t(this,b).reject(_):t(this,b).resolve({password:_})};try{o.onPassword(n,L.code)}catch(_){t(this,b).reject(_)}}else t(this,b).reject(new _util.PasswordException(L.message,L.code));return t(this,b).promise}),a.on("DataLoaded",L=>{var n;(n=o.onProgress)==null||n.call(o,{loaded:L.length,total:L.length}),this.downloadInfoCapability.resolve(L)}),a.on("StartRenderPage",L=>{if(this.destroyed)return;t(this,O).get(L.pageIndex)._startRenderPage(L.transparency,L.cacheKey)}),a.on("commonobj",([L,n,_])=>{var F;if(!this.destroyed&&!this.commonObjs.has(L))switch(n){case"Font":const nt=this._params;if("error"in _){const j=_.error;(0,_util.warn)(`Error during font loading: ${j}`),this.commonObjs.resolve(L,j);break}const W=nt.pdfBug&&((F=globalThis.FontInspector)!=null&&F.enabled)?(j,rt)=>globalThis.FontInspector.fontAdded(j,rt):null,q=new _font_loader.FontFaceObject(_,{isEvalSupported:nt.isEvalSupported,disableFontFace:nt.disableFontFace,ignoreErrors:nt.ignoreErrors,inspectFont:W});this.fontLoader.bind(q).catch(j=>a.sendWithPromise("FontFallback",{id:L})).finally(()=>{!nt.fontExtraProperties&&q.data&&(q.data=null),this.commonObjs.resolve(L,q)});break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(L,_);break;default:throw new Error(`Got unknown common object type ${n}`)}}),a.on("obj",([L,n,_,F])=>{var W;if(this.destroyed)return;const nt=t(this,O).get(n);if(!nt.objs.has(L))switch(_){case"Image":if(nt.objs.resolve(L,F),F){let q;if(F.bitmap){const{width:j,height:rt}=F;q=j*rt*4}else q=((W=F.data)==null?void 0:W.length)||0;q>_util.MAX_IMAGE_SIZE_TO_CACHE&&(nt._maybeCleanupAfterRender=!0)}break;case"Pattern":nt.objs.resolve(L,F);break;default:throw new Error(`Got unknown object type ${_}`)}}),a.on("DocProgress",L=>{var n;this.destroyed||(n=o.onProgress)==null||n.call(o,{loaded:L.loaded,total:L.total})}),a.on("FetchBuiltInCMap",L=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(L):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))),a.on("FetchStandardFontData",L=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(L):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.")))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){var L;this.annotationStorage.size<=0&&(0,_util.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:a,transfers:o}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:a,filename:((L=this._fullReader)==null?void 0:L.filename)??null},o).finally(()=>{this.annotationStorage.resetModified()})}getPage(a){if(!Number.isInteger(a)||a<=0||a>this._numPages)return Promise.reject(new Error("Invalid page request."));const o=a-1,L=t(this,v).get(o);if(L)return L;const n=this.messageHandler.sendWithPromise("GetPage",{pageIndex:o}).then(_=>{if(this.destroyed)throw new Error("Transport destroyed");const F=new PDFPageProxy(o,_,this,this._params.pdfBug);return t(this,O).set(o,F),F});return t(this,v).set(o,n),n}getPageIndex(a){return typeof a!="object"||a===null||!Number.isInteger(a.num)||a.num<0||!Number.isInteger(a.gen)||a.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:a.num,gen:a.gen})}getAnnotations(a,o){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:a,intent:o})}getFieldObjects(){return z(this,E,Ae).call(this,"GetFieldObjects")}hasJSActions(){return z(this,E,Ae).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(a){return typeof a!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:a})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return z(this,E,Ae).call(this,"GetDocJSActions")}getPageJSActions(a){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:a})}getStructTree(a){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:a})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then(a=>new _optional_content_config.OptionalContentConfig(a))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const a="GetMetadata",o=t(this,N).get(a);if(o)return o;const L=this.messageHandler.sendWithPromise(a,null).then(n=>{var _,F;return{info:n[0],metadata:n[1]?new _metadata.Metadata(n[1]):null,contentDispositionFilename:((_=this._fullReader)==null?void 0:_.filename)??null,contentLength:((F=this._fullReader)==null?void 0:F.contentLength)??null}});return t(this,N).set(a,L),L}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(a=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const o of t(this,O).values())if(!o.cleanup())throw new Error(`startCleanup: Page ${o.pageNumber} is currently rendering.`);this.commonObjs.clear(),a||this.fontLoader.clear(),t(this,N).clear(),this.filterFactory.destroy(!0)}}get loadingParams(){const{disableAutoFetch:a,enableXfa:o}=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:a,enableXfa:o})}}N=new WeakMap,O=new WeakMap,v=new WeakMap,b=new WeakMap,E=new WeakSet,Ae=function(a,o=null){const L=t(this,N).get(a);if(L)return L;const n=this.messageHandler.sendWithPromise(a,o);return t(this,N).set(a,n),n};class PDFObjects{constructor(){Q(this,m);Q(this,h,Object.create(null))}get(a,o=null){if(o){const n=z(this,m,De).call(this,a);return n.capability.promise.then(()=>o(n.data)),null}const L=t(this,h)[a];if(!(L!=null&&L.capability.settled))throw new Error(`Requesting object that isn't resolved yet ${a}.`);return L.data}has(a){const o=t(this,h)[a];return(o==null?void 0:o.capability.settled)||!1}resolve(a,o=null){const L=z(this,m,De).call(this,a);L.data=o,L.capability.resolve()}clear(){var a;for(const o in t(this,h)){const{data:L}=t(this,h)[o];(a=L==null?void 0:L.bitmap)==null||a.close()}et(this,h,Object.create(null))}}h=new WeakMap,m=new WeakSet,De=function(a){var o;return(o=t(this,h))[a]||(o[a]={capability:new _util.PromiseCapability,data:null})};class RenderTask{constructor(a){Q(this,A,null);et(this,A,a),this.onContinue=null}get promise(){return t(this,A).capability.promise}cancel(a=0){t(this,A).cancel(null,a)}get separateAnnots(){const{separateAnnots:a}=t(this,A).operatorList;if(!a)return!1;const{annotationCanvasMap:o}=t(this,A);return a.form||a.canvas&&(o==null?void 0:o.size)>0}}A=new WeakMap,exports.RenderTask=RenderTask;const l=class l{constructor({callback:a,params:o,objs:L,commonObjs:n,annotationCanvasMap:_,operatorList:F,pageIndex:nt,canvasFactory:W,filterFactory:q,useRequestAnimationFrame:j=!1,pdfBug:rt=!1,pageColors:C=null}){this.callback=a,this.params=o,this.objs=L,this.commonObjs=n,this.annotationCanvasMap=_,this.operatorListIdx=null,this.operatorList=F,this._pageIndex=nt,this.canvasFactory=W,this.filterFactory=q,this._pdfBug=rt,this.pageColors=C,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=j===!0&&typeof window<"u",this.cancelled=!1,this.capability=new _util.PromiseCapability,this.task=new RenderTask(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=o.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:a=!1,optionalContentConfig:o}){var nt,W;if(this.cancelled)return;if(this._canvas){if(t(l,r).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");t(l,r).add(this._canvas)}this._pdfBug&&((nt=globalThis.StepperManager)!=null&&nt.enabled)&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:L,viewport:n,transform:_,background:F}=this.params;this.gfx=new _canvas.CanvasGraphics(L,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:o},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:_,viewport:n,transparency:a,background:F}),this.operatorListIdx=0,this.graphicsReady=!0,(W=this.graphicsReadyCallback)==null||W.call(this)}cancel(a=null,o=0){var L;this.running=!1,this.cancelled=!0,(L=this.gfx)==null||L.endDrawing(),t(l,r).delete(this._canvas),this.callback(a||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,o))}operatorListChanged(){var a;if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}(a=this.stepper)==null||a.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame(()=>{this._nextBound().catch(this._cancelBound)}):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),t(l,r).delete(this._canvas),this.callback())))}};r=new WeakMap,Q(l,r,new WeakSet);let InternalRenderTask=l;const version="3.11.174";exports.version=version;const build="ce8716743";exports.build=build},(St,d,lt)=>{var R,g,N,gi,v;Object.defineProperty(d,"__esModule",{value:!0}),d.SerializableEmpty=d.PrintAnnotationStorage=d.AnnotationStorage=void 0;var c=lt(1),x=lt(4),ct=lt(8);const V=Object.freeze({map:null,hash:"",transfers:void 0});d.SerializableEmpty=V;class mt{constructor(){Q(this,N);Q(this,R,!1);Q(this,g,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(E,f){const h=t(this,g).get(E);return h===void 0?f:Object.assign(f,h)}getRawValue(E){return t(this,g).get(E)}remove(E){if(t(this,g).delete(E),t(this,g).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const f of t(this,g).values())if(f instanceof x.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(E,f){const h=t(this,g).get(E);let m=!1;if(h!==void 0)for(const[I,A]of Object.entries(f))h[I]!==A&&(m=!0,h[I]=A);else m=!0,t(this,g).set(E,f);m&&z(this,N,gi).call(this),f instanceof x.AnnotationEditor&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(f.constructor._type)}has(E){return t(this,g).has(E)}getAll(){return t(this,g).size>0?(0,c.objectFromMap)(t(this,g)):null}setAll(E){for(const[f,h]of Object.entries(E))this.setValue(f,h)}get size(){return t(this,g).size}resetModified(){t(this,R)&&(et(this,R,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new B(this)}get serializable(){if(t(this,g).size===0)return V;const E=new Map,f=new ct.MurmurHash3_64,h=[],m=Object.create(null);let I=!1;for(const[A,r]of t(this,g)){const l=r instanceof x.AnnotationEditor?r.serialize(!1,m):r;l&&(E.set(A,l),f.update(`${A}:${JSON.stringify(l)}`),I||(I=!!l.bitmap))}if(I)for(const A of E.values())A.bitmap&&h.push(A.bitmap);return E.size>0?{map:E,hash:f.hexdigest(),transfers:h}:V}}R=new WeakMap,g=new WeakMap,N=new WeakSet,gi=function(){t(this,R)||(et(this,R,!0),typeof this.onSetModified=="function"&&this.onSetModified())},d.AnnotationStorage=mt;class B extends mt{constructor(f){super();Q(this,v);const{map:h,hash:m,transfers:I}=f.serializable,A=structuredClone(h,I?{transfer:I}:null);et(this,v,{map:A,hash:m,transfers:I})}get print(){(0,c.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return t(this,v)}}v=new WeakMap,d.PrintAnnotationStorage=B},(St,d,lt)=>{var B,R,g,N,O,v,b,E,f,h,m,I,A,r,l,Ie,Le,o,Oe,Ne,mi,bi,_i,Be,Ai;Object.defineProperty(d,"__esModule",{value:!0}),d.AnnotationEditor=void 0;var c=lt(5),x=lt(1),ct=lt(6);const j=class j{constructor(C){Q(this,l);Q(this,B,"");Q(this,R,!1);Q(this,g,null);Q(this,N,null);Q(this,O,null);Q(this,v,!1);Q(this,b,null);Q(this,E,this.focusin.bind(this));Q(this,f,this.focusout.bind(this));Q(this,h,!1);Q(this,m,!1);Q(this,I,!1);Kt(this,"_initialOptions",Object.create(null));Kt(this,"_uiManager",null);Kt(this,"_focusEventsAllowed",!0);Kt(this,"_l10nPromise",null);Q(this,A,!1);Q(this,r,j._zIndex++);this.constructor===j&&(0,x.unreachable)("Cannot initialize AnnotationEditor."),this.parent=C.parent,this.id=C.id,this.width=this.height=null,this.pageIndex=C.parent.pageIndex,this.name=C.name,this.div=null,this._uiManager=C.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=C.isCentered,this._structTreeParentId=null;const{rotation:U,rawDims:{pageWidth:Y,pageHeight:S,pageX:e,pageY:i}}=this.parent.viewport;this.rotation=U,this.pageRotation=(360+U-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[Y,S],this.pageTranslation=[e,i];const[u,T]=this.parentDimensions;this.x=C.x/u,this.y=C.y/T,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return(0,x.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(C){const U=new mt({id:C.parent.getNextId(),parent:C.parent,uiManager:C._uiManager});U.annotationElementId=C.annotationElementId,U.deleted=!0,U._uiManager.addToAnnotationStorage(U)}static initialize(C,U=null){if(j._l10nPromise||(j._l10nPromise=new Map(["editor_alt_text_button_label","editor_alt_text_edit_button_label","editor_alt_text_decorative_tooltip"].map(S=>[S,C.get(S)]))),U!=null&&U.strings)for(const S of U.strings)j._l10nPromise.set(S,C.get(S));if(j._borderLineWidth!==-1)return;const Y=getComputedStyle(document.documentElement);j._borderLineWidth=parseFloat(Y.getPropertyValue("--outline-width"))||0}static updateDefaultParams(C,U){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(C){return!1}static paste(C,U){(0,x.unreachable)("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return t(this,A)}set _isDraggable(C){var U;et(this,A,C),(U=this.div)==null||U.classList.toggle("draggable",C)}center(){const[C,U]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*U/(C*2),this.y+=this.width*C/(U*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*U/(C*2),this.y-=this.width*C/(U*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(C){this._uiManager.addCommands(C)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=t(this,r)}setParent(C){C!==null&&(this.pageIndex=C.pageIndex,this.pageDimensions=C.pageDimensions),this.parent=C}focusin(C){this._focusEventsAllowed&&(t(this,h)?et(this,h,!1):this.parent.setSelected(this))}focusout(C){var Y;if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;const U=C.relatedTarget;U!=null&&U.closest(`#${this.id}`)||(C.preventDefault(),(Y=this.parent)!=null&&Y.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(C,U,Y,S){const[e,i]=this.parentDimensions;[Y,S]=this.screenToPageTranslation(Y,S),this.x=(C+Y)/e,this.y=(U+S)/i,this.fixAndSetPosition()}translate(C,U){z(this,l,Ie).call(this,this.parentDimensions,C,U)}translateInPage(C,U){z(this,l,Ie).call(this,this.pageDimensions,C,U),this.div.scrollIntoView({block:"nearest"})}drag(C,U){const[Y,S]=this.parentDimensions;if(this.x+=C/Y,this.y+=U/S,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:P,y:k}=this.div.getBoundingClientRect();this.parent.findNewParent(this,P,k)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:e,y:i}=this;const[u,T]=z(this,l,Le).call(this);e+=u,i+=T,this.div.style.left=`${(100*e).toFixed(2)}%`,this.div.style.top=`${(100*i).toFixed(2)}%`,this.div.scrollIntoView({block:"nearest"})}fixAndSetPosition(){const[C,U]=this.pageDimensions;let{x:Y,y:S,width:e,height:i}=this;switch(e*=C,i*=U,Y*=C,S*=U,this.rotation){case 0:Y=Math.max(0,Math.min(C-e,Y)),S=Math.max(0,Math.min(U-i,S));break;case 90:Y=Math.max(0,Math.min(C-i,Y)),S=Math.min(U,Math.max(e,S));break;case 180:Y=Math.min(C,Math.max(e,Y)),S=Math.min(U,Math.max(i,S));break;case 270:Y=Math.min(C,Math.max(i,Y)),S=Math.max(0,Math.min(U-e,S));break}this.x=Y/=C,this.y=S/=U;const[u,T]=z(this,l,Le).call(this);Y+=u,S+=T;const{style:P}=this.div;P.left=`${(100*Y).toFixed(2)}%`,P.top=`${(100*S).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(C,U){var Y;return z(Y=j,o,Oe).call(Y,C,U,this.parentRotation)}pageTranslationToScreen(C,U){var Y;return z(Y=j,o,Oe).call(Y,C,U,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:C,pageDimensions:[U,Y]}=this,S=U*C,e=Y*C;return x.FeatureTest.isCSSRoundSupported?[Math.round(S),Math.round(e)]:[S,e]}setDims(C,U){var e;const[Y,S]=this.parentDimensions;this.div.style.width=`${(100*C/Y).toFixed(2)}%`,t(this,v)||(this.div.style.height=`${(100*U/S).toFixed(2)}%`),(e=t(this,g))==null||e.classList.toggle("small",C<j.SMALL_EDITOR_SIZE||U<j.SMALL_EDITOR_SIZE)}fixDims(){const{style:C}=this.div,{height:U,width:Y}=C,S=Y.endsWith("%"),e=!t(this,v)&&U.endsWith("%");if(S&&e)return;const[i,u]=this.parentDimensions;S||(C.width=`${(100*parseFloat(Y)/i).toFixed(2)}%`),!t(this,v)&&!e&&(C.height=`${(100*parseFloat(U)/u).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}async addAltTextButton(){if(t(this,g))return;const C=et(this,g,document.createElement("button"));C.className="altText";const U=await j._l10nPromise.get("editor_alt_text_button_label");C.textContent=U,C.setAttribute("aria-label",U),C.tabIndex="0",C.addEventListener("contextmenu",ct.noContextMenu),C.addEventListener("pointerdown",Y=>Y.stopPropagation()),C.addEventListener("click",Y=>{Y.preventDefault(),this._uiManager.editAltText(this)},{capture:!0}),C.addEventListener("keydown",Y=>{Y.target===C&&Y.key==="Enter"&&(Y.preventDefault(),this._uiManager.editAltText(this))}),z(this,l,Be).call(this),this.div.append(C),j.SMALL_EDITOR_SIZE||(j.SMALL_EDITOR_SIZE=Math.min(128,Math.round(C.getBoundingClientRect().width*1.4)))}getClientDimensions(){return this.div.getBoundingClientRect()}get altTextData(){return{altText:t(this,B),decorative:t(this,R)}}set altTextData({altText:C,decorative:U}){t(this,B)===C&&t(this,R)===U||(et(this,B,C),et(this,R,U),z(this,l,Be).call(this))}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.setAttribute("tabIndex",0),this.setInForeground(),this.div.addEventListener("focusin",t(this,E)),this.div.addEventListener("focusout",t(this,f));const[C,U]=this.parentDimensions;this.parentRotation%180!==0&&(this.div.style.maxWidth=`${(100*U/C).toFixed(2)}%`,this.div.style.maxHeight=`${(100*C/U).toFixed(2)}%`);const[Y,S]=this.getInitialTranslation();return this.translate(Y,S),(0,c.bindEvents)(this,this.div,["pointerdown"]),this.div}pointerdown(C){const{isMac:U}=x.FeatureTest.platform;if(C.button!==0||C.ctrlKey&&U){C.preventDefault();return}et(this,h,!0),z(this,l,Ai).call(this,C)}moveInDOM(){var C;(C=this.parent)==null||C.moveEditorInDOM(this)}_setParentAndPosition(C,U,Y){C.changeParent(this),this.x=U,this.y=Y,this.fixAndSetPosition()}getRect(C,U){const Y=this.parentScale,[S,e]=this.pageDimensions,[i,u]=this.pageTranslation,T=C/Y,P=U/Y,k=this.x*S,G=this.y*e,it=this.width*S,ht=this.height*e;switch(this.rotation){case 0:return[k+T+i,e-G-P-ht+u,k+T+it+i,e-G-P+u];case 90:return[k+P+i,e-G+T+u,k+P+ht+i,e-G+T+it+u];case 180:return[k-T-it+i,e-G+P+u,k-T+i,e-G+P+ht+u];case 270:return[k-P-ht+i,e-G-T-it+u,k-P+i,e-G-T+u];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(C,U){const[Y,S,e,i]=C,u=e-Y,T=i-S;switch(this.rotation){case 0:return[Y,U-i,u,T];case 90:return[Y,U-S,T,u];case 180:return[e,U-S,u,T];case 270:return[e,U-i,T,u];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){et(this,I,!0)}disableEditMode(){et(this,I,!1)}isInEditMode(){return t(this,I)}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){var C,U;(C=this.div)==null||C.addEventListener("focusin",t(this,E)),(U=this.div)==null||U.addEventListener("focusout",t(this,f))}serialize(C=!1,U=null){(0,x.unreachable)("An editor must be serializable")}static deserialize(C,U,Y){const S=new this.prototype.constructor({parent:U,id:U.getNextId(),uiManager:Y});S.rotation=C.rotation;const[e,i]=S.pageDimensions,[u,T,P,k]=S.getRectInCurrentCoords(C.rect,i);return S.x=u/e,S.y=T/i,S.width=P/e,S.height=k/i,S}remove(){var C;this.div.removeEventListener("focusin",t(this,E)),this.div.removeEventListener("focusout",t(this,f)),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),(C=t(this,g))==null||C.remove(),et(this,g,null),et(this,N,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(z(this,l,mi).call(this),t(this,b).classList.remove("hidden"))}select(){var C;this.makeResizable(),(C=this.div)==null||C.classList.add("selectedEditor")}unselect(){var C,U,Y;(C=t(this,b))==null||C.classList.add("hidden"),(U=this.div)==null||U.classList.remove("selectedEditor"),(Y=this.div)!=null&&Y.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus()}updateParams(C,U){}disableEditing(){t(this,g)&&(t(this,g).hidden=!0)}enableEditing(){t(this,g)&&(t(this,g).hidden=!1)}enterInEditMode(){}get contentDiv(){return this.div}get isEditing(){return t(this,m)}set isEditing(C){et(this,m,C),this.parent&&(C?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(C,U){et(this,v,!0);const Y=C/U,{style:S}=this.div;S.aspectRatio=Y,S.height="auto"}static get MIN_SIZE(){return 16}};B=new WeakMap,R=new WeakMap,g=new WeakMap,N=new WeakMap,O=new WeakMap,v=new WeakMap,b=new WeakMap,E=new WeakMap,f=new WeakMap,h=new WeakMap,m=new WeakMap,I=new WeakMap,A=new WeakMap,r=new WeakMap,l=new WeakSet,Ie=function([C,U],Y,S){[Y,S]=this.screenToPageTranslation(Y,S),this.x+=Y/C,this.y+=S/U,this.fixAndSetPosition()},Le=function(){const[C,U]=this.parentDimensions,{_borderLineWidth:Y}=j,S=Y/C,e=Y/U;switch(this.rotation){case 90:return[-S,e];case 180:return[S,e];case 270:return[S,-e];default:return[-S,-e]}},o=new WeakSet,Oe=function(C,U,Y){switch(Y){case 90:return[U,-C];case 180:return[-C,-U];case 270:return[-U,C];default:return[C,U]}},Ne=function(C){switch(C){case 90:{const[U,Y]=this.pageDimensions;return[0,-U/Y,Y/U,0]}case 180:return[-1,0,0,-1];case 270:{const[U,Y]=this.pageDimensions;return[0,U/Y,-Y/U,0]}default:return[1,0,0,1]}},mi=function(){if(t(this,b))return;et(this,b,document.createElement("div")),t(this,b).classList.add("resizers");const C=["topLeft","topRight","bottomRight","bottomLeft"];this._willKeepAspectRatio||C.push("topMiddle","middleRight","bottomMiddle","middleLeft");for(const U of C){const Y=document.createElement("div");t(this,b).append(Y),Y.classList.add("resizer",U),Y.addEventListener("pointerdown",z(this,l,bi).bind(this,U)),Y.addEventListener("contextmenu",ct.noContextMenu)}this.div.prepend(t(this,b))},bi=function(C,U){U.preventDefault();const{isMac:Y}=x.FeatureTest.platform;if(U.button!==0||U.ctrlKey&&Y)return;const S=z(this,l,_i).bind(this,C),e=this._isDraggable;this._isDraggable=!1;const i={passive:!0,capture:!0};window.addEventListener("pointermove",S,i);const u=this.x,T=this.y,P=this.width,k=this.height,G=this.parent.div.style.cursor,it=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(U.target).cursor;const ht=()=>{this._isDraggable=e,window.removeEventListener("pointerup",ht),window.removeEventListener("blur",ht),window.removeEventListener("pointermove",S,i),this.parent.div.style.cursor=G,this.div.style.cursor=it;const dt=this.x,pt=this.y,_t=this.width,yt=this.height;dt===u&&pt===T&&_t===P&&yt===k||this.addCommands({cmd:()=>{this.width=_t,this.height=yt,this.x=dt,this.y=pt;const[K,Z]=this.parentDimensions;this.setDims(K*_t,Z*yt),this.fixAndSetPosition()},undo:()=>{this.width=P,this.height=k,this.x=u,this.y=T;const[K,Z]=this.parentDimensions;this.setDims(K*P,Z*k),this.fixAndSetPosition()},mustExec:!0})};window.addEventListener("pointerup",ht),window.addEventListener("blur",ht)},_i=function(C,U){const[Y,S]=this.parentDimensions,e=this.x,i=this.y,u=this.width,T=this.height,P=j.MIN_SIZE/Y,k=j.MIN_SIZE/S,G=Ft=>Math.round(Ft*1e4)/1e4,it=z(this,l,Ne).call(this,this.rotation),ht=(Ft,Nt)=>[it[0]*Ft+it[2]*Nt,it[1]*Ft+it[3]*Nt],dt=z(this,l,Ne).call(this,360-this.rotation),pt=(Ft,Nt)=>[dt[0]*Ft+dt[2]*Nt,dt[1]*Ft+dt[3]*Nt];let _t,yt,K=!1,Z=!1;switch(C){case"topLeft":K=!0,_t=(Ft,Nt)=>[0,0],yt=(Ft,Nt)=>[Ft,Nt];break;case"topMiddle":_t=(Ft,Nt)=>[Ft/2,0],yt=(Ft,Nt)=>[Ft/2,Nt];break;case"topRight":K=!0,_t=(Ft,Nt)=>[Ft,0],yt=(Ft,Nt)=>[0,Nt];break;case"middleRight":Z=!0,_t=(Ft,Nt)=>[Ft,Nt/2],yt=(Ft,Nt)=>[0,Nt/2];break;case"bottomRight":K=!0,_t=(Ft,Nt)=>[Ft,Nt],yt=(Ft,Nt)=>[0,0];break;case"bottomMiddle":_t=(Ft,Nt)=>[Ft/2,Nt],yt=(Ft,Nt)=>[Ft/2,0];break;case"bottomLeft":K=!0,_t=(Ft,Nt)=>[0,Nt],yt=(Ft,Nt)=>[Ft,0];break;case"middleLeft":Z=!0,_t=(Ft,Nt)=>[0,Nt/2],yt=(Ft,Nt)=>[Ft,Nt/2];break}const p=_t(u,T),M=yt(u,T);let X=ht(...M);const J=G(e+X[0]),ut=G(i+X[1]);let vt=1,At=1,[$,Tt]=this.screenToPageTranslation(U.movementX,U.movementY);if([$,Tt]=pt($/Y,Tt/S),K){const Ft=Math.hypot(u,T);vt=At=Math.max(Math.min(Math.hypot(M[0]-p[0]-$,M[1]-p[1]-Tt)/Ft,1/u,1/T),P/u,k/T)}else Z?vt=Math.max(P,Math.min(1,Math.abs(M[0]-p[0]-$)))/u:At=Math.max(k,Math.min(1,Math.abs(M[1]-p[1]-Tt)))/T;const wt=G(u*vt),jt=G(T*At);X=ht(...yt(wt,jt));const Bt=J-X[0],Xt=ut-X[1];this.width=wt,this.height=jt,this.x=Bt,this.y=Xt,this.setDims(Y*wt,S*jt),this.fixAndSetPosition()},Be=async function(){var Y;const C=t(this,g);if(!C)return;if(!t(this,B)&&!t(this,R)){C.classList.remove("done"),(Y=t(this,N))==null||Y.remove();return}j._l10nPromise.get("editor_alt_text_edit_button_label").then(S=>{C.setAttribute("aria-label",S)});let U=t(this,N);if(!U){et(this,N,U=document.createElement("span")),U.className="tooltip",U.setAttribute("role","tooltip");const S=U.id=`alt-text-tooltip-${this.id}`;C.setAttribute("aria-describedby",S);const e=100;C.addEventListener("mouseenter",()=>{et(this,O,setTimeout(()=>{et(this,O,null),t(this,N).classList.add("show"),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"alt_text_tooltip"}}})},e))}),C.addEventListener("mouseleave",()=>{var i;clearTimeout(t(this,O)),et(this,O,null),(i=t(this,N))==null||i.classList.remove("show")})}C.classList.add("done"),U.innerText=t(this,R)?await j._l10nPromise.get("editor_alt_text_decorative_tooltip"):t(this,B),U.parentNode||C.append(U)},Ai=function(C){if(!this._isDraggable)return;const U=this._uiManager.isSelected(this);this._uiManager.setUpDragSession();let Y,S;U&&(Y={passive:!0,capture:!0},S=i=>{const[u,T]=this.screenToPageTranslation(i.movementX,i.movementY);this._uiManager.dragSelectedEditors(u,T)},window.addEventListener("pointermove",S,Y));const e=()=>{if(window.removeEventListener("pointerup",e),window.removeEventListener("blur",e),U&&window.removeEventListener("pointermove",S,Y),et(this,h,!1),!this._uiManager.endDragSession()){const{isMac:i}=x.FeatureTest.platform;C.ctrlKey&&!i||C.shiftKey||C.metaKey&&i?this.parent.toggleSelected(this):this.parent.setSelected(this)}};window.addEventListener("pointerup",e),window.addEventListener("blur",e)},Q(j,o),Kt(j,"_borderLineWidth",-1),Kt(j,"_colorManager",new c.ColorManager),Kt(j,"_zIndex",1),Kt(j,"SMALL_EDITOR_SIZE",0);let V=j;d.AnnotationEditor=V;class mt extends V{constructor(C){super(C),this.annotationElementId=C.annotationElementId,this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}},(St,d,lt)=>{var v,b,E,f,h,Ue,A,r,l,s,a,yi,n,_,F,nt,W,q,j,rt,C,U,Y,S,e,i,u,T,P,k,G,it,ht,dt,pt,_t,yt,K,Z,p,M,X,J,ut,vt,At,$,vi,je,He,ye,We,Ge,Zt,de,Si,Ei,ze,ue,Xe;Object.defineProperty(d,"__esModule",{value:!0}),d.KeyboardManager=d.CommandManager=d.ColorManager=d.AnnotationEditorUIManager=void 0,d.bindEvents=ct,d.opacityToHex=V;var c=lt(1),x=lt(6);function ct(Gt,D,ft){for(const Ct of ft)D.addEventListener(Ct,Gt[Ct].bind(Gt))}function V(Gt){return Math.round(Math.min(255,Math.max(1,255*Gt))).toString(16).padStart(2,"0")}class mt{constructor(){Q(this,v,0)}getId(){return`${c.AnnotationEditorPrefix}${he(this,v)._++}`}}v=new WeakMap;const I=class I{constructor(){Q(this,h);Q(this,b,(0,c.getUuid)());Q(this,E,0);Q(this,f,null)}static get _isSVGFittingCanvas(){const D='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',Ct=new OffscreenCanvas(1,3).getContext("2d"),Dt=new Image;Dt.src=D;const Ot=Dt.decode().then(()=>(Ct.drawImage(Dt,0,0,1,1,0,0,1,3),new Uint32Array(Ct.getImageData(0,0,1,1).data.buffer)[0]===0));return(0,c.shadow)(this,"_isSVGFittingCanvas",Ot)}async getFromFile(D){const{lastModified:ft,name:Ct,size:Dt,type:Ot}=D;return z(this,h,Ue).call(this,`${ft}_${Ct}_${Dt}_${Ot}`,D)}async getFromUrl(D){return z(this,h,Ue).call(this,D,D)}async getFromId(D){t(this,f)||et(this,f,new Map);const ft=t(this,f).get(D);return ft?ft.bitmap?(ft.refCounter+=1,ft):ft.file?this.getFromFile(ft.file):this.getFromUrl(ft.url):null}getSvgUrl(D){const ft=t(this,f).get(D);return ft!=null&&ft.isSvg?ft.svgUrl:null}deleteId(D){t(this,f)||et(this,f,new Map);const ft=t(this,f).get(D);ft&&(ft.refCounter-=1,ft.refCounter===0&&(ft.bitmap=null))}isValidId(D){return D.startsWith(`image_${t(this,b)}_`)}};b=new WeakMap,E=new WeakMap,f=new WeakMap,h=new WeakSet,Ue=async function(D,ft){t(this,f)||et(this,f,new Map);let Ct=t(this,f).get(D);if(Ct===null)return null;if(Ct!=null&&Ct.bitmap)return Ct.refCounter+=1,Ct;try{Ct||(Ct={bitmap:null,id:`image_${t(this,b)}_${he(this,E)._++}`,refCounter:0,isSvg:!1});let Dt;if(typeof ft=="string"){Ct.url=ft;const Ot=await fetch(ft);if(!Ot.ok)throw new Error(Ot.statusText);Dt=await Ot.blob()}else Dt=Ct.file=ft;if(Dt.type==="image/svg+xml"){const Ot=I._isSVGFittingCanvas,Pt=new FileReader,w=new Image,y=new Promise((H,at)=>{w.onload=()=>{Ct.bitmap=w,Ct.isSvg=!0,H()},Pt.onload=async()=>{const ot=Ct.svgUrl=Pt.result;w.src=await Ot?`${ot}#svgView(preserveAspectRatio(none))`:ot},w.onerror=Pt.onerror=at});Pt.readAsDataURL(Dt),await y}else Ct.bitmap=await createImageBitmap(Dt);Ct.refCounter=1}catch(Dt){console.error(Dt),Ct=null}return t(this,f).set(D,Ct),Ct&&t(this,f).set(Ct.id,Ct),Ct};let B=I;class R{constructor(D=128){Q(this,A,[]);Q(this,r,!1);Q(this,l);Q(this,s,-1);et(this,l,D)}add({cmd:D,undo:ft,mustExec:Ct,type:Dt=NaN,overwriteIfSameType:Ot=!1,keepUndo:Pt=!1}){if(Ct&&D(),t(this,r))return;const w={cmd:D,undo:ft,type:Dt};if(t(this,s)===-1){t(this,A).length>0&&(t(this,A).length=0),et(this,s,0),t(this,A).push(w);return}if(Ot&&t(this,A)[t(this,s)].type===Dt){Pt&&(w.undo=t(this,A)[t(this,s)].undo),t(this,A)[t(this,s)]=w;return}const y=t(this,s)+1;y===t(this,l)?t(this,A).splice(0,1):(et(this,s,y),y<t(this,A).length&&t(this,A).splice(y)),t(this,A).push(w)}undo(){t(this,s)!==-1&&(et(this,r,!0),t(this,A)[t(this,s)].undo(),et(this,r,!1),et(this,s,t(this,s)-1))}redo(){t(this,s)<t(this,A).length-1&&(et(this,s,t(this,s)+1),et(this,r,!0),t(this,A)[t(this,s)].cmd(),et(this,r,!1))}hasSomethingToUndo(){return t(this,s)!==-1}hasSomethingToRedo(){return t(this,s)<t(this,A).length-1}destroy(){et(this,A,null)}}A=new WeakMap,r=new WeakMap,l=new WeakMap,s=new WeakMap,d.CommandManager=R;class g{constructor(D){Q(this,a);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:ft}=c.FeatureTest.platform;for(const[Ct,Dt,Ot={}]of D)for(const Pt of Ct){const w=Pt.startsWith("mac+");ft&&w?(this.callbacks.set(Pt.slice(4),{callback:Dt,options:Ot}),this.allKeys.add(Pt.split("+").at(-1))):!ft&&!w&&(this.callbacks.set(Pt,{callback:Dt,options:Ot}),this.allKeys.add(Pt.split("+").at(-1)))}}exec(D,ft){if(!this.allKeys.has(ft.key))return;const Ct=this.callbacks.get(z(this,a,yi).call(this,ft));if(!Ct)return;const{callback:Dt,options:{bubbles:Ot=!1,args:Pt=[],checker:w=null}}=Ct;w&&!w(D,ft)||(Dt.bind(D,...Pt)(),Ot||(ft.stopPropagation(),ft.preventDefault()))}}a=new WeakSet,yi=function(D){D.altKey&&this.buffer.push("alt"),D.ctrlKey&&this.buffer.push("ctrl"),D.metaKey&&this.buffer.push("meta"),D.shiftKey&&this.buffer.push("shift"),this.buffer.push(D.key);const ft=this.buffer.join("+");return this.buffer.length=0,ft},d.KeyboardManager=g;const L=class L{get _colors(){const D=new Map([["CanvasText",null],["Canvas",null]]);return(0,x.getColorValues)(D),(0,c.shadow)(this,"_colors",D)}convert(D){const ft=(0,x.getRGB)(D);if(!window.matchMedia("(forced-colors: active)").matches)return ft;for(const[Ct,Dt]of this._colors)if(Dt.every((Ot,Pt)=>Ot===ft[Pt]))return L._colorsMapping.get(Ct);return ft}getHexCode(D){const ft=this._colors.get(D);return ft?c.Util.makeHexColor(...ft):D}};Kt(L,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let N=L;d.ColorManager=N;const zt=class zt{constructor(D,ft,Ct,Dt,Ot,Pt){Q(this,$);Q(this,n,null);Q(this,_,new Map);Q(this,F,new Map);Q(this,nt,null);Q(this,W,null);Q(this,q,new R);Q(this,j,0);Q(this,rt,new Set);Q(this,C,null);Q(this,U,null);Q(this,Y,new Set);Q(this,S,null);Q(this,e,new mt);Q(this,i,!1);Q(this,u,!1);Q(this,T,null);Q(this,P,c.AnnotationEditorType.NONE);Q(this,k,new Set);Q(this,G,null);Q(this,it,this.blur.bind(this));Q(this,ht,this.focus.bind(this));Q(this,dt,this.copy.bind(this));Q(this,pt,this.cut.bind(this));Q(this,_t,this.paste.bind(this));Q(this,yt,this.keydown.bind(this));Q(this,K,this.onEditingAction.bind(this));Q(this,Z,this.onPageChanging.bind(this));Q(this,p,this.onScaleChanging.bind(this));Q(this,M,this.onRotationChanging.bind(this));Q(this,X,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1});Q(this,J,[0,0]);Q(this,ut,null);Q(this,vt,null);Q(this,At,null);et(this,vt,D),et(this,At,ft),et(this,nt,Ct),this._eventBus=Dt,this._eventBus._on("editingaction",t(this,K)),this._eventBus._on("pagechanging",t(this,Z)),this._eventBus._on("scalechanging",t(this,p)),this._eventBus._on("rotationchanging",t(this,M)),et(this,W,Ot.annotationStorage),et(this,S,Ot.filterFactory),et(this,G,Pt),this.viewParameters={realScale:x.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0}}static get _keyboardManager(){const D=zt.prototype,ft=Ot=>{const{activeElement:Pt}=document;return Pt&&t(Ot,vt).contains(Pt)&&Ot.hasSomethingToControl()},Ct=this.TRANSLATE_SMALL,Dt=this.TRANSLATE_BIG;return(0,c.shadow)(this,"_keyboardManager",new g([[["ctrl+a","mac+meta+a"],D.selectAll],[["ctrl+z","mac+meta+z"],D.undo],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],D.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],D.delete],[["Escape","mac+Escape"],D.unselectAll],[["ArrowLeft","mac+ArrowLeft"],D.translateSelectedEditors,{args:[-Ct,0],checker:ft}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],D.translateSelectedEditors,{args:[-Dt,0],checker:ft}],[["ArrowRight","mac+ArrowRight"],D.translateSelectedEditors,{args:[Ct,0],checker:ft}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],D.translateSelectedEditors,{args:[Dt,0],checker:ft}],[["ArrowUp","mac+ArrowUp"],D.translateSelectedEditors,{args:[0,-Ct],checker:ft}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],D.translateSelectedEditors,{args:[0,-Dt],checker:ft}],[["ArrowDown","mac+ArrowDown"],D.translateSelectedEditors,{args:[0,Ct],checker:ft}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],D.translateSelectedEditors,{args:[0,Dt],checker:ft}]]))}destroy(){z(this,$,ye).call(this),z(this,$,je).call(this),this._eventBus._off("editingaction",t(this,K)),this._eventBus._off("pagechanging",t(this,Z)),this._eventBus._off("scalechanging",t(this,p)),this._eventBus._off("rotationchanging",t(this,M));for(const D of t(this,F).values())D.destroy();t(this,F).clear(),t(this,_).clear(),t(this,Y).clear(),et(this,n,null),t(this,k).clear(),t(this,q).destroy(),t(this,nt).destroy()}get hcmFilter(){return(0,c.shadow)(this,"hcmFilter",t(this,G)?t(this,S).addHCMFilter(t(this,G).foreground,t(this,G).background):"none")}get direction(){return(0,c.shadow)(this,"direction",getComputedStyle(t(this,vt)).direction)}editAltText(D){var ft;(ft=t(this,nt))==null||ft.editAltText(this,D)}onPageChanging({pageNumber:D}){et(this,j,D-1)}focusMainContainer(){t(this,vt).focus()}findParent(D,ft){for(const Ct of t(this,F).values()){const{x:Dt,y:Ot,width:Pt,height:w}=Ct.div.getBoundingClientRect();if(D>=Dt&&D<=Dt+Pt&&ft>=Ot&&ft<=Ot+w)return Ct}return null}disableUserSelect(D=!1){t(this,At).classList.toggle("noUserSelect",D)}addShouldRescale(D){t(this,Y).add(D)}removeShouldRescale(D){t(this,Y).delete(D)}onScaleChanging({scale:D}){this.commitOrRemove(),this.viewParameters.realScale=D*x.PixelsPerInch.PDF_TO_CSS_UNITS;for(const ft of t(this,Y))ft.onScaleChanging()}onRotationChanging({pagesRotation:D}){this.commitOrRemove(),this.viewParameters.rotation=D}addToAnnotationStorage(D){!D.isEmpty()&&t(this,W)&&!t(this,W).has(D.id)&&t(this,W).setValue(D.id,D)}blur(){if(!this.hasSelection)return;const{activeElement:D}=document;for(const ft of t(this,k))if(ft.div.contains(D)){et(this,T,[ft,D]),ft._focusEventsAllowed=!1;break}}focus(){if(!t(this,T))return;const[D,ft]=t(this,T);et(this,T,null),ft.addEventListener("focusin",()=>{D._focusEventsAllowed=!0},{once:!0}),ft.focus()}addEditListeners(){z(this,$,He).call(this),z(this,$,We).call(this)}removeEditListeners(){z(this,$,ye).call(this),z(this,$,Ge).call(this)}copy(D){var Ct;if(D.preventDefault(),(Ct=t(this,n))==null||Ct.commitOrRemove(),!this.hasSelection)return;const ft=[];for(const Dt of t(this,k)){const Ot=Dt.serialize(!0);Ot&&ft.push(Ot)}ft.length!==0&&D.clipboardData.setData("application/pdfjs",JSON.stringify(ft))}cut(D){this.copy(D),this.delete()}paste(D){D.preventDefault();const{clipboardData:ft}=D;for(const Ot of ft.items)for(const Pt of t(this,U))if(Pt.isHandlingMimeForPasting(Ot.type)){Pt.paste(Ot,this.currentLayer);return}let Ct=ft.getData("application/pdfjs");if(!Ct)return;try{Ct=JSON.parse(Ct)}catch(Ot){(0,c.warn)(`paste: "${Ot.message}".`);return}if(!Array.isArray(Ct))return;this.unselectAll();const Dt=this.currentLayer;try{const Ot=[];for(const y of Ct){const H=Dt.deserialize(y);if(!H)return;Ot.push(H)}const Pt=()=>{for(const y of Ot)z(this,$,ze).call(this,y);z(this,$,Xe).call(this,Ot)},w=()=>{for(const y of Ot)y.remove()};this.addCommands({cmd:Pt,undo:w,mustExec:!0})}catch(Ot){(0,c.warn)(`paste: "${Ot.message}".`)}}keydown(D){var ft;(ft=this.getActive())!=null&&ft.shouldGetKeyboardEvents()||zt._keyboardManager.exec(this,D)}onEditingAction(D){["undo","redo","delete","selectAll"].includes(D.name)&&this[D.name]()}setEditingState(D){D?(z(this,$,vi).call(this),z(this,$,He).call(this),z(this,$,We).call(this),z(this,$,Zt).call(this,{isEditing:t(this,P)!==c.AnnotationEditorType.NONE,isEmpty:z(this,$,ue).call(this),hasSomethingToUndo:t(this,q).hasSomethingToUndo(),hasSomethingToRedo:t(this,q).hasSomethingToRedo(),hasSelectedEditor:!1})):(z(this,$,je).call(this),z(this,$,ye).call(this),z(this,$,Ge).call(this),z(this,$,Zt).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(D){if(!t(this,U)){et(this,U,D);for(const ft of t(this,U))z(this,$,de).call(this,ft.defaultPropertiesToUpdate)}}getId(){return t(this,e).getId()}get currentLayer(){return t(this,F).get(t(this,j))}getLayer(D){return t(this,F).get(D)}get currentPageIndex(){return t(this,j)}addLayer(D){t(this,F).set(D.pageIndex,D),t(this,i)?D.enable():D.disable()}removeLayer(D){t(this,F).delete(D.pageIndex)}updateMode(D,ft=null){if(t(this,P)!==D){if(et(this,P,D),D===c.AnnotationEditorType.NONE){this.setEditingState(!1),z(this,$,Ei).call(this);return}this.setEditingState(!0),z(this,$,Si).call(this),this.unselectAll();for(const Ct of t(this,F).values())Ct.updateMode(D);if(ft){for(const Ct of t(this,_).values())if(Ct.annotationElementId===ft){this.setSelected(Ct),Ct.enterInEditMode();break}}}}updateToolbar(D){D!==t(this,P)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:D})}updateParams(D,ft){if(t(this,U)){if(D===c.AnnotationEditorParamsType.CREATE){this.currentLayer.addNewEditor(D);return}for(const Ct of t(this,k))Ct.updateParams(D,ft);for(const Ct of t(this,U))Ct.updateDefaultParams(D,ft)}}enableWaiting(D=!1){if(t(this,u)!==D){et(this,u,D);for(const ft of t(this,F).values())D?ft.disableClick():ft.enableClick(),ft.div.classList.toggle("waiting",D)}}getEditors(D){const ft=[];for(const Ct of t(this,_).values())Ct.pageIndex===D&&ft.push(Ct);return ft}getEditor(D){return t(this,_).get(D)}addEditor(D){t(this,_).set(D.id,D)}removeEditor(D){var ft;t(this,_).delete(D.id),this.unselect(D),(!D.annotationElementId||!t(this,rt).has(D.annotationElementId))&&((ft=t(this,W))==null||ft.remove(D.id))}addDeletedAnnotationElement(D){t(this,rt).add(D.annotationElementId),D.deleted=!0}isDeletedAnnotationElement(D){return t(this,rt).has(D)}removeDeletedAnnotationElement(D){t(this,rt).delete(D.annotationElementId),D.deleted=!1}setActiveEditor(D){t(this,n)!==D&&(et(this,n,D),D&&z(this,$,de).call(this,D.propertiesToUpdate))}toggleSelected(D){if(t(this,k).has(D)){t(this,k).delete(D),D.unselect(),z(this,$,Zt).call(this,{hasSelectedEditor:this.hasSelection});return}t(this,k).add(D),D.select(),z(this,$,de).call(this,D.propertiesToUpdate),z(this,$,Zt).call(this,{hasSelectedEditor:!0})}setSelected(D){for(const ft of t(this,k))ft!==D&&ft.unselect();t(this,k).clear(),t(this,k).add(D),D.select(),z(this,$,de).call(this,D.propertiesToUpdate),z(this,$,Zt).call(this,{hasSelectedEditor:!0})}isSelected(D){return t(this,k).has(D)}unselect(D){D.unselect(),t(this,k).delete(D),z(this,$,Zt).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return t(this,k).size!==0}undo(){t(this,q).undo(),z(this,$,Zt).call(this,{hasSomethingToUndo:t(this,q).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:z(this,$,ue).call(this)})}redo(){t(this,q).redo(),z(this,$,Zt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:t(this,q).hasSomethingToRedo(),isEmpty:z(this,$,ue).call(this)})}addCommands(D){t(this,q).add(D),z(this,$,Zt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:z(this,$,ue).call(this)})}delete(){if(this.commitOrRemove(),!this.hasSelection)return;const D=[...t(this,k)],ft=()=>{for(const Dt of D)Dt.remove()},Ct=()=>{for(const Dt of D)z(this,$,ze).call(this,Dt)};this.addCommands({cmd:ft,undo:Ct,mustExec:!0})}commitOrRemove(){var D;(D=t(this,n))==null||D.commitOrRemove()}hasSomethingToControl(){return t(this,n)||this.hasSelection}selectAll(){for(const D of t(this,k))D.commit();z(this,$,Xe).call(this,t(this,_).values())}unselectAll(){if(t(this,n)){t(this,n).commitOrRemove();return}if(this.hasSelection){for(const D of t(this,k))D.unselect();t(this,k).clear(),z(this,$,Zt).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(D,ft,Ct=!1){if(Ct||this.commitOrRemove(),!this.hasSelection)return;t(this,J)[0]+=D,t(this,J)[1]+=ft;const[Dt,Ot]=t(this,J),Pt=[...t(this,k)],w=1e3;t(this,ut)&&clearTimeout(t(this,ut)),et(this,ut,setTimeout(()=>{et(this,ut,null),t(this,J)[0]=t(this,J)[1]=0,this.addCommands({cmd:()=>{for(const y of Pt)t(this,_).has(y.id)&&y.translateInPage(Dt,Ot)},undo:()=>{for(const y of Pt)t(this,_).has(y.id)&&y.translateInPage(-Dt,-Ot)},mustExec:!1})},w));for(const y of Pt)y.translateInPage(D,ft)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),et(this,C,new Map);for(const D of t(this,k))t(this,C).set(D,{savedX:D.x,savedY:D.y,savedPageIndex:D.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!t(this,C))return!1;this.disableUserSelect(!1);const D=t(this,C);et(this,C,null);let ft=!1;for(const[{x:Dt,y:Ot,pageIndex:Pt},w]of D)w.newX=Dt,w.newY=Ot,w.newPageIndex=Pt,ft||(ft=Dt!==w.savedX||Ot!==w.savedY||Pt!==w.savedPageIndex);if(!ft)return!1;const Ct=(Dt,Ot,Pt,w)=>{if(t(this,_).has(Dt.id)){const y=t(this,F).get(w);y?Dt._setParentAndPosition(y,Ot,Pt):(Dt.pageIndex=w,Dt.x=Ot,Dt.y=Pt)}};return this.addCommands({cmd:()=>{for(const[Dt,{newX:Ot,newY:Pt,newPageIndex:w}]of D)Ct(Dt,Ot,Pt,w)},undo:()=>{for(const[Dt,{savedX:Ot,savedY:Pt,savedPageIndex:w}]of D)Ct(Dt,Ot,Pt,w)},mustExec:!0}),!0}dragSelectedEditors(D,ft){if(t(this,C))for(const Ct of t(this,C).keys())Ct.drag(D,ft)}rebuild(D){if(D.parent===null){const ft=this.getLayer(D.pageIndex);ft?(ft.changeParent(D),ft.addOrRebuild(D)):(this.addEditor(D),this.addToAnnotationStorage(D),D.rebuild())}else D.parent.addOrRebuild(D)}isActive(D){return t(this,n)===D}getActive(){return t(this,n)}getMode(){return t(this,P)}get imageManager(){return(0,c.shadow)(this,"imageManager",new B)}};n=new WeakMap,_=new WeakMap,F=new WeakMap,nt=new WeakMap,W=new WeakMap,q=new WeakMap,j=new WeakMap,rt=new WeakMap,C=new WeakMap,U=new WeakMap,Y=new WeakMap,S=new WeakMap,e=new WeakMap,i=new WeakMap,u=new WeakMap,T=new WeakMap,P=new WeakMap,k=new WeakMap,G=new WeakMap,it=new WeakMap,ht=new WeakMap,dt=new WeakMap,pt=new WeakMap,_t=new WeakMap,yt=new WeakMap,K=new WeakMap,Z=new WeakMap,p=new WeakMap,M=new WeakMap,X=new WeakMap,J=new WeakMap,ut=new WeakMap,vt=new WeakMap,At=new WeakMap,$=new WeakSet,vi=function(){window.addEventListener("focus",t(this,ht)),window.addEventListener("blur",t(this,it))},je=function(){window.removeEventListener("focus",t(this,ht)),window.removeEventListener("blur",t(this,it))},He=function(){window.addEventListener("keydown",t(this,yt),{capture:!0})},ye=function(){window.removeEventListener("keydown",t(this,yt),{capture:!0})},We=function(){document.addEventListener("copy",t(this,dt)),document.addEventListener("cut",t(this,pt)),document.addEventListener("paste",t(this,_t))},Ge=function(){document.removeEventListener("copy",t(this,dt)),document.removeEventListener("cut",t(this,pt)),document.removeEventListener("paste",t(this,_t))},Zt=function(D){Object.entries(D).some(([Ct,Dt])=>t(this,X)[Ct]!==Dt)&&this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(t(this,X),D)})},de=function(D){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:D})},Si=function(){if(!t(this,i)){et(this,i,!0);for(const D of t(this,F).values())D.enable()}},Ei=function(){if(this.unselectAll(),t(this,i)){et(this,i,!1);for(const D of t(this,F).values())D.disable()}},ze=function(D){const ft=t(this,F).get(D.pageIndex);ft?ft.addOrRebuild(D):this.addEditor(D)},ue=function(){if(t(this,_).size===0)return!0;if(t(this,_).size===1)for(const D of t(this,_).values())return D.isEmpty();return!1},Xe=function(D){t(this,k).clear();for(const ft of D)ft.isEmpty()||(t(this,k).add(ft),ft.select());z(this,$,Zt).call(this,{hasSelectedEditor:!0})},Kt(zt,"TRANSLATE_SMALL",1),Kt(zt,"TRANSLATE_BIG",10);let O=zt;d.AnnotationEditorUIManager=O},(St,d,lt)=>{var j,rt,C,U,Y,S,e,i,u,T,P,k,ae,oe,$e,ve,Se,fe,pe;Object.defineProperty(d,"__esModule",{value:!0}),d.StatTimer=d.RenderingCancelledException=d.PixelsPerInch=d.PageViewport=d.PDFDateString=d.DOMStandardFontDataFactory=d.DOMSVGFactory=d.DOMFilterFactory=d.DOMCanvasFactory=d.DOMCMapReaderFactory=void 0,d.deprecated=s,d.getColorValues=_,d.getCurrentTransform=F,d.getCurrentTransformInverse=nt,d.getFilenameFromUrl=h,d.getPdfFilenameFromUrl=m,d.getRGB=n,d.getXfaPageViewport=L,d.isDataScheme=E,d.isPdfFile=f,d.isValidFetchUrl=A,d.loadScript=l,d.noContextMenu=r,d.setLayerDimensions=W;var c=lt(7),x=lt(1);const ct="http://www.w3.org/2000/svg",q=class q{};Kt(q,"CSS",96),Kt(q,"PDF",72),Kt(q,"PDF_TO_CSS_UNITS",q.CSS/q.PDF);let V=q;d.PixelsPerInch=V;class mt extends c.BaseFilterFactory{constructor({docId:p,ownerDocument:M=globalThis.document}={}){super();Q(this,k);Q(this,j);Q(this,rt);Q(this,C);Q(this,U);Q(this,Y);Q(this,S);Q(this,e);Q(this,i);Q(this,u);Q(this,T);Q(this,P,0);et(this,C,p),et(this,U,M)}addFilter(p){if(!p)return"none";let M=t(this,k,ae).get(p);if(M)return M;let X,J,ut,vt;if(p.length===1){const wt=p[0],jt=new Array(256);for(let Bt=0;Bt<256;Bt++)jt[Bt]=wt[Bt]/255;vt=X=J=ut=jt.join(",")}else{const[wt,jt,Bt]=p,Xt=new Array(256),Ft=new Array(256),Nt=new Array(256);for(let $t=0;$t<256;$t++)Xt[$t]=wt[$t]/255,Ft[$t]=jt[$t]/255,Nt[$t]=Bt[$t]/255;X=Xt.join(","),J=Ft.join(","),ut=Nt.join(","),vt=`${X}${J}${ut}`}if(M=t(this,k,ae).get(vt),M)return t(this,k,ae).set(p,M),M;const At=`g_${t(this,C)}_transfer_map_${he(this,P)._++}`,$=`url(#${At})`;t(this,k,ae).set(p,$),t(this,k,ae).set(vt,$);const Tt=z(this,k,ve).call(this,At);return z(this,k,fe).call(this,X,J,ut,Tt),$}addHCMFilter(p,M){var jt;const X=`${p}-${M}`;if(t(this,S)===X)return t(this,e);if(et(this,S,X),et(this,e,"none"),(jt=t(this,Y))==null||jt.remove(),!p||!M)return t(this,e);const J=z(this,k,pe).call(this,p);p=x.Util.makeHexColor(...J);const ut=z(this,k,pe).call(this,M);if(M=x.Util.makeHexColor(...ut),t(this,k,oe).style.color="",p==="#000000"&&M==="#ffffff"||p===M)return t(this,e);const vt=new Array(256);for(let Bt=0;Bt<=255;Bt++){const Xt=Bt/255;vt[Bt]=Xt<=.03928?Xt/12.92:((Xt+.055)/1.055)**2.4}const At=vt.join(","),$=`g_${t(this,C)}_hcm_filter`,Tt=et(this,i,z(this,k,ve).call(this,$));z(this,k,fe).call(this,At,At,At,Tt),z(this,k,$e).call(this,Tt);const wt=(Bt,Xt)=>{const Ft=J[Bt]/255,Nt=ut[Bt]/255,$t=new Array(Xt+1);for(let qt=0;qt<=Xt;qt++)$t[qt]=Ft+qt/Xt*(Nt-Ft);return $t.join(",")};return z(this,k,fe).call(this,wt(0,5),wt(1,5),wt(2,5),Tt),et(this,e,`url(#${$})`),t(this,e)}addHighlightHCMFilter(p,M,X,J){var Nt;const ut=`${p}-${M}-${X}-${J}`;if(t(this,u)===ut)return t(this,T);if(et(this,u,ut),et(this,T,"none"),(Nt=t(this,i))==null||Nt.remove(),!p||!M)return t(this,T);const[vt,At]=[p,M].map(z(this,k,pe).bind(this));let $=Math.round(.2126*vt[0]+.7152*vt[1]+.0722*vt[2]),Tt=Math.round(.2126*At[0]+.7152*At[1]+.0722*At[2]),[wt,jt]=[X,J].map(z(this,k,pe).bind(this));Tt<$&&([$,Tt,wt,jt]=[Tt,$,jt,wt]),t(this,k,oe).style.color="";const Bt=($t,qt,bt)=>{const tt=new Array(256),st=(Tt-$)/bt,kt=$t/255,zt=(qt-$t)/(255*bt);let Gt=0;for(let D=0;D<=bt;D++){const ft=Math.round($+D*st),Ct=kt+D*zt;for(let Dt=Gt;Dt<=ft;Dt++)tt[Dt]=Ct;Gt=ft+1}for(let D=Gt;D<256;D++)tt[D]=tt[Gt-1];return tt.join(",")},Xt=`g_${t(this,C)}_hcm_highlight_filter`,Ft=et(this,i,z(this,k,ve).call(this,Xt));return z(this,k,$e).call(this,Ft),z(this,k,fe).call(this,Bt(wt[0],jt[0],5),Bt(wt[1],jt[1],5),Bt(wt[2],jt[2],5),Ft),et(this,T,`url(#${Xt})`),t(this,T)}destroy(p=!1){p&&(t(this,e)||t(this,T))||(t(this,rt)&&(t(this,rt).parentNode.parentNode.remove(),et(this,rt,null)),t(this,j)&&(t(this,j).clear(),et(this,j,null)),et(this,P,0))}}j=new WeakMap,rt=new WeakMap,C=new WeakMap,U=new WeakMap,Y=new WeakMap,S=new WeakMap,e=new WeakMap,i=new WeakMap,u=new WeakMap,T=new WeakMap,P=new WeakMap,k=new WeakSet,ae=function(){return t(this,j)||et(this,j,new Map)},oe=function(){if(!t(this,rt)){const p=t(this,U).createElement("div"),{style:M}=p;M.visibility="hidden",M.contain="strict",M.width=M.height=0,M.position="absolute",M.top=M.left=0,M.zIndex=-1;const X=t(this,U).createElementNS(ct,"svg");X.setAttribute("width",0),X.setAttribute("height",0),et(this,rt,t(this,U).createElementNS(ct,"defs")),p.append(X),X.append(t(this,rt)),t(this,U).body.append(p)}return t(this,rt)},$e=function(p){const M=t(this,U).createElementNS(ct,"feColorMatrix");M.setAttribute("type","matrix"),M.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),p.append(M)},ve=function(p){const M=t(this,U).createElementNS(ct,"filter");return M.setAttribute("color-interpolation-filters","sRGB"),M.setAttribute("id",p),t(this,k,oe).append(M),M},Se=function(p,M,X){const J=t(this,U).createElementNS(ct,M);J.setAttribute("type","discrete"),J.setAttribute("tableValues",X),p.append(J)},fe=function(p,M,X,J){const ut=t(this,U).createElementNS(ct,"feComponentTransfer");J.append(ut),z(this,k,Se).call(this,ut,"feFuncR",p),z(this,k,Se).call(this,ut,"feFuncG",M),z(this,k,Se).call(this,ut,"feFuncB",X)},pe=function(p){return t(this,k,oe).style.color=p,n(getComputedStyle(t(this,k,oe)).getPropertyValue("color"))},d.DOMFilterFactory=mt;class B extends c.BaseCanvasFactory{constructor({ownerDocument:Z=globalThis.document}={}){super(),this._document=Z}_createCanvas(Z,p){const M=this._document.createElement("canvas");return M.width=Z,M.height=p,M}}d.DOMCanvasFactory=B;async function R(K,Z=!1){if(A(K,document.baseURI)){const p=await fetch(K);if(!p.ok)throw new Error(p.statusText);return Z?new Uint8Array(await p.arrayBuffer()):(0,x.stringToBytes)(await p.text())}return new Promise((p,M)=>{const X=new XMLHttpRequest;X.open("GET",K,!0),Z&&(X.responseType="arraybuffer"),X.onreadystatechange=()=>{if(X.readyState===XMLHttpRequest.DONE){if(X.status===200||X.status===0){let J;if(Z&&X.response?J=new Uint8Array(X.response):!Z&&X.responseText&&(J=(0,x.stringToBytes)(X.responseText)),J){p(J);return}}M(new Error(X.statusText))}},X.send(null)})}class g extends c.BaseCMapReaderFactory{_fetchData(Z,p){return R(Z,this.isCompressed).then(M=>({cMapData:M,compressionType:p}))}}d.DOMCMapReaderFactory=g;class N extends c.BaseStandardFontDataFactory{_fetchData(Z){return R(Z,!0)}}d.DOMStandardFontDataFactory=N;class O extends c.BaseSVGFactory{_createSVG(Z){return document.createElementNS(ct,Z)}}d.DOMSVGFactory=O;class v{constructor({viewBox:Z,scale:p,rotation:M,offsetX:X=0,offsetY:J=0,dontFlip:ut=!1}){this.viewBox=Z,this.scale=p,this.rotation=M,this.offsetX=X,this.offsetY=J;const vt=(Z[2]+Z[0])/2,At=(Z[3]+Z[1])/2;let $,Tt,wt,jt;switch(M%=360,M<0&&(M+=360),M){case 180:$=-1,Tt=0,wt=0,jt=1;break;case 90:$=0,Tt=1,wt=1,jt=0;break;case 270:$=0,Tt=-1,wt=-1,jt=0;break;case 0:$=1,Tt=0,wt=0,jt=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}ut&&(wt=-wt,jt=-jt);let Bt,Xt,Ft,Nt;$===0?(Bt=Math.abs(At-Z[1])*p+X,Xt=Math.abs(vt-Z[0])*p+J,Ft=(Z[3]-Z[1])*p,Nt=(Z[2]-Z[0])*p):(Bt=Math.abs(vt-Z[0])*p+X,Xt=Math.abs(At-Z[1])*p+J,Ft=(Z[2]-Z[0])*p,Nt=(Z[3]-Z[1])*p),this.transform=[$*p,Tt*p,wt*p,jt*p,Bt-$*p*vt-wt*p*At,Xt-Tt*p*vt-jt*p*At],this.width=Ft,this.height=Nt}get rawDims(){const{viewBox:Z}=this;return(0,x.shadow)(this,"rawDims",{pageWidth:Z[2]-Z[0],pageHeight:Z[3]-Z[1],pageX:Z[0],pageY:Z[1]})}clone({scale:Z=this.scale,rotation:p=this.rotation,offsetX:M=this.offsetX,offsetY:X=this.offsetY,dontFlip:J=!1}={}){return new v({viewBox:this.viewBox.slice(),scale:Z,rotation:p,offsetX:M,offsetY:X,dontFlip:J})}convertToViewportPoint(Z,p){return x.Util.applyTransform([Z,p],this.transform)}convertToViewportRectangle(Z){const p=x.Util.applyTransform([Z[0],Z[1]],this.transform),M=x.Util.applyTransform([Z[2],Z[3]],this.transform);return[p[0],p[1],M[0],M[1]]}convertToPdfPoint(Z,p){return x.Util.applyInverseTransform([Z,p],this.transform)}}d.PageViewport=v;class b extends x.BaseException{constructor(Z,p=0){super(Z,"RenderingCancelledException"),this.extraDelay=p}}d.RenderingCancelledException=b;function E(K){const Z=K.length;let p=0;for(;p<Z&&K[p].trim()==="";)p++;return K.substring(p,p+5).toLowerCase()==="data:"}function f(K){return typeof K=="string"&&/\.pdf$/i.test(K)}function h(K,Z=!1){return Z||([K]=K.split(/[#?]/,1)),K.substring(K.lastIndexOf("/")+1)}function m(K,Z="document.pdf"){if(typeof K!="string")return Z;if(E(K))return(0,x.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),Z;const p=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,M=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,X=p.exec(K);let J=M.exec(X[1])||M.exec(X[2])||M.exec(X[3]);if(J&&(J=J[0],J.includes("%")))try{J=M.exec(decodeURIComponent(J))[0]}catch{}return J||Z}class I{constructor(){Kt(this,"started",Object.create(null));Kt(this,"times",[])}time(Z){Z in this.started&&(0,x.warn)(`Timer is already running for ${Z}`),this.started[Z]=Date.now()}timeEnd(Z){Z in this.started||(0,x.warn)(`Timer has not been started for ${Z}`),this.times.push({name:Z,start:this.started[Z],end:Date.now()}),delete this.started[Z]}toString(){const Z=[];let p=0;for(const{name:M}of this.times)p=Math.max(M.length,p);for(const{name:M,start:X,end:J}of this.times)Z.push(`${M.padEnd(p)} ${J-X}ms
`);return Z.join("")}}d.StatTimer=I;function A(K,Z){try{const{protocol:p}=Z?new URL(K,Z):new URL(K);return p==="http:"||p==="https:"}catch{return!1}}function r(K){K.preventDefault()}function l(K,Z=!1){return new Promise((p,M)=>{const X=document.createElement("script");X.src=K,X.onload=function(J){Z&&X.remove(),p(J)},X.onerror=function(){M(new Error(`Cannot load script at: ${X.src}`))},(document.head||document.documentElement).append(X)})}function s(K){console.log("Deprecated API usage: "+K)}let a;class o{static toDateObject(Z){if(!Z||typeof Z!="string")return null;a||(a=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const p=a.exec(Z);if(!p)return null;const M=parseInt(p[1],10);let X=parseInt(p[2],10);X=X>=1&&X<=12?X-1:0;let J=parseInt(p[3],10);J=J>=1&&J<=31?J:1;let ut=parseInt(p[4],10);ut=ut>=0&&ut<=23?ut:0;let vt=parseInt(p[5],10);vt=vt>=0&&vt<=59?vt:0;let At=parseInt(p[6],10);At=At>=0&&At<=59?At:0;const $=p[7]||"Z";let Tt=parseInt(p[8],10);Tt=Tt>=0&&Tt<=23?Tt:0;let wt=parseInt(p[9],10)||0;return wt=wt>=0&&wt<=59?wt:0,$==="-"?(ut+=Tt,vt+=wt):$==="+"&&(ut-=Tt,vt-=wt),new Date(Date.UTC(M,X,J,ut,vt,At))}}d.PDFDateString=o;function L(K,{scale:Z=1,rotation:p=0}){const{width:M,height:X}=K.attributes.style,J=[0,0,parseInt(M),parseInt(X)];return new v({viewBox:J,scale:Z,rotation:p})}function n(K){if(K.startsWith("#")){const Z=parseInt(K.slice(1),16);return[(Z&16711680)>>16,(Z&65280)>>8,Z&255]}return K.startsWith("rgb(")?K.slice(4,-1).split(",").map(Z=>parseInt(Z)):K.startsWith("rgba(")?K.slice(5,-1).split(",").map(Z=>parseInt(Z)).slice(0,3):((0,x.warn)(`Not a valid color format: "${K}"`),[0,0,0])}function _(K){const Z=document.createElement("span");Z.style.visibility="hidden",document.body.append(Z);for(const p of K.keys()){Z.style.color=p;const M=window.getComputedStyle(Z).color;K.set(p,n(M))}Z.remove()}function F(K){const{a:Z,b:p,c:M,d:X,e:J,f:ut}=K.getTransform();return[Z,p,M,X,J,ut]}function nt(K){const{a:Z,b:p,c:M,d:X,e:J,f:ut}=K.getTransform().invertSelf();return[Z,p,M,X,J,ut]}function W(K,Z,p=!1,M=!0){if(Z instanceof v){const{pageWidth:X,pageHeight:J}=Z.rawDims,{style:ut}=K,vt=x.FeatureTest.isCSSRoundSupported,At=`var(--scale-factor) * ${X}px`,$=`var(--scale-factor) * ${J}px`,Tt=vt?`round(${At}, 1px)`:`calc(${At})`,wt=vt?`round(${$}, 1px)`:`calc(${$})`;!p||Z.rotation%180===0?(ut.width=Tt,ut.height=wt):(ut.width=wt,ut.height=Tt)}M&&K.setAttribute("data-main-rotation",Z.rotation)}},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.BaseStandardFontDataFactory=d.BaseSVGFactory=d.BaseFilterFactory=d.BaseCanvasFactory=d.BaseCMapReaderFactory=void 0;var c=lt(1);class x{constructor(){this.constructor===x&&(0,c.unreachable)("Cannot initialize BaseFilterFactory.")}addFilter(g){return"none"}addHCMFilter(g,N){return"none"}addHighlightHCMFilter(g,N,O,v){return"none"}destroy(g=!1){}}d.BaseFilterFactory=x;class ct{constructor(){this.constructor===ct&&(0,c.unreachable)("Cannot initialize BaseCanvasFactory.")}create(g,N){if(g<=0||N<=0)throw new Error("Invalid canvas size");const O=this._createCanvas(g,N);return{canvas:O,context:O.getContext("2d")}}reset(g,N,O){if(!g.canvas)throw new Error("Canvas is not specified");if(N<=0||O<=0)throw new Error("Invalid canvas size");g.canvas.width=N,g.canvas.height=O}destroy(g){if(!g.canvas)throw new Error("Canvas is not specified");g.canvas.width=0,g.canvas.height=0,g.canvas=null,g.context=null}_createCanvas(g,N){(0,c.unreachable)("Abstract method `_createCanvas` called.")}}d.BaseCanvasFactory=ct;class V{constructor({baseUrl:g=null,isCompressed:N=!0}){this.constructor===V&&(0,c.unreachable)("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=g,this.isCompressed=N}async fetch({name:g}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!g)throw new Error("CMap name must be specified.");const N=this.baseUrl+g+(this.isCompressed?".bcmap":""),O=this.isCompressed?c.CMapCompressionType.BINARY:c.CMapCompressionType.NONE;return this._fetchData(N,O).catch(v=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${N}`)})}_fetchData(g,N){(0,c.unreachable)("Abstract method `_fetchData` called.")}}d.BaseCMapReaderFactory=V;class mt{constructor({baseUrl:g=null}){this.constructor===mt&&(0,c.unreachable)("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=g}async fetch({filename:g}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!g)throw new Error("Font filename must be specified.");const N=`${this.baseUrl}${g}`;return this._fetchData(N).catch(O=>{throw new Error(`Unable to load font data at: ${N}`)})}_fetchData(g){(0,c.unreachable)("Abstract method `_fetchData` called.")}}d.BaseStandardFontDataFactory=mt;class B{constructor(){this.constructor===B&&(0,c.unreachable)("Cannot initialize BaseSVGFactory.")}create(g,N,O=!1){if(g<=0||N<=0)throw new Error("Invalid SVG dimensions");const v=this._createSVG("svg:svg");return v.setAttribute("version","1.1"),O||(v.setAttribute("width",`${g}px`),v.setAttribute("height",`${N}px`)),v.setAttribute("preserveAspectRatio","none"),v.setAttribute("viewBox",`0 0 ${g} ${N}`),v}createElement(g){if(typeof g!="string")throw new Error("Invalid SVG element type");return this._createSVG(g)}_createSVG(g){(0,c.unreachable)("Abstract method `_createSVG` called.")}}d.BaseSVGFactory=B},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.MurmurHash3_64=void 0;var c=lt(1);const x=3285377520,ct=4294901760,V=65535;class mt{constructor(R){this.h1=R?R&4294967295:x,this.h2=R?R&4294967295:x}update(R){let g,N;if(typeof R=="string"){g=new Uint8Array(R.length*2),N=0;for(let s=0,a=R.length;s<a;s++){const o=R.charCodeAt(s);o<=255?g[N++]=o:(g[N++]=o>>>8,g[N++]=o&255)}}else if((0,c.isArrayBuffer)(R))g=R.slice(),N=g.byteLength;else throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");const O=N>>2,v=N-O*4,b=new Uint32Array(g.buffer,0,O);let E=0,f=0,h=this.h1,m=this.h2;const I=3432918353,A=461845907,r=I&V,l=A&V;for(let s=0;s<O;s++)s&1?(E=b[s],E=E*I&ct|E*r&V,E=E<<15|E>>>17,E=E*A&ct|E*l&V,h^=E,h=h<<13|h>>>19,h=h*5+3864292196):(f=b[s],f=f*I&ct|f*r&V,f=f<<15|f>>>17,f=f*A&ct|f*l&V,m^=f,m=m<<13|m>>>19,m=m*5+3864292196);switch(E=0,v){case 3:E^=g[O*4+2]<<16;case 2:E^=g[O*4+1]<<8;case 1:E^=g[O*4],E=E*I&ct|E*r&V,E=E<<15|E>>>17,E=E*A&ct|E*l&V,O&1?h^=E:m^=E}this.h1=h,this.h2=m}hexdigest(){let R=this.h1,g=this.h2;return R^=g>>>1,R=R*3981806797&ct|R*36045&V,g=g*4283543511&ct|((g<<16|R>>>16)*2950163797&ct)>>>16,R^=g>>>1,R=R*444984403&ct|R*60499&V,g=g*3301882366&ct|((g<<16|R>>>16)*3120437893&ct)>>>16,R^=g>>>1,(R>>>0).toString(16).padStart(8,"0")+(g>>>0).toString(16).padStart(8,"0")}}d.MurmurHash3_64=mt},(St,d,lt)=>{var V;Object.defineProperty(d,"__esModule",{value:!0}),d.FontLoader=d.FontFaceObject=void 0;var c=lt(1);class x{constructor({ownerDocument:B=globalThis.document,styleElement:R=null}){Q(this,V,new Set);this._document=B,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(B){this.nativeFontFaces.add(B),this._document.fonts.add(B)}removeNativeFontFace(B){this.nativeFontFaces.delete(B),this._document.fonts.delete(B)}insertRule(B){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const R=this.styleElement.sheet;R.insertRule(B,R.cssRules.length)}clear(){for(const B of this.nativeFontFaces)this._document.fonts.delete(B);this.nativeFontFaces.clear(),t(this,V).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont(B){if(!(!B||t(this,V).has(B.loadedName))){if((0,c.assert)(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:R,src:g,style:N}=B,O=new FontFace(R,g,N);this.addNativeFontFace(O);try{await O.load(),t(this,V).add(R)}catch{(0,c.warn)(`Cannot load system font: ${B.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(O)}return}(0,c.unreachable)("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(B){if(B.attached||B.missingFile&&!B.systemFontInfo)return;if(B.attached=!0,B.systemFontInfo){await this.loadSystemFont(B.systemFontInfo);return}if(this.isFontLoadingAPISupported){const g=B.createNativeFontFace();if(g){this.addNativeFontFace(g);try{await g.loaded}catch(N){throw(0,c.warn)(`Failed to load font '${g.family}': '${N}'.`),B.disableFontFace=!0,N}}return}const R=B.createFontFaceRule();if(R){if(this.insertRule(R),this.isSyncFontLoadingSupported)return;await new Promise(g=>{const N=this._queueLoadingCallback(g);this._prepareFontLoadEvent(B,N)})}}get isFontLoadingAPISupported(){var R;const B=!!((R=this._document)!=null&&R.fonts);return(0,c.shadow)(this,"isFontLoadingAPISupported",B)}get isSyncFontLoadingSupported(){let B=!1;return(c.isNodeJS||typeof navigator<"u"&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(B=!0),(0,c.shadow)(this,"isSyncFontLoadingSupported",B)}_queueLoadingCallback(B){function R(){for((0,c.assert)(!N.done,"completeRequest() cannot be called twice."),N.done=!0;g.length>0&&g[0].done;){const O=g.shift();setTimeout(O.callback,0)}}const{loadingRequests:g}=this,N={done:!1,complete:R,callback:B};return g.push(N),N}get _loadTestFont(){const B=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,c.shadow)(this,"_loadTestFont",B)}_prepareFontLoadEvent(B,R){function g(n,_){return n.charCodeAt(_)<<24|n.charCodeAt(_+1)<<16|n.charCodeAt(_+2)<<8|n.charCodeAt(_+3)&255}function N(n,_,F,nt){const W=n.substring(0,_),q=n.substring(_+F);return W+nt+q}let O,v;const b=this._document.createElement("canvas");b.width=1,b.height=1;const E=b.getContext("2d");let f=0;function h(n,_){if(++f>30){(0,c.warn)("Load test font never loaded."),_();return}if(E.font="30px "+n,E.fillText(".",0,20),E.getImageData(0,0,1,1).data[3]>0){_();return}setTimeout(h.bind(null,n,_))}const m=`lt${Date.now()}${this.loadTestFontId++}`;let I=this._loadTestFont;I=N(I,976,m.length,m);const r=16,l=1482184792;let s=g(I,r);for(O=0,v=m.length-3;O<v;O+=4)s=s-l+g(m,O)|0;O<m.length&&(s=s-l+g(m+"XXX",O)|0),I=N(I,r,4,(0,c.string32)(s));const a=`url(data:font/opentype;base64,${btoa(I)});`,o=`@font-face {font-family:"${m}";src:${a}}`;this.insertRule(o);const L=this._document.createElement("div");L.style.visibility="hidden",L.style.width=L.style.height="10px",L.style.position="absolute",L.style.top=L.style.left="0px";for(const n of[B.loadedName,m]){const _=this._document.createElement("span");_.textContent="Hi",_.style.fontFamily=n,L.append(_)}this._document.body.append(L),h(m,()=>{L.remove(),R.complete()})}}V=new WeakMap,d.FontLoader=x;class ct{constructor(B,{isEvalSupported:R=!0,disableFontFace:g=!1,ignoreErrors:N=!1,inspectFont:O=null}){this.compiledGlyphs=Object.create(null);for(const v in B)this[v]=B[v];this.isEvalSupported=R!==!1,this.disableFontFace=g===!0,this.ignoreErrors=N===!0,this._inspectFont=O}createNativeFontFace(){var R;if(!this.data||this.disableFontFace)return null;let B;if(!this.cssFontInfo)B=new FontFace(this.loadedName,this.data,{});else{const g={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(g.style=`oblique ${this.cssFontInfo.italicAngle}deg`),B=new FontFace(this.cssFontInfo.fontFamily,this.data,g)}return(R=this._inspectFont)==null||R.call(this,this),B}createFontFaceRule(){var N;if(!this.data||this.disableFontFace)return null;const B=(0,c.bytesToString)(this.data),R=`url(data:${this.mimetype};base64,${btoa(B)});`;let g;if(!this.cssFontInfo)g=`@font-face {font-family:"${this.loadedName}";src:${R}}`;else{let O=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(O+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),g=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${O}src:${R}}`}return(N=this._inspectFont)==null||N.call(this,this,R),g}getPathGenerator(B,R){if(this.compiledGlyphs[R]!==void 0)return this.compiledGlyphs[R];let g;try{g=B.get(this.loadedName+"_path_"+R)}catch(N){if(!this.ignoreErrors)throw N;return(0,c.warn)(`getPathGenerator - ignoring character: "${N}".`),this.compiledGlyphs[R]=function(O,v){}}if(this.isEvalSupported&&c.FeatureTest.isEvalSupported){const N=[];for(const O of g){const v=O.args!==void 0?O.args.join(","):"";N.push("c.",O.cmd,"(",v,`);
`)}return this.compiledGlyphs[R]=new Function("c","size",N.join(""))}return this.compiledGlyphs[R]=function(N,O){for(const v of g)v.cmd==="scale"&&(v.args=[O,-O]),N[v.cmd].apply(N,v.args)}}}d.FontFaceObject=ct},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.NodeStandardFontDataFactory=d.NodeFilterFactory=d.NodeCanvasFactory=d.NodeCMapReaderFactory=void 0;var c=lt(7);lt(1);const x=function(R){return new Promise((g,N)=>{require$$5.readFile(R,(v,b)=>{if(v||!b){N(new Error(v));return}g(new Uint8Array(b))})})};class ct extends c.BaseFilterFactory{}d.NodeFilterFactory=ct;class V extends c.BaseCanvasFactory{_createCanvas(g,N){return require$$5.createCanvas(g,N)}}d.NodeCanvasFactory=V;class mt extends c.BaseCMapReaderFactory{_fetchData(g,N){return x(g).then(O=>({cMapData:O,compressionType:N}))}}d.NodeCMapReaderFactory=mt;class B extends c.BaseStandardFontDataFactory{_fetchData(g){return x(g)}}d.NodeStandardFontDataFactory=B},(St,d,lt)=>{var rt,Ve,qe;Object.defineProperty(d,"__esModule",{value:!0}),d.CanvasGraphics=void 0;var c=lt(1),x=lt(6),ct=lt(12),V=lt(13);const mt=16,B=100,R=4096,g=15,N=10,O=1e3,v=16;function b(S,e){if(S._removeMirroring)throw new Error("Context is already forwarding operations.");S.__originalSave=S.save,S.__originalRestore=S.restore,S.__originalRotate=S.rotate,S.__originalScale=S.scale,S.__originalTranslate=S.translate,S.__originalTransform=S.transform,S.__originalSetTransform=S.setTransform,S.__originalResetTransform=S.resetTransform,S.__originalClip=S.clip,S.__originalMoveTo=S.moveTo,S.__originalLineTo=S.lineTo,S.__originalBezierCurveTo=S.bezierCurveTo,S.__originalRect=S.rect,S.__originalClosePath=S.closePath,S.__originalBeginPath=S.beginPath,S._removeMirroring=()=>{S.save=S.__originalSave,S.restore=S.__originalRestore,S.rotate=S.__originalRotate,S.scale=S.__originalScale,S.translate=S.__originalTranslate,S.transform=S.__originalTransform,S.setTransform=S.__originalSetTransform,S.resetTransform=S.__originalResetTransform,S.clip=S.__originalClip,S.moveTo=S.__originalMoveTo,S.lineTo=S.__originalLineTo,S.bezierCurveTo=S.__originalBezierCurveTo,S.rect=S.__originalRect,S.closePath=S.__originalClosePath,S.beginPath=S.__originalBeginPath,delete S._removeMirroring},S.save=function(){e.save(),this.__originalSave()},S.restore=function(){e.restore(),this.__originalRestore()},S.translate=function(u,T){e.translate(u,T),this.__originalTranslate(u,T)},S.scale=function(u,T){e.scale(u,T),this.__originalScale(u,T)},S.transform=function(u,T,P,k,G,it){e.transform(u,T,P,k,G,it),this.__originalTransform(u,T,P,k,G,it)},S.setTransform=function(u,T,P,k,G,it){e.setTransform(u,T,P,k,G,it),this.__originalSetTransform(u,T,P,k,G,it)},S.resetTransform=function(){e.resetTransform(),this.__originalResetTransform()},S.rotate=function(u){e.rotate(u),this.__originalRotate(u)},S.clip=function(u){e.clip(u),this.__originalClip(u)},S.moveTo=function(i,u){e.moveTo(i,u),this.__originalMoveTo(i,u)},S.lineTo=function(i,u){e.lineTo(i,u),this.__originalLineTo(i,u)},S.bezierCurveTo=function(i,u,T,P,k,G){e.bezierCurveTo(i,u,T,P,k,G),this.__originalBezierCurveTo(i,u,T,P,k,G)},S.rect=function(i,u,T,P){e.rect(i,u,T,P),this.__originalRect(i,u,T,P)},S.closePath=function(){e.closePath(),this.__originalClosePath()},S.beginPath=function(){e.beginPath(),this.__originalBeginPath()}}class E{constructor(e){this.canvasFactory=e,this.cache=Object.create(null)}getCanvas(e,i,u){let T;return this.cache[e]!==void 0?(T=this.cache[e],this.canvasFactory.reset(T,i,u)):(T=this.canvasFactory.create(i,u),this.cache[e]=T),T}delete(e){delete this.cache[e]}clear(){for(const e in this.cache){const i=this.cache[e];this.canvasFactory.destroy(i),delete this.cache[e]}}}function f(S,e,i,u,T,P,k,G,it,ht){const[dt,pt,_t,yt,K,Z]=(0,x.getCurrentTransform)(S);if(pt===0&&_t===0){const X=k*dt+K,J=Math.round(X),ut=G*yt+Z,vt=Math.round(ut),At=(k+it)*dt+K,$=Math.abs(Math.round(At)-J)||1,Tt=(G+ht)*yt+Z,wt=Math.abs(Math.round(Tt)-vt)||1;return S.setTransform(Math.sign(dt),0,0,Math.sign(yt),J,vt),S.drawImage(e,i,u,T,P,0,0,$,wt),S.setTransform(dt,pt,_t,yt,K,Z),[$,wt]}if(dt===0&&yt===0){const X=G*_t+K,J=Math.round(X),ut=k*pt+Z,vt=Math.round(ut),At=(G+ht)*_t+K,$=Math.abs(Math.round(At)-J)||1,Tt=(k+it)*pt+Z,wt=Math.abs(Math.round(Tt)-vt)||1;return S.setTransform(0,Math.sign(pt),Math.sign(_t),0,J,vt),S.drawImage(e,i,u,T,P,0,0,wt,$),S.setTransform(dt,pt,_t,yt,K,Z),[wt,$]}S.drawImage(e,i,u,T,P,k,G,it,ht);const p=Math.hypot(dt,pt),M=Math.hypot(_t,yt);return[p*it,M*ht]}function h(S){const{width:e,height:i}=S;if(e>O||i>O)return null;const u=1e3,T=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),P=e+1;let k=new Uint8Array(P*(i+1)),G,it,ht;const dt=e+7&-8;let pt=new Uint8Array(dt*i),_t=0;for(const M of S.data){let X=128;for(;X>0;)pt[_t++]=M&X?0:255,X>>=1}let yt=0;for(_t=0,pt[_t]!==0&&(k[0]=1,++yt),it=1;it<e;it++)pt[_t]!==pt[_t+1]&&(k[it]=pt[_t]?2:1,++yt),_t++;for(pt[_t]!==0&&(k[it]=2,++yt),G=1;G<i;G++){_t=G*dt,ht=G*P,pt[_t-dt]!==pt[_t]&&(k[ht]=pt[_t]?1:8,++yt);let M=(pt[_t]?4:0)+(pt[_t-dt]?8:0);for(it=1;it<e;it++)M=(M>>2)+(pt[_t+1]?4:0)+(pt[_t-dt+1]?8:0),T[M]&&(k[ht+it]=T[M],++yt),_t++;if(pt[_t-dt]!==pt[_t]&&(k[ht+it]=pt[_t]?2:4,++yt),yt>u)return null}for(_t=dt*(i-1),ht=G*P,pt[_t]!==0&&(k[ht]=8,++yt),it=1;it<e;it++)pt[_t]!==pt[_t+1]&&(k[ht+it]=pt[_t]?4:8,++yt),_t++;if(pt[_t]!==0&&(k[ht+it]=4,++yt),yt>u)return null;const K=new Int32Array([0,P,-1,0,-P,0,0,0,1]),Z=new Path2D;for(G=0;yt&&G<=i;G++){let M=G*P;const X=M+e;for(;M<X&&!k[M];)M++;if(M===X)continue;Z.moveTo(M%P,G);const J=M;let ut=k[M];do{const vt=K[ut];do M+=vt;while(!k[M]);const At=k[M];At!==5&&At!==10?(ut=At,k[M]=0):(ut=At&51*ut>>4,k[M]&=ut>>2|ut<<2),Z.lineTo(M%P,M/P|0),k[M]||--yt}while(J!==M);--G}return pt=null,k=null,function(M){M.save(),M.scale(1/e,-1/i),M.translate(0,-i),M.fill(Z),M.beginPath(),M.restore()}}class m{constructor(e,i){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=c.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=c.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=c.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,e,i])}clone(){const e=Object.create(this);return e.clipBox=this.clipBox.slice(),e}setCurrentPoint(e,i){this.x=e,this.y=i}updatePathMinMax(e,i,u){[i,u]=c.Util.applyTransform([i,u],e),this.minX=Math.min(this.minX,i),this.minY=Math.min(this.minY,u),this.maxX=Math.max(this.maxX,i),this.maxY=Math.max(this.maxY,u)}updateRectMinMax(e,i){const u=c.Util.applyTransform(i,e),T=c.Util.applyTransform(i.slice(2),e);this.minX=Math.min(this.minX,u[0],T[0]),this.minY=Math.min(this.minY,u[1],T[1]),this.maxX=Math.max(this.maxX,u[0],T[0]),this.maxY=Math.max(this.maxY,u[1],T[1])}updateScalingPathMinMax(e,i){c.Util.scaleMinMax(e,i),this.minX=Math.min(this.minX,i[0]),this.maxX=Math.max(this.maxX,i[1]),this.minY=Math.min(this.minY,i[2]),this.maxY=Math.max(this.maxY,i[3])}updateCurvePathMinMax(e,i,u,T,P,k,G,it,ht,dt){const pt=c.Util.bezierBoundingBox(i,u,T,P,k,G,it,ht);if(dt){dt[0]=Math.min(dt[0],pt[0],pt[2]),dt[1]=Math.max(dt[1],pt[0],pt[2]),dt[2]=Math.min(dt[2],pt[1],pt[3]),dt[3]=Math.max(dt[3],pt[1],pt[3]);return}this.updateRectMinMax(e,pt)}getPathBoundingBox(e=ct.PathType.FILL,i=null){const u=[this.minX,this.minY,this.maxX,this.maxY];if(e===ct.PathType.STROKE){i||(0,c.unreachable)("Stroke bounding box must include transform.");const T=c.Util.singularValueDecompose2dScale(i),P=T[0]*this.lineWidth/2,k=T[1]*this.lineWidth/2;u[0]-=P,u[1]-=k,u[2]+=P,u[3]+=k}return u}updateClipFromPath(){const e=c.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(e||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(e){this.clipBox=e,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(e=ct.PathType.FILL,i=null){return c.Util.intersect(this.clipBox,this.getPathBoundingBox(e,i))}}function I(S,e){if(typeof ImageData<"u"&&e instanceof ImageData){S.putImageData(e,0,0);return}const i=e.height,u=e.width,T=i%v,P=(i-T)/v,k=T===0?P:P+1,G=S.createImageData(u,v);let it=0,ht;const dt=e.data,pt=G.data;let _t,yt,K,Z;if(e.kind===c.ImageKind.GRAYSCALE_1BPP){const p=dt.byteLength,M=new Uint32Array(pt.buffer,0,pt.byteLength>>2),X=M.length,J=u+7>>3,ut=4294967295,vt=c.FeatureTest.isLittleEndian?4278190080:255;for(_t=0;_t<k;_t++){for(K=_t<P?v:T,ht=0,yt=0;yt<K;yt++){const At=p-it;let $=0;const Tt=At>J?u:At*8-7,wt=Tt&-8;let jt=0,Bt=0;for(;$<wt;$+=8)Bt=dt[it++],M[ht++]=Bt&128?ut:vt,M[ht++]=Bt&64?ut:vt,M[ht++]=Bt&32?ut:vt,M[ht++]=Bt&16?ut:vt,M[ht++]=Bt&8?ut:vt,M[ht++]=Bt&4?ut:vt,M[ht++]=Bt&2?ut:vt,M[ht++]=Bt&1?ut:vt;for(;$<Tt;$++)jt===0&&(Bt=dt[it++],jt=128),M[ht++]=Bt&jt?ut:vt,jt>>=1}for(;ht<X;)M[ht++]=0;S.putImageData(G,0,_t*v)}}else if(e.kind===c.ImageKind.RGBA_32BPP){for(yt=0,Z=u*v*4,_t=0;_t<P;_t++)pt.set(dt.subarray(it,it+Z)),it+=Z,S.putImageData(G,0,yt),yt+=v;_t<k&&(Z=u*T*4,pt.set(dt.subarray(it,it+Z)),S.putImageData(G,0,yt))}else if(e.kind===c.ImageKind.RGB_24BPP)for(K=v,Z=u*K,_t=0;_t<k;_t++){for(_t>=P&&(K=T,Z=u*K),ht=0,yt=Z;yt--;)pt[ht++]=dt[it++],pt[ht++]=dt[it++],pt[ht++]=dt[it++],pt[ht++]=255;S.putImageData(G,0,_t*v)}else throw new Error(`bad image kind: ${e.kind}`)}function A(S,e){if(e.bitmap){S.drawImage(e.bitmap,0,0);return}const i=e.height,u=e.width,T=i%v,P=(i-T)/v,k=T===0?P:P+1,G=S.createImageData(u,v);let it=0;const ht=e.data,dt=G.data;for(let pt=0;pt<k;pt++){const _t=pt<P?v:T;({srcPos:it}=(0,V.convertBlackAndWhiteToRGBA)({src:ht,srcPos:it,dest:dt,width:u,height:_t,nonBlackColor:0})),S.putImageData(G,0,pt*v)}}function r(S,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const u of i)S[u]!==void 0&&(e[u]=S[u]);S.setLineDash!==void 0&&(e.setLineDash(S.getLineDash()),e.lineDashOffset=S.lineDashOffset)}function l(S){if(S.strokeStyle=S.fillStyle="#000000",S.fillRule="nonzero",S.globalAlpha=1,S.lineWidth=1,S.lineCap="butt",S.lineJoin="miter",S.miterLimit=10,S.globalCompositeOperation="source-over",S.font="10px sans-serif",S.setLineDash!==void 0&&(S.setLineDash([]),S.lineDashOffset=0),!c.isNodeJS){const{filter:e}=S;e!=="none"&&e!==""&&(S.filter="none")}}function s(S,e,i,u){const T=S.length;for(let P=3;P<T;P+=4){const k=S[P];if(k===0)S[P-3]=e,S[P-2]=i,S[P-1]=u;else if(k<255){const G=255-k;S[P-3]=S[P-3]*k+e*G>>8,S[P-2]=S[P-2]*k+i*G>>8,S[P-1]=S[P-1]*k+u*G>>8}}}function a(S,e,i){const u=S.length,T=1/255;for(let P=3;P<u;P+=4){const k=i?i[S[P]]:S[P];e[P]=e[P]*k*T|0}}function o(S,e,i){const u=S.length;for(let T=3;T<u;T+=4){const P=S[T-3]*77+S[T-2]*152+S[T-1]*28;e[T]=i?e[T]*i[P>>8]>>8:e[T]*P>>16}}function L(S,e,i,u,T,P,k,G,it,ht,dt){const pt=!!P,_t=pt?P[0]:0,yt=pt?P[1]:0,K=pt?P[2]:0,Z=T==="Luminosity"?o:a,M=Math.min(u,Math.ceil(1048576/i));for(let X=0;X<u;X+=M){const J=Math.min(M,u-X),ut=S.getImageData(G-ht,X+(it-dt),i,J),vt=e.getImageData(G,X+it,i,J);pt&&s(ut.data,_t,yt,K),Z(ut.data,vt.data,k),e.putImageData(vt,G,X+it)}}function n(S,e,i,u){const T=u[0],P=u[1],k=u[2]-T,G=u[3]-P;k===0||G===0||(L(e.context,i,k,G,e.subtype,e.backdrop,e.transferMap,T,P,e.offsetX,e.offsetY),S.save(),S.globalAlpha=1,S.globalCompositeOperation="source-over",S.setTransform(1,0,0,1,0,0),S.drawImage(i.canvas,0,0),S.restore())}function _(S,e){const i=c.Util.singularValueDecompose2dScale(S);i[0]=Math.fround(i[0]),i[1]=Math.fround(i[1]);const u=Math.fround((globalThis.devicePixelRatio||1)*x.PixelsPerInch.PDF_TO_CSS_UNITS);return e!==void 0?e:i[0]<=u||i[1]<=u}const F=["butt","round","square"],nt=["miter","round","bevel"],W={},q={},Y=class Y{constructor(e,i,u,T,P,{optionalContentConfig:k,markedContentStack:G=null},it,ht){Q(this,rt);this.ctx=e,this.current=new m(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=i,this.objs=u,this.canvasFactory=T,this.filterFactory=P,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=G||[],this.optionalContentConfig=k,this.cachedCanvases=new E(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=it,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=ht,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(e,i=null){return typeof e=="string"?e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e):i}beginDrawing({transform:e,viewport:i,transparency:u=!1,background:T=null}){const P=this.ctx.canvas.width,k=this.ctx.canvas.height,G=this.ctx.fillStyle;if(this.ctx.fillStyle=T||"#ffffff",this.ctx.fillRect(0,0,P,k),this.ctx.fillStyle=G,u){const it=this.cachedCanvases.getCanvas("transparent",P,k);this.compositeCtx=this.ctx,this.transparentCanvas=it.canvas,this.ctx=it.context,this.ctx.save(),this.ctx.transform(...(0,x.getCurrentTransform)(this.compositeCtx))}this.ctx.save(),l(this.ctx),e&&(this.ctx.transform(...e),this.outputScaleX=e[0],this.outputScaleY=e[0]),this.ctx.transform(...i.transform),this.viewportScale=i.scale,this.baseTransform=(0,x.getCurrentTransform)(this.ctx)}executeOperatorList(e,i,u,T){const P=e.argsArray,k=e.fnArray;let G=i||0;const it=P.length;if(it===G)return G;const ht=it-G>N&&typeof u=="function",dt=ht?Date.now()+g:0;let pt=0;const _t=this.commonObjs,yt=this.objs;let K;for(;;){if(T!==void 0&&G===T.nextBreakPoint)return T.breakIt(G,u),G;if(K=k[G],K!==c.OPS.dependency)this[K].apply(this,P[G]);else for(const Z of P[G]){const p=Z.startsWith("g_")?_t:yt;if(!p.has(Z))return p.get(Z,u),G}if(G++,G===it)return G;if(ht&&++pt>N){if(Date.now()>dt)return u(),G;pt=0}}}endDrawing(){z(this,rt,Ve).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const e of this._cachedBitmapsMap.values()){for(const i of e.values())typeof HTMLCanvasElement<"u"&&i instanceof HTMLCanvasElement&&(i.width=i.height=0);e.clear()}this._cachedBitmapsMap.clear(),z(this,rt,qe).call(this)}_scaleImage(e,i){const u=e.width,T=e.height;let P=Math.max(Math.hypot(i[0],i[1]),1),k=Math.max(Math.hypot(i[2],i[3]),1),G=u,it=T,ht="prescale1",dt,pt;for(;P>2&&G>1||k>2&&it>1;){let _t=G,yt=it;P>2&&G>1&&(_t=G>=16384?Math.floor(G/2)-1||1:Math.ceil(G/2),P/=G/_t),k>2&&it>1&&(yt=it>=16384?Math.floor(it/2)-1||1:Math.ceil(it)/2,k/=it/yt),dt=this.cachedCanvases.getCanvas(ht,_t,yt),pt=dt.context,pt.clearRect(0,0,_t,yt),pt.drawImage(e,0,0,G,it,0,0,_t,yt),e=dt.canvas,G=_t,it=yt,ht=ht==="prescale1"?"prescale2":"prescale1"}return{img:e,paintWidth:G,paintHeight:it}}_createMaskCanvas(e){const i=this.ctx,{width:u,height:T}=e,P=this.current.fillColor,k=this.current.patternFill,G=(0,x.getCurrentTransform)(i);let it,ht,dt,pt;if((e.bitmap||e.data)&&e.count>1){const $=e.bitmap||e.data.buffer;ht=JSON.stringify(k?G:[G.slice(0,4),P]),it=this._cachedBitmapsMap.get($),it||(it=new Map,this._cachedBitmapsMap.set($,it));const Tt=it.get(ht);if(Tt&&!k){const wt=Math.round(Math.min(G[0],G[2])+G[4]),jt=Math.round(Math.min(G[1],G[3])+G[5]);return{canvas:Tt,offsetX:wt,offsetY:jt}}dt=Tt}dt||(pt=this.cachedCanvases.getCanvas("maskCanvas",u,T),A(pt.context,e));let _t=c.Util.transform(G,[1/u,0,0,-1/T,0,0]);_t=c.Util.transform(_t,[1,0,0,1,0,-T]);const yt=c.Util.applyTransform([0,0],_t),K=c.Util.applyTransform([u,T],_t),Z=c.Util.normalizeRect([yt[0],yt[1],K[0],K[1]]),p=Math.round(Z[2]-Z[0])||1,M=Math.round(Z[3]-Z[1])||1,X=this.cachedCanvases.getCanvas("fillCanvas",p,M),J=X.context,ut=Math.min(yt[0],K[0]),vt=Math.min(yt[1],K[1]);J.translate(-ut,-vt),J.transform(..._t),dt||(dt=this._scaleImage(pt.canvas,(0,x.getCurrentTransformInverse)(J)),dt=dt.img,it&&k&&it.set(ht,dt)),J.imageSmoothingEnabled=_((0,x.getCurrentTransform)(J),e.interpolate),f(J,dt,0,0,dt.width,dt.height,0,0,u,T),J.globalCompositeOperation="source-in";const At=c.Util.transform((0,x.getCurrentTransformInverse)(J),[1,0,0,1,-ut,-vt]);return J.fillStyle=k?P.getPattern(i,this,At,ct.PathType.FILL):P,J.fillRect(0,0,u,T),it&&!k&&(this.cachedCanvases.delete("fillCanvas"),it.set(ht,X.canvas)),{canvas:X.canvas,offsetX:Math.round(ut),offsetY:Math.round(vt)}}setLineWidth(e){e!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=e,this.ctx.lineWidth=e}setLineCap(e){this.ctx.lineCap=F[e]}setLineJoin(e){this.ctx.lineJoin=nt[e]}setMiterLimit(e){this.ctx.miterLimit=e}setDash(e,i){const u=this.ctx;u.setLineDash!==void 0&&(u.setLineDash(e),u.lineDashOffset=i)}setRenderingIntent(e){}setFlatness(e){}setGState(e){for(const[i,u]of e)switch(i){case"LW":this.setLineWidth(u);break;case"LC":this.setLineCap(u);break;case"LJ":this.setLineJoin(u);break;case"ML":this.setMiterLimit(u);break;case"D":this.setDash(u[0],u[1]);break;case"RI":this.setRenderingIntent(u);break;case"FL":this.setFlatness(u);break;case"Font":this.setFont(u[0],u[1]);break;case"CA":this.current.strokeAlpha=u;break;case"ca":this.current.fillAlpha=u,this.ctx.globalAlpha=u;break;case"BM":this.ctx.globalCompositeOperation=u;break;case"SMask":this.current.activeSMask=u?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(u);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const e=this.inSMaskMode;this.current.activeSMask&&!e?this.beginSMaskMode():!this.current.activeSMask&&e&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const e=this.ctx.canvas.width,i=this.ctx.canvas.height,u="smaskGroupAt"+this.groupLevel,T=this.cachedCanvases.getCanvas(u,e,i);this.suspendedCtx=this.ctx,this.ctx=T.context;const P=this.ctx;P.setTransform(...(0,x.getCurrentTransform)(this.suspendedCtx)),r(this.suspendedCtx,P),b(P,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),r(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(e){if(!this.current.activeSMask)return;e?(e[0]=Math.floor(e[0]),e[1]=Math.floor(e[1]),e[2]=Math.ceil(e[2]),e[3]=Math.ceil(e[3])):e=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const i=this.current.activeSMask,u=this.suspendedCtx;n(u,i,this.ctx,e),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}save(){this.inSMaskMode?(r(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const e=this.current;this.stateStack.push(e),this.current=e.clone()}restore(){this.stateStack.length===0&&this.inSMaskMode&&this.endSMaskMode(),this.stateStack.length!==0&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),r(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(e,i,u,T,P,k){this.ctx.transform(e,i,u,T,P,k),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(e,i,u){const T=this.ctx,P=this.current;let k=P.x,G=P.y,it,ht;const dt=(0,x.getCurrentTransform)(T),pt=dt[0]===0&&dt[3]===0||dt[1]===0&&dt[2]===0,_t=pt?u.slice(0):null;for(let yt=0,K=0,Z=e.length;yt<Z;yt++)switch(e[yt]|0){case c.OPS.rectangle:k=i[K++],G=i[K++];const p=i[K++],M=i[K++],X=k+p,J=G+M;T.moveTo(k,G),p===0||M===0?T.lineTo(X,J):(T.lineTo(X,G),T.lineTo(X,J),T.lineTo(k,J)),pt||P.updateRectMinMax(dt,[k,G,X,J]),T.closePath();break;case c.OPS.moveTo:k=i[K++],G=i[K++],T.moveTo(k,G),pt||P.updatePathMinMax(dt,k,G);break;case c.OPS.lineTo:k=i[K++],G=i[K++],T.lineTo(k,G),pt||P.updatePathMinMax(dt,k,G);break;case c.OPS.curveTo:it=k,ht=G,k=i[K+4],G=i[K+5],T.bezierCurveTo(i[K],i[K+1],i[K+2],i[K+3],k,G),P.updateCurvePathMinMax(dt,it,ht,i[K],i[K+1],i[K+2],i[K+3],k,G,_t),K+=6;break;case c.OPS.curveTo2:it=k,ht=G,T.bezierCurveTo(k,G,i[K],i[K+1],i[K+2],i[K+3]),P.updateCurvePathMinMax(dt,it,ht,k,G,i[K],i[K+1],i[K+2],i[K+3],_t),k=i[K+2],G=i[K+3],K+=4;break;case c.OPS.curveTo3:it=k,ht=G,k=i[K+2],G=i[K+3],T.bezierCurveTo(i[K],i[K+1],k,G,k,G),P.updateCurvePathMinMax(dt,it,ht,i[K],i[K+1],k,G,k,G,_t),K+=4;break;case c.OPS.closePath:T.closePath();break}pt&&P.updateScalingPathMinMax(dt,_t),P.setCurrentPoint(k,G)}closePath(){this.ctx.closePath()}stroke(e=!0){const i=this.ctx,u=this.current.strokeColor;i.globalAlpha=this.current.strokeAlpha,this.contentVisible&&(typeof u=="object"&&(u!=null&&u.getPattern)?(i.save(),i.strokeStyle=u.getPattern(i,this,(0,x.getCurrentTransformInverse)(i),ct.PathType.STROKE),this.rescaleAndStroke(!1),i.restore()):this.rescaleAndStroke(!0)),e&&this.consumePath(this.current.getClippedPathBoundingBox()),i.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(e=!0){const i=this.ctx,u=this.current.fillColor,T=this.current.patternFill;let P=!1;T&&(i.save(),i.fillStyle=u.getPattern(i,this,(0,x.getCurrentTransformInverse)(i),ct.PathType.FILL),P=!0);const k=this.current.getClippedPathBoundingBox();this.contentVisible&&k!==null&&(this.pendingEOFill?(i.fill("evenodd"),this.pendingEOFill=!1):i.fill()),P&&i.restore(),e&&this.consumePath(k)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=W}eoClip(){this.pendingClip=q}beginText(){this.current.textMatrix=c.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const e=this.pendingTextPaths,i=this.ctx;if(e===void 0){i.beginPath();return}i.save(),i.beginPath();for(const u of e)i.setTransform(...u.transform),i.translate(u.x,u.y),u.addToPath(i,u.fontSize);i.restore(),i.clip(),i.beginPath(),delete this.pendingTextPaths}setCharSpacing(e){this.current.charSpacing=e}setWordSpacing(e){this.current.wordSpacing=e}setHScale(e){this.current.textHScale=e/100}setLeading(e){this.current.leading=-e}setFont(e,i){var dt;const u=this.commonObjs.get(e),T=this.current;if(!u)throw new Error(`Can't find font for ${e}`);if(T.fontMatrix=u.fontMatrix||c.FONT_IDENTITY_MATRIX,(T.fontMatrix[0]===0||T.fontMatrix[3]===0)&&(0,c.warn)("Invalid font matrix for font "+e),i<0?(i=-i,T.fontDirection=-1):T.fontDirection=1,this.current.font=u,this.current.fontSize=i,u.isType3Font)return;const P=u.loadedName||"sans-serif",k=((dt=u.systemFontInfo)==null?void 0:dt.css)||`"${P}", ${u.fallbackName}`;let G="normal";u.black?G="900":u.bold&&(G="bold");const it=u.italic?"italic":"normal";let ht=i;i<mt?ht=mt:i>B&&(ht=B),this.current.fontSizeScale=i/ht,this.ctx.font=`${it} ${G} ${ht}px ${k}`}setTextRenderingMode(e){this.current.textRenderingMode=e}setTextRise(e){this.current.textRise=e}moveText(e,i){this.current.x=this.current.lineX+=e,this.current.y=this.current.lineY+=i}setLeadingMoveText(e,i){this.setLeading(-i),this.moveText(e,i)}setTextMatrix(e,i,u,T,P,k){this.current.textMatrix=[e,i,u,T,P,k],this.current.textMatrixScale=Math.hypot(e,i),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(e,i,u,T){const P=this.ctx,k=this.current,G=k.font,it=k.textRenderingMode,ht=k.fontSize/k.fontSizeScale,dt=it&c.TextRenderingMode.FILL_STROKE_MASK,pt=!!(it&c.TextRenderingMode.ADD_TO_PATH_FLAG),_t=k.patternFill&&!G.missingFile;let yt;(G.disableFontFace||pt||_t)&&(yt=G.getPathGenerator(this.commonObjs,e)),G.disableFontFace||_t?(P.save(),P.translate(i,u),P.beginPath(),yt(P,ht),T&&P.setTransform(...T),(dt===c.TextRenderingMode.FILL||dt===c.TextRenderingMode.FILL_STROKE)&&P.fill(),(dt===c.TextRenderingMode.STROKE||dt===c.TextRenderingMode.FILL_STROKE)&&P.stroke(),P.restore()):((dt===c.TextRenderingMode.FILL||dt===c.TextRenderingMode.FILL_STROKE)&&P.fillText(e,i,u),(dt===c.TextRenderingMode.STROKE||dt===c.TextRenderingMode.FILL_STROKE)&&P.strokeText(e,i,u)),pt&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:(0,x.getCurrentTransform)(P),x:i,y:u,fontSize:ht,addToPath:yt})}get isFontSubpixelAAEnabled(){const{context:e}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);e.scale(1.5,1),e.fillText("I",0,10);const i=e.getImageData(0,0,10,10).data;let u=!1;for(let T=3;T<i.length;T+=4)if(i[T]>0&&i[T]<255){u=!0;break}return(0,c.shadow)(this,"isFontSubpixelAAEnabled",u)}showText(e){const i=this.current,u=i.font;if(u.isType3Font)return this.showType3Text(e);const T=i.fontSize;if(T===0)return;const P=this.ctx,k=i.fontSizeScale,G=i.charSpacing,it=i.wordSpacing,ht=i.fontDirection,dt=i.textHScale*ht,pt=e.length,_t=u.vertical,yt=_t?1:-1,K=u.defaultVMetrics,Z=T*i.fontMatrix[0],p=i.textRenderingMode===c.TextRenderingMode.FILL&&!u.disableFontFace&&!i.patternFill;P.save(),P.transform(...i.textMatrix),P.translate(i.x,i.y+i.textRise),ht>0?P.scale(dt,-1):P.scale(dt,1);let M;if(i.patternFill){P.save();const At=i.fillColor.getPattern(P,this,(0,x.getCurrentTransformInverse)(P),ct.PathType.FILL);M=(0,x.getCurrentTransform)(P),P.restore(),P.fillStyle=At}let X=i.lineWidth;const J=i.textMatrixScale;if(J===0||X===0){const At=i.textRenderingMode&c.TextRenderingMode.FILL_STROKE_MASK;(At===c.TextRenderingMode.STROKE||At===c.TextRenderingMode.FILL_STROKE)&&(X=this.getSinglePixelWidth())}else X/=J;if(k!==1&&(P.scale(k,k),X/=k),P.lineWidth=X,u.isInvalidPDFjsFont){const At=[];let $=0;for(const Tt of e)At.push(Tt.unicode),$+=Tt.width;P.fillText(At.join(""),0,0),i.x+=$*Z*dt,P.restore(),this.compose();return}let ut=0,vt;for(vt=0;vt<pt;++vt){const At=e[vt];if(typeof At=="number"){ut+=yt*At*T/1e3;continue}let $=!1;const Tt=(At.isSpace?it:0)+G,wt=At.fontChar,jt=At.accent;let Bt,Xt,Ft=At.width;if(_t){const $t=At.vmetric||K,qt=-(At.vmetric?$t[1]:Ft*.5)*Z,bt=$t[2]*Z;Ft=$t?-$t[0]:Ft,Bt=qt/k,Xt=(ut+bt)/k}else Bt=ut/k,Xt=0;if(u.remeasure&&Ft>0){const $t=P.measureText(wt).width*1e3/T*k;if(Ft<$t&&this.isFontSubpixelAAEnabled){const qt=Ft/$t;$=!0,P.save(),P.scale(qt,1),Bt/=qt}else Ft!==$t&&(Bt+=(Ft-$t)/2e3*T/k)}if(this.contentVisible&&(At.isInFont||u.missingFile)){if(p&&!jt)P.fillText(wt,Bt,Xt);else if(this.paintChar(wt,Bt,Xt,M),jt){const $t=Bt+T*jt.offset.x/k,qt=Xt-T*jt.offset.y/k;this.paintChar(jt.fontChar,$t,qt,M)}}const Nt=_t?Ft*Z-Tt*ht:Ft*Z+Tt*ht;ut+=Nt,$&&P.restore()}_t?i.y-=ut:i.x+=ut*dt,P.restore(),this.compose()}showType3Text(e){const i=this.ctx,u=this.current,T=u.font,P=u.fontSize,k=u.fontDirection,G=T.vertical?1:-1,it=u.charSpacing,ht=u.wordSpacing,dt=u.textHScale*k,pt=u.fontMatrix||c.FONT_IDENTITY_MATRIX,_t=e.length,yt=u.textRenderingMode===c.TextRenderingMode.INVISIBLE;let K,Z,p,M;if(!(yt||P===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,i.save(),i.transform(...u.textMatrix),i.translate(u.x,u.y),i.scale(dt,k),K=0;K<_t;++K){if(Z=e[K],typeof Z=="number"){M=G*Z*P/1e3,this.ctx.translate(M,0),u.x+=M*dt;continue}const X=(Z.isSpace?ht:0)+it,J=T.charProcOperatorList[Z.operatorListId];if(!J){(0,c.warn)(`Type3 character "${Z.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=Z,this.save(),i.scale(P,P),i.transform(...pt),this.executeOperatorList(J),this.restore()),p=c.Util.applyTransform([Z.width,0],pt)[0]*P+X,i.translate(p,0),u.x+=p*dt}i.restore(),this.processingType3=null}}setCharWidth(e,i){}setCharWidthAndBounds(e,i,u,T,P,k){this.ctx.rect(u,T,P-u,k-T),this.ctx.clip(),this.endPath()}getColorN_Pattern(e){let i;if(e[0]==="TilingPattern"){const u=e[1],T=this.baseTransform||(0,x.getCurrentTransform)(this.ctx),P={createCanvasGraphics:k=>new Y(k,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};i=new ct.TilingPattern(e,u,this.ctx,P,T)}else i=this._getPattern(e[1],e[2]);return i}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(e,i,u){const T=c.Util.makeHexColor(e,i,u);this.ctx.strokeStyle=T,this.current.strokeColor=T}setFillRGBColor(e,i,u){const T=c.Util.makeHexColor(e,i,u);this.ctx.fillStyle=T,this.current.fillColor=T,this.current.patternFill=!1}_getPattern(e,i=null){let u;return this.cachedPatterns.has(e)?u=this.cachedPatterns.get(e):(u=(0,ct.getShadingPattern)(this.getObject(e)),this.cachedPatterns.set(e,u)),i&&(u.matrix=i),u}shadingFill(e){if(!this.contentVisible)return;const i=this.ctx;this.save();const u=this._getPattern(e);i.fillStyle=u.getPattern(i,this,(0,x.getCurrentTransformInverse)(i),ct.PathType.SHADING);const T=(0,x.getCurrentTransformInverse)(i);if(T){const{width:P,height:k}=i.canvas,[G,it,ht,dt]=c.Util.getAxialAlignedBoundingBox([0,0,P,k],T);this.ctx.fillRect(G,it,ht-G,dt-it)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){(0,c.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,c.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(e,i){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(e)&&e.length===6&&this.transform(...e),this.baseTransform=(0,x.getCurrentTransform)(this.ctx),i)){const u=i[2]-i[0],T=i[3]-i[1];this.ctx.rect(i[0],i[1],u,T),this.current.updateRectMinMax((0,x.getCurrentTransform)(this.ctx),i),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(e){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const i=this.ctx;e.isolated||(0,c.info)("TODO: Support non-isolated groups."),e.knockout&&(0,c.warn)("Knockout groups not supported.");const u=(0,x.getCurrentTransform)(i);if(e.matrix&&i.transform(...e.matrix),!e.bbox)throw new Error("Bounding box is required.");let T=c.Util.getAxialAlignedBoundingBox(e.bbox,(0,x.getCurrentTransform)(i));const P=[0,0,i.canvas.width,i.canvas.height];T=c.Util.intersect(T,P)||[0,0,0,0];const k=Math.floor(T[0]),G=Math.floor(T[1]);let it=Math.max(Math.ceil(T[2])-k,1),ht=Math.max(Math.ceil(T[3])-G,1),dt=1,pt=1;it>R&&(dt=it/R,it=R),ht>R&&(pt=ht/R,ht=R),this.current.startNewPathAndClipBox([0,0,it,ht]);let _t="groupAt"+this.groupLevel;e.smask&&(_t+="_smask_"+this.smaskCounter++%2);const yt=this.cachedCanvases.getCanvas(_t,it,ht),K=yt.context;K.scale(1/dt,1/pt),K.translate(-k,-G),K.transform(...u),e.smask?this.smaskStack.push({canvas:yt.canvas,context:K,offsetX:k,offsetY:G,scaleX:dt,scaleY:pt,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null}):(i.setTransform(1,0,0,1,0,0),i.translate(k,G),i.scale(dt,pt),i.save()),r(i,K),this.ctx=K,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(i),this.groupLevel++}endGroup(e){if(!this.contentVisible)return;this.groupLevel--;const i=this.ctx,u=this.groupStack.pop();if(this.ctx=u,this.ctx.imageSmoothingEnabled=!1,e.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const T=(0,x.getCurrentTransform)(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...T);const P=c.Util.getAxialAlignedBoundingBox([0,0,i.canvas.width,i.canvas.height],T);this.ctx.drawImage(i.canvas,0,0),this.ctx.restore(),this.compose(P)}}beginAnnotation(e,i,u,T,P){if(z(this,rt,Ve).call(this),l(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),Array.isArray(i)&&i.length===4){const k=i[2]-i[0],G=i[3]-i[1];if(P&&this.annotationCanvasMap){u=u.slice(),u[4]-=i[0],u[5]-=i[1],i=i.slice(),i[0]=i[1]=0,i[2]=k,i[3]=G;const[it,ht]=c.Util.singularValueDecompose2dScale((0,x.getCurrentTransform)(this.ctx)),{viewportScale:dt}=this,pt=Math.ceil(k*this.outputScaleX*dt),_t=Math.ceil(G*this.outputScaleY*dt);this.annotationCanvas=this.canvasFactory.create(pt,_t);const{canvas:yt,context:K}=this.annotationCanvas;this.annotationCanvasMap.set(e,yt),this.annotationCanvas.savedCtx=this.ctx,this.ctx=K,this.ctx.save(),this.ctx.setTransform(it,0,0,-ht,0,G*ht),l(this.ctx)}else l(this.ctx),this.ctx.rect(i[0],i[1],k,G),this.ctx.clip(),this.endPath()}this.current=new m(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...u),this.transform(...T)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),z(this,rt,qe).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(e){if(!this.contentVisible)return;const i=e.count;e=this.getObject(e.data,e),e.count=i;const u=this.ctx,T=this.processingType3;if(T&&(T.compiled===void 0&&(T.compiled=h(e)),T.compiled)){T.compiled(u);return}const P=this._createMaskCanvas(e),k=P.canvas;u.save(),u.setTransform(1,0,0,1,0,0),u.drawImage(k,P.offsetX,P.offsetY),u.restore(),this.compose()}paintImageMaskXObjectRepeat(e,i,u=0,T=0,P,k){if(!this.contentVisible)return;e=this.getObject(e.data,e);const G=this.ctx;G.save();const it=(0,x.getCurrentTransform)(G);G.transform(i,u,T,P,0,0);const ht=this._createMaskCanvas(e);G.setTransform(1,0,0,1,ht.offsetX-it[4],ht.offsetY-it[5]);for(let dt=0,pt=k.length;dt<pt;dt+=2){const _t=c.Util.transform(it,[i,u,T,P,k[dt],k[dt+1]]),[yt,K]=c.Util.applyTransform([0,0],_t);G.drawImage(ht.canvas,yt,K)}G.restore(),this.compose()}paintImageMaskXObjectGroup(e){if(!this.contentVisible)return;const i=this.ctx,u=this.current.fillColor,T=this.current.patternFill;for(const P of e){const{data:k,width:G,height:it,transform:ht}=P,dt=this.cachedCanvases.getCanvas("maskCanvas",G,it),pt=dt.context;pt.save();const _t=this.getObject(k,P);A(pt,_t),pt.globalCompositeOperation="source-in",pt.fillStyle=T?u.getPattern(pt,this,(0,x.getCurrentTransformInverse)(i),ct.PathType.FILL):u,pt.fillRect(0,0,G,it),pt.restore(),i.save(),i.transform(...ht),i.scale(1,-1),f(i,dt.canvas,0,0,G,it,0,-1,1,1),i.restore()}this.compose()}paintImageXObject(e){if(!this.contentVisible)return;const i=this.getObject(e);if(!i){(0,c.warn)("Dependent image isn't ready yet");return}this.paintInlineImageXObject(i)}paintImageXObjectRepeat(e,i,u,T){if(!this.contentVisible)return;const P=this.getObject(e);if(!P){(0,c.warn)("Dependent image isn't ready yet");return}const k=P.width,G=P.height,it=[];for(let ht=0,dt=T.length;ht<dt;ht+=2)it.push({transform:[i,0,0,u,T[ht],T[ht+1]],x:0,y:0,w:k,h:G});this.paintInlineImageXObjectGroup(P,it)}applyTransferMapsToCanvas(e){return this.current.transferMaps!=="none"&&(e.filter=this.current.transferMaps,e.drawImage(e.canvas,0,0),e.filter="none"),e.canvas}applyTransferMapsToBitmap(e){if(this.current.transferMaps==="none")return e.bitmap;const{bitmap:i,width:u,height:T}=e,P=this.cachedCanvases.getCanvas("inlineImage",u,T),k=P.context;return k.filter=this.current.transferMaps,k.drawImage(i,0,0),k.filter="none",P.canvas}paintInlineImageXObject(e){if(!this.contentVisible)return;const i=e.width,u=e.height,T=this.ctx;if(this.save(),!c.isNodeJS){const{filter:G}=T;G!=="none"&&G!==""&&(T.filter="none")}T.scale(1/i,-1/u);let P;if(e.bitmap)P=this.applyTransferMapsToBitmap(e);else if(typeof HTMLElement=="function"&&e instanceof HTMLElement||!e.data)P=e;else{const it=this.cachedCanvases.getCanvas("inlineImage",i,u).context;I(it,e),P=this.applyTransferMapsToCanvas(it)}const k=this._scaleImage(P,(0,x.getCurrentTransformInverse)(T));T.imageSmoothingEnabled=_((0,x.getCurrentTransform)(T),e.interpolate),f(T,k.img,0,0,k.paintWidth,k.paintHeight,0,-u,i,u),this.compose(),this.restore()}paintInlineImageXObjectGroup(e,i){if(!this.contentVisible)return;const u=this.ctx;let T;if(e.bitmap)T=e.bitmap;else{const P=e.width,k=e.height,it=this.cachedCanvases.getCanvas("inlineImage",P,k).context;I(it,e),T=this.applyTransferMapsToCanvas(it)}for(const P of i)u.save(),u.transform(...P.transform),u.scale(1,-1),f(u,T,P.x,P.y,P.w,P.h,0,-1,1,1),u.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(e){}markPointProps(e,i){}beginMarkedContent(e){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(e,i){e==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(i)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(e){const i=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(e);const u=this.ctx;this.pendingClip&&(i||(this.pendingClip===q?u.clip("evenodd"):u.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),u.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const e=(0,x.getCurrentTransform)(this.ctx);if(e[1]===0&&e[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(e[0]),Math.abs(e[3]));else{const i=Math.abs(e[0]*e[3]-e[2]*e[1]),u=Math.hypot(e[0],e[2]),T=Math.hypot(e[1],e[3]);this._cachedGetSinglePixelWidth=Math.max(u,T)/i}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:e}=this.current,{a:i,b:u,c:T,d:P}=this.ctx.getTransform();let k,G;if(u===0&&T===0){const it=Math.abs(i),ht=Math.abs(P);if(it===ht)if(e===0)k=G=1/it;else{const dt=it*e;k=G=dt<1?1/dt:1}else if(e===0)k=1/it,G=1/ht;else{const dt=it*e,pt=ht*e;k=dt<1?1/dt:1,G=pt<1?1/pt:1}}else{const it=Math.abs(i*P-u*T),ht=Math.hypot(i,u),dt=Math.hypot(T,P);if(e===0)k=dt/it,G=ht/it;else{const pt=e*it;k=dt>pt?dt/pt:1,G=ht>pt?ht/pt:1}}this._cachedScaleForStroking[0]=k,this._cachedScaleForStroking[1]=G}return this._cachedScaleForStroking}rescaleAndStroke(e){const{ctx:i}=this,{lineWidth:u}=this.current,[T,P]=this.getScaleForStroking();if(i.lineWidth=u||1,T===1&&P===1){i.stroke();return}const k=i.getLineDash();if(e&&i.save(),i.scale(T,P),k.length>0){const G=Math.max(T,P);i.setLineDash(k.map(it=>it/G)),i.lineDashOffset/=G}i.stroke(),e&&i.restore()}isContentVisible(){for(let e=this.markedContentStack.length-1;e>=0;e--)if(!this.markedContentStack[e].visible)return!1;return!0}};rt=new WeakSet,Ve=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},qe=function(){if(this.pageColors){const e=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(e!=="none"){const i=this.ctx.filter;this.ctx.filter=e,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=i}}};let j=Y;d.CanvasGraphics=j;for(const S in c.OPS)j.prototype[S]!==void 0&&(j.prototype[c.OPS[S]]=j.prototype[S])},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.TilingPattern=d.PathType=void 0,d.getShadingPattern=v;var c=lt(1),x=lt(6);const ct={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};d.PathType=ct;function V(h,m){if(!m)return;const I=m[2]-m[0],A=m[3]-m[1],r=new Path2D;r.rect(m[0],m[1],I,A),h.clip(r)}class mt{constructor(){this.constructor===mt&&(0,c.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,c.unreachable)("Abstract method `getPattern` called.")}}class B extends mt{constructor(m){super(),this._type=m[1],this._bbox=m[2],this._colorStops=m[3],this._p0=m[4],this._p1=m[5],this._r0=m[6],this._r1=m[7],this.matrix=null}_createGradient(m){let I;this._type==="axial"?I=m.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(I=m.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const A of this._colorStops)I.addColorStop(A[0],A[1]);return I}getPattern(m,I,A,r){let l;if(r===ct.STROKE||r===ct.FILL){const s=I.current.getClippedPathBoundingBox(r,(0,x.getCurrentTransform)(m))||[0,0,0,0],a=Math.ceil(s[2]-s[0])||1,o=Math.ceil(s[3]-s[1])||1,L=I.cachedCanvases.getCanvas("pattern",a,o,!0),n=L.context;n.clearRect(0,0,n.canvas.width,n.canvas.height),n.beginPath(),n.rect(0,0,n.canvas.width,n.canvas.height),n.translate(-s[0],-s[1]),A=c.Util.transform(A,[1,0,0,1,s[0],s[1]]),n.transform(...I.baseTransform),this.matrix&&n.transform(...this.matrix),V(n,this._bbox),n.fillStyle=this._createGradient(n),n.fill(),l=m.createPattern(L.canvas,"no-repeat");const _=new DOMMatrix(A);l.setTransform(_)}else V(m,this._bbox),l=this._createGradient(m);return l}}function R(h,m,I,A,r,l,s,a){const o=m.coords,L=m.colors,n=h.data,_=h.width*4;let F;o[I+1]>o[A+1]&&(F=I,I=A,A=F,F=l,l=s,s=F),o[A+1]>o[r+1]&&(F=A,A=r,r=F,F=s,s=a,a=F),o[I+1]>o[A+1]&&(F=I,I=A,A=F,F=l,l=s,s=F);const nt=(o[I]+m.offsetX)*m.scaleX,W=(o[I+1]+m.offsetY)*m.scaleY,q=(o[A]+m.offsetX)*m.scaleX,j=(o[A+1]+m.offsetY)*m.scaleY,rt=(o[r]+m.offsetX)*m.scaleX,C=(o[r+1]+m.offsetY)*m.scaleY;if(W>=C)return;const U=L[l],Y=L[l+1],S=L[l+2],e=L[s],i=L[s+1],u=L[s+2],T=L[a],P=L[a+1],k=L[a+2],G=Math.round(W),it=Math.round(C);let ht,dt,pt,_t,yt,K,Z,p;for(let M=G;M<=it;M++){if(M<j){const At=M<W?0:(W-M)/(W-j);ht=nt-(nt-q)*At,dt=U-(U-e)*At,pt=Y-(Y-i)*At,_t=S-(S-u)*At}else{let At;M>C?At=1:j===C?At=0:At=(j-M)/(j-C),ht=q-(q-rt)*At,dt=e-(e-T)*At,pt=i-(i-P)*At,_t=u-(u-k)*At}let X;M<W?X=0:M>C?X=1:X=(W-M)/(W-C),yt=nt-(nt-rt)*X,K=U-(U-T)*X,Z=Y-(Y-P)*X,p=S-(S-k)*X;const J=Math.round(Math.min(ht,yt)),ut=Math.round(Math.max(ht,yt));let vt=_*M+J*4;for(let At=J;At<=ut;At++)X=(ht-At)/(ht-yt),X<0?X=0:X>1&&(X=1),n[vt++]=dt-(dt-K)*X|0,n[vt++]=pt-(pt-Z)*X|0,n[vt++]=_t-(_t-p)*X|0,n[vt++]=255}}function g(h,m,I){const A=m.coords,r=m.colors;let l,s;switch(m.type){case"lattice":const a=m.verticesPerRow,o=Math.floor(A.length/a)-1,L=a-1;for(l=0;l<o;l++){let n=l*a;for(let _=0;_<L;_++,n++)R(h,I,A[n],A[n+1],A[n+a],r[n],r[n+1],r[n+a]),R(h,I,A[n+a+1],A[n+1],A[n+a],r[n+a+1],r[n+1],r[n+a])}break;case"triangles":for(l=0,s=A.length;l<s;l+=3)R(h,I,A[l],A[l+1],A[l+2],r[l],r[l+1],r[l+2]);break;default:throw new Error("illegal figure")}}class N extends mt{constructor(m){super(),this._coords=m[2],this._colors=m[3],this._figures=m[4],this._bounds=m[5],this._bbox=m[7],this._background=m[8],this.matrix=null}_createMeshCanvas(m,I,A){const a=Math.floor(this._bounds[0]),o=Math.floor(this._bounds[1]),L=Math.ceil(this._bounds[2])-a,n=Math.ceil(this._bounds[3])-o,_=Math.min(Math.ceil(Math.abs(L*m[0]*1.1)),3e3),F=Math.min(Math.ceil(Math.abs(n*m[1]*1.1)),3e3),nt=L/_,W=n/F,q={coords:this._coords,colors:this._colors,offsetX:-a,offsetY:-o,scaleX:1/nt,scaleY:1/W},j=_+2*2,rt=F+2*2,C=A.getCanvas("mesh",j,rt,!1),U=C.context,Y=U.createImageData(_,F);if(I){const e=Y.data;for(let i=0,u=e.length;i<u;i+=4)e[i]=I[0],e[i+1]=I[1],e[i+2]=I[2],e[i+3]=255}for(const e of this._figures)g(Y,e,q);return U.putImageData(Y,2,2),{canvas:C.canvas,offsetX:a-2*nt,offsetY:o-2*W,scaleX:nt,scaleY:W}}getPattern(m,I,A,r){V(m,this._bbox);let l;if(r===ct.SHADING)l=c.Util.singularValueDecompose2dScale((0,x.getCurrentTransform)(m));else if(l=c.Util.singularValueDecompose2dScale(I.baseTransform),this.matrix){const a=c.Util.singularValueDecompose2dScale(this.matrix);l=[l[0]*a[0],l[1]*a[1]]}const s=this._createMeshCanvas(l,r===ct.SHADING?null:this._background,I.cachedCanvases);return r!==ct.SHADING&&(m.setTransform(...I.baseTransform),this.matrix&&m.transform(...this.matrix)),m.translate(s.offsetX,s.offsetY),m.scale(s.scaleX,s.scaleY),m.createPattern(s.canvas,"no-repeat")}}class O extends mt{getPattern(){return"hotpink"}}function v(h){switch(h[0]){case"RadialAxial":return new B(h);case"Mesh":return new N(h);case"Dummy":return new O}throw new Error(`Unknown IR type: ${h[0]}`)}const b={COLORED:1,UNCOLORED:2},f=class f{constructor(m,I,A,r,l){this.operatorList=m[2],this.matrix=m[3]||[1,0,0,1,0,0],this.bbox=m[4],this.xstep=m[5],this.ystep=m[6],this.paintType=m[7],this.tilingType=m[8],this.color=I,this.ctx=A,this.canvasGraphicsFactory=r,this.baseTransform=l}createPatternCanvas(m){const I=this.operatorList,A=this.bbox,r=this.xstep,l=this.ystep,s=this.paintType,a=this.tilingType,o=this.color,L=this.canvasGraphicsFactory;(0,c.info)("TilingType: "+a);const n=A[0],_=A[1],F=A[2],nt=A[3],W=c.Util.singularValueDecompose2dScale(this.matrix),q=c.Util.singularValueDecompose2dScale(this.baseTransform),j=[W[0]*q[0],W[1]*q[1]],rt=this.getSizeAndScale(r,this.ctx.canvas.width,j[0]),C=this.getSizeAndScale(l,this.ctx.canvas.height,j[1]),U=m.cachedCanvases.getCanvas("pattern",rt.size,C.size,!0),Y=U.context,S=L.createCanvasGraphics(Y);S.groupLevel=m.groupLevel,this.setFillAndStrokeStyleToContext(S,s,o);let e=n,i=_,u=F,T=nt;return n<0&&(e=0,u+=Math.abs(n)),_<0&&(i=0,T+=Math.abs(_)),Y.translate(-(rt.scale*e),-(C.scale*i)),S.transform(rt.scale,0,0,C.scale,0,0),Y.save(),this.clipBbox(S,e,i,u,T),S.baseTransform=(0,x.getCurrentTransform)(S.ctx),S.executeOperatorList(I),S.endDrawing(),{canvas:U.canvas,scaleX:rt.scale,scaleY:C.scale,offsetX:e,offsetY:i}}getSizeAndScale(m,I,A){m=Math.abs(m);const r=Math.max(f.MAX_PATTERN_SIZE,I);let l=Math.ceil(m*A);return l>=r?l=r:A=l/m,{scale:A,size:l}}clipBbox(m,I,A,r,l){const s=r-I,a=l-A;m.ctx.rect(I,A,s,a),m.current.updateRectMinMax((0,x.getCurrentTransform)(m.ctx),[I,A,r,l]),m.clip(),m.endPath()}setFillAndStrokeStyleToContext(m,I,A){const r=m.ctx,l=m.current;switch(I){case b.COLORED:const s=this.ctx;r.fillStyle=s.fillStyle,r.strokeStyle=s.strokeStyle,l.fillColor=s.fillStyle,l.strokeColor=s.strokeStyle;break;case b.UNCOLORED:const a=c.Util.makeHexColor(A[0],A[1],A[2]);r.fillStyle=a,r.strokeStyle=a,l.fillColor=a,l.strokeColor=a;break;default:throw new c.FormatError(`Unsupported paint type: ${I}`)}}getPattern(m,I,A,r){let l=A;r!==ct.SHADING&&(l=c.Util.transform(l,I.baseTransform),this.matrix&&(l=c.Util.transform(l,this.matrix)));const s=this.createPatternCanvas(I);let a=new DOMMatrix(l);a=a.translate(s.offsetX,s.offsetY),a=a.scale(1/s.scaleX,1/s.scaleY);const o=m.createPattern(s.canvas,"repeat");return o.setTransform(a),o}};Kt(f,"MAX_PATTERN_SIZE",3e3);let E=f;d.TilingPattern=E},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.convertBlackAndWhiteToRGBA=ct,d.convertToRGBA=x,d.grayToRGBA=mt;var c=lt(1);function x(B){switch(B.kind){case c.ImageKind.GRAYSCALE_1BPP:return ct(B);case c.ImageKind.RGB_24BPP:return V(B)}return null}function ct({src:B,srcPos:R=0,dest:g,width:N,height:O,nonBlackColor:v=4294967295,inverseDecode:b=!1}){const E=c.FeatureTest.isLittleEndian?4278190080:255,[f,h]=b?[v,E]:[E,v],m=N>>3,I=N&7,A=B.length;g=new Uint32Array(g.buffer);let r=0;for(let l=0;l<O;l++){for(const a=R+m;R<a;R++){const o=R<A?B[R]:255;g[r++]=o&128?h:f,g[r++]=o&64?h:f,g[r++]=o&32?h:f,g[r++]=o&16?h:f,g[r++]=o&8?h:f,g[r++]=o&4?h:f,g[r++]=o&2?h:f,g[r++]=o&1?h:f}if(I===0)continue;const s=R<A?B[R++]:255;for(let a=0;a<I;a++)g[r++]=s&1<<7-a?h:f}return{srcPos:R,destPos:r}}function V({src:B,srcPos:R=0,dest:g,destPos:N=0,width:O,height:v}){let b=0;const E=B.length>>2,f=new Uint32Array(B.buffer,R,E);if(c.FeatureTest.isLittleEndian){for(;b<E-2;b+=3,N+=4){const h=f[b],m=f[b+1],I=f[b+2];g[N]=h|4278190080,g[N+1]=h>>>24|m<<8|4278190080,g[N+2]=m>>>16|I<<16|4278190080,g[N+3]=I>>>8|4278190080}for(let h=b*4,m=B.length;h<m;h+=3)g[N++]=B[h]|B[h+1]<<8|B[h+2]<<16|4278190080}else{for(;b<E-2;b+=3,N+=4){const h=f[b],m=f[b+1],I=f[b+2];g[N]=h|255,g[N+1]=h<<24|m>>>8|255,g[N+2]=m<<16|I>>>16|255,g[N+3]=I<<8|255}for(let h=b*4,m=B.length;h<m;h+=3)g[N++]=B[h]<<24|B[h+1]<<16|B[h+2]<<8|255}return{srcPos:R,destPos:N}}function mt(B,R){if(c.FeatureTest.isLittleEndian)for(let g=0,N=B.length;g<N;g++)R[g]=B[g]*65793|4278190080;else for(let g=0,N=B.length;g<N;g++)R[g]=B[g]*16843008|255}},(St,d)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.GlobalWorkerOptions=void 0;const lt=Object.create(null);d.GlobalWorkerOptions=lt,lt.workerPort=null,lt.workerSrc=""},(St,d,lt)=>{var B,wi,Ci,Ee;Object.defineProperty(d,"__esModule",{value:!0}),d.MessageHandler=void 0;var c=lt(1);const x={DATA:1,ERROR:2},ct={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function V(O){switch(O instanceof Error||typeof O=="object"&&O!==null||(0,c.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),O.name){case"AbortException":return new c.AbortException(O.message);case"MissingPDFException":return new c.MissingPDFException(O.message);case"PasswordException":return new c.PasswordException(O.message,O.code);case"UnexpectedResponseException":return new c.UnexpectedResponseException(O.message,O.status);case"UnknownErrorException":return new c.UnknownErrorException(O.message,O.details);default:return new c.UnknownErrorException(O.message,O.toString())}}class mt{constructor(v,b,E){Q(this,B);this.sourceName=v,this.targetName=b,this.comObj=E,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=f=>{const h=f.data;if(h.targetName!==this.sourceName)return;if(h.stream){z(this,B,Ci).call(this,h);return}if(h.callback){const I=h.callbackId,A=this.callbackCapabilities[I];if(!A)throw new Error(`Cannot resolve callback ${I}`);if(delete this.callbackCapabilities[I],h.callback===x.DATA)A.resolve(h.data);else if(h.callback===x.ERROR)A.reject(V(h.reason));else throw new Error("Unexpected callback case");return}const m=this.actionHandler[h.action];if(!m)throw new Error(`Unknown action from worker: ${h.action}`);if(h.callbackId){const I=this.sourceName,A=h.sourceName;new Promise(function(r){r(m(h.data))}).then(function(r){E.postMessage({sourceName:I,targetName:A,callback:x.DATA,callbackId:h.callbackId,data:r})},function(r){E.postMessage({sourceName:I,targetName:A,callback:x.ERROR,callbackId:h.callbackId,reason:V(r)})});return}if(h.streamId){z(this,B,wi).call(this,h);return}m(h.data)},E.addEventListener("message",this._onComObjOnMessage)}on(v,b){const E=this.actionHandler;if(E[v])throw new Error(`There is already an actionName called "${v}"`);E[v]=b}send(v,b,E){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:v,data:b},E)}sendWithPromise(v,b,E){const f=this.callbackId++,h=new c.PromiseCapability;this.callbackCapabilities[f]=h;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:v,callbackId:f,data:b},E)}catch(m){h.reject(m)}return h.promise}sendWithStream(v,b,E,f){const h=this.streamId++,m=this.sourceName,I=this.targetName,A=this.comObj;return new ReadableStream({start:r=>{const l=new c.PromiseCapability;return this.streamControllers[h]={controller:r,startCall:l,pullCall:null,cancelCall:null,isClosed:!1},A.postMessage({sourceName:m,targetName:I,action:v,streamId:h,data:b,desiredSize:r.desiredSize},f),l.promise},pull:r=>{const l=new c.PromiseCapability;return this.streamControllers[h].pullCall=l,A.postMessage({sourceName:m,targetName:I,stream:ct.PULL,streamId:h,desiredSize:r.desiredSize}),l.promise},cancel:r=>{(0,c.assert)(r instanceof Error,"cancel must have a valid reason");const l=new c.PromiseCapability;return this.streamControllers[h].cancelCall=l,this.streamControllers[h].isClosed=!0,A.postMessage({sourceName:m,targetName:I,stream:ct.CANCEL,streamId:h,reason:V(r)}),l.promise}},E)}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}B=new WeakSet,wi=function(v){const b=v.streamId,E=this.sourceName,f=v.sourceName,h=this.comObj,m=this,I=this.actionHandler[v.action],A={enqueue(r,l=1,s){if(this.isCancelled)return;const a=this.desiredSize;this.desiredSize-=l,a>0&&this.desiredSize<=0&&(this.sinkCapability=new c.PromiseCapability,this.ready=this.sinkCapability.promise),h.postMessage({sourceName:E,targetName:f,stream:ct.ENQUEUE,streamId:b,chunk:r},s)},close(){this.isCancelled||(this.isCancelled=!0,h.postMessage({sourceName:E,targetName:f,stream:ct.CLOSE,streamId:b}),delete m.streamSinks[b])},error(r){(0,c.assert)(r instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,h.postMessage({sourceName:E,targetName:f,stream:ct.ERROR,streamId:b,reason:V(r)}))},sinkCapability:new c.PromiseCapability,onPull:null,onCancel:null,isCancelled:!1,desiredSize:v.desiredSize,ready:null};A.sinkCapability.resolve(),A.ready=A.sinkCapability.promise,this.streamSinks[b]=A,new Promise(function(r){r(I(v.data,A))}).then(function(){h.postMessage({sourceName:E,targetName:f,stream:ct.START_COMPLETE,streamId:b,success:!0})},function(r){h.postMessage({sourceName:E,targetName:f,stream:ct.START_COMPLETE,streamId:b,reason:V(r)})})},Ci=function(v){const b=v.streamId,E=this.sourceName,f=v.sourceName,h=this.comObj,m=this.streamControllers[b],I=this.streamSinks[b];switch(v.stream){case ct.START_COMPLETE:v.success?m.startCall.resolve():m.startCall.reject(V(v.reason));break;case ct.PULL_COMPLETE:v.success?m.pullCall.resolve():m.pullCall.reject(V(v.reason));break;case ct.PULL:if(!I){h.postMessage({sourceName:E,targetName:f,stream:ct.PULL_COMPLETE,streamId:b,success:!0});break}I.desiredSize<=0&&v.desiredSize>0&&I.sinkCapability.resolve(),I.desiredSize=v.desiredSize,new Promise(function(A){var r;A((r=I.onPull)==null?void 0:r.call(I))}).then(function(){h.postMessage({sourceName:E,targetName:f,stream:ct.PULL_COMPLETE,streamId:b,success:!0})},function(A){h.postMessage({sourceName:E,targetName:f,stream:ct.PULL_COMPLETE,streamId:b,reason:V(A)})});break;case ct.ENQUEUE:if((0,c.assert)(m,"enqueue should have stream controller"),m.isClosed)break;m.controller.enqueue(v.chunk);break;case ct.CLOSE:if((0,c.assert)(m,"close should have stream controller"),m.isClosed)break;m.isClosed=!0,m.controller.close(),z(this,B,Ee).call(this,m,b);break;case ct.ERROR:(0,c.assert)(m,"error should have stream controller"),m.controller.error(V(v.reason)),z(this,B,Ee).call(this,m,b);break;case ct.CANCEL_COMPLETE:v.success?m.cancelCall.resolve():m.cancelCall.reject(V(v.reason)),z(this,B,Ee).call(this,m,b);break;case ct.CANCEL:if(!I)break;new Promise(function(A){var r;A((r=I.onCancel)==null?void 0:r.call(I,V(v.reason)))}).then(function(){h.postMessage({sourceName:E,targetName:f,stream:ct.CANCEL_COMPLETE,streamId:b,success:!0})},function(A){h.postMessage({sourceName:E,targetName:f,stream:ct.CANCEL_COMPLETE,streamId:b,reason:V(A)})}),I.sinkCapability.reject(V(v.reason)),I.isCancelled=!0,delete this.streamSinks[b];break;default:throw new Error("Unexpected stream case")}},Ee=async function(v,b){var E,f,h;await Promise.allSettled([(E=v.startCall)==null?void 0:E.promise,(f=v.pullCall)==null?void 0:f.promise,(h=v.cancelCall)==null?void 0:h.promise]),delete this.streamControllers[b]},d.MessageHandler=mt},(St,d,lt)=>{var ct,V;Object.defineProperty(d,"__esModule",{value:!0}),d.Metadata=void 0;var c=lt(1);class x{constructor({parsedData:B,rawData:R}){Q(this,ct);Q(this,V);et(this,ct,B),et(this,V,R)}getRaw(){return t(this,V)}get(B){return t(this,ct).get(B)??null}getAll(){return(0,c.objectFromMap)(t(this,ct))}has(B){return t(this,ct).has(B)}}ct=new WeakMap,V=new WeakMap,d.Metadata=x},(St,d,lt)=>{var B,R,g,N,O,v,Ye;Object.defineProperty(d,"__esModule",{value:!0}),d.OptionalContentConfig=void 0;var c=lt(1),x=lt(8);const ct=Symbol("INTERNAL");class V{constructor(f,h){Q(this,B,!0);this.name=f,this.intent=h}get visible(){return t(this,B)}_setVisible(f,h){f!==ct&&(0,c.unreachable)("Internal method `_setVisible` called."),et(this,B,h)}}B=new WeakMap;class mt{constructor(f){Q(this,v);Q(this,R,null);Q(this,g,new Map);Q(this,N,null);Q(this,O,null);if(this.name=null,this.creator=null,f!==null){this.name=f.name,this.creator=f.creator,et(this,O,f.order);for(const h of f.groups)t(this,g).set(h.id,new V(h.name,h.intent));if(f.baseState==="OFF")for(const h of t(this,g).values())h._setVisible(ct,!1);for(const h of f.on)t(this,g).get(h)._setVisible(ct,!0);for(const h of f.off)t(this,g).get(h)._setVisible(ct,!1);et(this,N,this.getHash())}}isVisible(f){if(t(this,g).size===0)return!0;if(!f)return(0,c.warn)("Optional content group not defined."),!0;if(f.type==="OCG")return t(this,g).has(f.id)?t(this,g).get(f.id).visible:((0,c.warn)(`Optional content group not found: ${f.id}`),!0);if(f.type==="OCMD"){if(f.expression)return z(this,v,Ye).call(this,f.expression);if(!f.policy||f.policy==="AnyOn"){for(const h of f.ids){if(!t(this,g).has(h))return(0,c.warn)(`Optional content group not found: ${h}`),!0;if(t(this,g).get(h).visible)return!0}return!1}else if(f.policy==="AllOn"){for(const h of f.ids){if(!t(this,g).has(h))return(0,c.warn)(`Optional content group not found: ${h}`),!0;if(!t(this,g).get(h).visible)return!1}return!0}else if(f.policy==="AnyOff"){for(const h of f.ids){if(!t(this,g).has(h))return(0,c.warn)(`Optional content group not found: ${h}`),!0;if(!t(this,g).get(h).visible)return!0}return!1}else if(f.policy==="AllOff"){for(const h of f.ids){if(!t(this,g).has(h))return(0,c.warn)(`Optional content group not found: ${h}`),!0;if(t(this,g).get(h).visible)return!1}return!0}return(0,c.warn)(`Unknown optional content policy ${f.policy}.`),!0}return(0,c.warn)(`Unknown group type ${f.type}.`),!0}setVisibility(f,h=!0){if(!t(this,g).has(f)){(0,c.warn)(`Optional content group not found: ${f}`);return}t(this,g).get(f)._setVisible(ct,!!h),et(this,R,null)}get hasInitialVisibility(){return t(this,N)===null||this.getHash()===t(this,N)}getOrder(){return t(this,g).size?t(this,O)?t(this,O).slice():[...t(this,g).keys()]:null}getGroups(){return t(this,g).size>0?(0,c.objectFromMap)(t(this,g)):null}getGroup(f){return t(this,g).get(f)||null}getHash(){if(t(this,R)!==null)return t(this,R);const f=new x.MurmurHash3_64;for(const[h,m]of t(this,g))f.update(`${h}:${m.visible}`);return et(this,R,f.hexdigest())}}R=new WeakMap,g=new WeakMap,N=new WeakMap,O=new WeakMap,v=new WeakSet,Ye=function(f){const h=f.length;if(h<2)return!0;const m=f[0];for(let I=1;I<h;I++){const A=f[I];let r;if(Array.isArray(A))r=z(this,v,Ye).call(this,A);else if(t(this,g).has(A))r=t(this,g).get(A).visible;else return(0,c.warn)(`Optional content group not found: ${A}`),!0;switch(m){case"And":if(!r)return!1;break;case"Or":if(r)return!0;break;case"Not":return!r;default:return!0}}return m==="And"},d.OptionalContentConfig=mt},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.PDFDataTransportStream=void 0;var c=lt(1),x=lt(6);class ct{constructor({length:R,initialData:g,progressiveDone:N=!1,contentDispositionFilename:O=null,disableRange:v=!1,disableStream:b=!1},E){if((0,c.assert)(E,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.'),this._queuedChunks=[],this._progressiveDone=N,this._contentDispositionFilename=O,(g==null?void 0:g.length)>0){const f=g instanceof Uint8Array&&g.byteLength===g.buffer.byteLength?g.buffer:new Uint8Array(g).buffer;this._queuedChunks.push(f)}this._pdfDataRangeTransport=E,this._isStreamingSupported=!b,this._isRangeSupported=!v,this._contentLength=R,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener((f,h)=>{this._onReceiveData({begin:f,chunk:h})}),this._pdfDataRangeTransport.addProgressListener((f,h)=>{this._onProgress({loaded:f,total:h})}),this._pdfDataRangeTransport.addProgressiveReadListener(f=>{this._onReceiveData({chunk:f})}),this._pdfDataRangeTransport.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),this._pdfDataRangeTransport.transportReady()}_onReceiveData({begin:R,chunk:g}){const N=g instanceof Uint8Array&&g.byteLength===g.buffer.byteLength?g.buffer:new Uint8Array(g).buffer;if(R===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(N):this._queuedChunks.push(N);else{const O=this._rangeReaders.some(function(v){return v._begin!==R?!1:(v._enqueue(N),!0)});(0,c.assert)(O,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){var R;return((R=this._fullRequestReader)==null?void 0:R._loaded)??0}_onProgress(R){var g,N,O,v;R.total===void 0?(N=(g=this._rangeReaders[0])==null?void 0:g.onProgress)==null||N.call(g,{loaded:R.loaded}):(v=(O=this._fullRequestReader)==null?void 0:O.onProgress)==null||v.call(O,{loaded:R.loaded,total:R.total})}_onProgressiveDone(){var R;(R=this._fullRequestReader)==null||R.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(R){const g=this._rangeReaders.indexOf(R);g>=0&&this._rangeReaders.splice(g,1)}getFullReader(){(0,c.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const R=this._queuedChunks;return this._queuedChunks=null,new V(this,R,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(R,g){if(g<=this._progressiveDataLength)return null;const N=new mt(this,R,g);return this._pdfDataRangeTransport.requestDataRange(R,g),this._rangeReaders.push(N),N}cancelAllRequests(R){var g;(g=this._fullRequestReader)==null||g.cancel(R);for(const N of this._rangeReaders.slice(0))N.cancel(R);this._pdfDataRangeTransport.abort()}}d.PDFDataTransportStream=ct;class V{constructor(R,g,N=!1,O=null){this._stream=R,this._done=N||!1,this._filename=(0,x.isPdfFile)(O)?O:null,this._queuedChunks=g||[],this._loaded=0;for(const v of this._queuedChunks)this._loaded+=v.byteLength;this._requests=[],this._headersReady=Promise.resolve(),R._fullRequestReader=this,this.onProgress=null}_enqueue(R){this._done||(this._requests.length>0?this._requests.shift().resolve({value:R,done:!1}):this._queuedChunks.push(R),this._loaded+=R.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const R=new c.PromiseCapability;return this._requests.push(R),R.promise}cancel(R){this._done=!0;for(const g of this._requests)g.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class mt{constructor(R,g,N){this._stream=R,this._begin=g,this._end=N,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(R){if(!this._done){if(this._requests.length===0)this._queuedChunk=R;else{this._requests.shift().resolve({value:R,done:!1});for(const N of this._requests)N.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const g=this._queuedChunk;return this._queuedChunk=null,{value:g,done:!1}}if(this._done)return{value:void 0,done:!0};const R=new c.PromiseCapability;return this._requests.push(R),R.promise}cancel(R){this._done=!0;for(const g of this._requests)g.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.PDFFetchStream=void 0;var c=lt(1),x=lt(20);function ct(N,O,v){return{method:"GET",headers:N,signal:v.signal,mode:"cors",credentials:O?"include":"same-origin",redirect:"follow"}}function V(N){const O=new Headers;for(const v in N){const b=N[v];b!==void 0&&O.append(v,b)}return O}function mt(N){return N instanceof Uint8Array?N.buffer:N instanceof ArrayBuffer?N:((0,c.warn)(`getArrayBuffer - unexpected data format: ${N}`),new Uint8Array(N).buffer)}class B{constructor(O){this.source=O,this.isHttp=/^https?:/i.test(O.url),this.httpHeaders=this.isHttp&&O.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var O;return((O=this._fullRequestReader)==null?void 0:O._loaded)??0}getFullReader(){return(0,c.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new R(this),this._fullRequestReader}getRangeReader(O,v){if(v<=this._progressiveDataLength)return null;const b=new g(this,O,v);return this._rangeRequestReaders.push(b),b}cancelAllRequests(O){var v;(v=this._fullRequestReader)==null||v.cancel(O);for(const b of this._rangeRequestReaders.slice(0))b.cancel(O)}}d.PDFFetchStream=B;class R{constructor(O){this._stream=O,this._reader=null,this._loaded=0,this._filename=null;const v=O.source;this._withCredentials=v.withCredentials||!1,this._contentLength=v.length,this._headersCapability=new c.PromiseCapability,this._disableRange=v.disableRange||!1,this._rangeChunkSize=v.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!v.disableStream,this._isRangeSupported=!v.disableRange,this._headers=V(this._stream.httpHeaders);const b=v.url;fetch(b,ct(this._headers,this._withCredentials,this._abortController)).then(E=>{if(!(0,x.validateResponseStatus)(E.status))throw(0,x.createResponseStatusError)(E.status,b);this._reader=E.body.getReader(),this._headersCapability.resolve();const f=I=>E.headers.get(I),{allowRangeRequests:h,suggestedLength:m}=(0,x.validateRangeRequestCapabilities)({getResponseHeader:f,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=h,this._contentLength=m||this._contentLength,this._filename=(0,x.extractFilenameFromHeader)(f),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new c.AbortException("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var b;await this._headersCapability.promise;const{value:O,done:v}=await this._reader.read();return v?{value:O,done:v}:(this._loaded+=O.byteLength,(b=this.onProgress)==null||b.call(this,{loaded:this._loaded,total:this._contentLength}),{value:mt(O),done:!1})}cancel(O){var v;(v=this._reader)==null||v.cancel(O),this._abortController.abort()}}class g{constructor(O,v,b){this._stream=O,this._reader=null,this._loaded=0;const E=O.source;this._withCredentials=E.withCredentials||!1,this._readCapability=new c.PromiseCapability,this._isStreamingSupported=!E.disableStream,this._abortController=new AbortController,this._headers=V(this._stream.httpHeaders),this._headers.append("Range",`bytes=${v}-${b-1}`);const f=E.url;fetch(f,ct(this._headers,this._withCredentials,this._abortController)).then(h=>{if(!(0,x.validateResponseStatus)(h.status))throw(0,x.createResponseStatusError)(h.status,f);this._readCapability.resolve(),this._reader=h.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){var b;await this._readCapability.promise;const{value:O,done:v}=await this._reader.read();return v?{value:O,done:v}:(this._loaded+=O.byteLength,(b=this.onProgress)==null||b.call(this,{loaded:this._loaded}),{value:mt(O),done:!1})}cancel(O){var v;(v=this._reader)==null||v.cancel(O),this._abortController.abort()}}},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.createResponseStatusError=B,d.extractFilenameFromHeader=mt,d.validateRangeRequestCapabilities=V,d.validateResponseStatus=R;var c=lt(1),x=lt(21),ct=lt(6);function V({getResponseHeader:g,isHttp:N,rangeChunkSize:O,disableRange:v}){const b={allowRangeRequests:!1,suggestedLength:void 0},E=parseInt(g("Content-Length"),10);return!Number.isInteger(E)||(b.suggestedLength=E,E<=2*O)||v||!N||g("Accept-Ranges")!=="bytes"||(g("Content-Encoding")||"identity")!=="identity"||(b.allowRangeRequests=!0),b}function mt(g){const N=g("Content-Disposition");if(N){let O=(0,x.getFilenameFromContentDispositionHeader)(N);if(O.includes("%"))try{O=decodeURIComponent(O)}catch{}if((0,ct.isPdfFile)(O))return O}return null}function B(g,N){return g===404||g===0&&N.startsWith("file:")?new c.MissingPDFException('Missing PDF "'+N+'".'):new c.UnexpectedResponseException(`Unexpected server response (${g}) while retrieving PDF "${N}".`,g)}function R(g){return g===200||g===206}},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.getFilenameFromContentDispositionHeader=x;var c=lt(1);function x(ct){let V=!0,mt=B("filename\\*","i").exec(ct);if(mt){mt=mt[1];let E=O(mt);return E=unescape(E),E=v(E),E=b(E),g(E)}if(mt=N(ct),mt){const E=b(mt);return g(E)}if(mt=B("filename","i").exec(ct),mt){mt=mt[1];let E=O(mt);return E=b(E),g(E)}function B(E,f){return new RegExp("(?:^|;)\\s*"+E+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',f)}function R(E,f){if(E){if(!/^[\x00-\xFF]+$/.test(f))return f;try{const h=new TextDecoder(E,{fatal:!0}),m=(0,c.stringToBytes)(f);f=h.decode(m),V=!1}catch{}}return f}function g(E){return V&&/[\x80-\xff]/.test(E)&&(E=R("utf-8",E),V&&(E=R("iso-8859-1",E))),E}function N(E){const f=[];let h;const m=B("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(h=m.exec(E))!==null;){let[,A,r,l]=h;if(A=parseInt(A,10),A in f){if(A===0)break;continue}f[A]=[r,l]}const I=[];for(let A=0;A<f.length&&A in f;++A){let[r,l]=f[A];l=O(l),r&&(l=unescape(l),A===0&&(l=v(l))),I.push(l)}return I.join("")}function O(E){if(E.startsWith('"')){const f=E.slice(1).split('\\"');for(let h=0;h<f.length;++h){const m=f[h].indexOf('"');m!==-1&&(f[h]=f[h].slice(0,m),f.length=h+1),f[h]=f[h].replaceAll(/\\(.)/g,"$1")}E=f.join('"')}return E}function v(E){const f=E.indexOf("'");if(f===-1)return E;const h=E.slice(0,f),I=E.slice(f+1).replace(/^[^']*'/,"");return R(h,I)}function b(E){return!E.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(E)?E:E.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(f,h,m,I){if(m==="q"||m==="Q")return I=I.replaceAll("_"," "),I=I.replaceAll(/=([0-9a-fA-F]{2})/g,function(A,r){return String.fromCharCode(parseInt(r,16))}),R(h,I);try{I=atob(I)}catch{}return R(h,I)})}return""}},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.PDFNetworkStream=void 0;var c=lt(1),x=lt(20);const ct=200,V=206;function mt(O){const v=O.response;return typeof v!="string"?v:(0,c.stringToBytes)(v).buffer}class B{constructor(v,b={}){this.url=v,this.isHttp=/^https?:/i.test(v),this.httpHeaders=this.isHttp&&b.httpHeaders||Object.create(null),this.withCredentials=b.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(v,b,E){const f={begin:v,end:b};for(const h in E)f[h]=E[h];return this.request(f)}requestFull(v){return this.request(v)}request(v){const b=new XMLHttpRequest,E=this.currXhrId++,f=this.pendingRequests[E]={xhr:b};b.open("GET",this.url),b.withCredentials=this.withCredentials;for(const h in this.httpHeaders){const m=this.httpHeaders[h];m!==void 0&&b.setRequestHeader(h,m)}return this.isHttp&&"begin"in v&&"end"in v?(b.setRequestHeader("Range",`bytes=${v.begin}-${v.end-1}`),f.expectedStatus=V):f.expectedStatus=ct,b.responseType="arraybuffer",v.onError&&(b.onerror=function(h){v.onError(b.status)}),b.onreadystatechange=this.onStateChange.bind(this,E),b.onprogress=this.onProgress.bind(this,E),f.onHeadersReceived=v.onHeadersReceived,f.onDone=v.onDone,f.onError=v.onError,f.onProgress=v.onProgress,b.send(null),E}onProgress(v,b){var f;const E=this.pendingRequests[v];E&&((f=E.onProgress)==null||f.call(E,b))}onStateChange(v,b){var A,r,l;const E=this.pendingRequests[v];if(!E)return;const f=E.xhr;if(f.readyState>=2&&E.onHeadersReceived&&(E.onHeadersReceived(),delete E.onHeadersReceived),f.readyState!==4||!(v in this.pendingRequests))return;if(delete this.pendingRequests[v],f.status===0&&this.isHttp){(A=E.onError)==null||A.call(E,f.status);return}const h=f.status||ct;if(!(h===ct&&E.expectedStatus===V)&&h!==E.expectedStatus){(r=E.onError)==null||r.call(E,f.status);return}const I=mt(f);if(h===V){const s=f.getResponseHeader("Content-Range"),a=/bytes (\d+)-(\d+)\/(\d+)/.exec(s);E.onDone({begin:parseInt(a[1],10),chunk:I})}else I?E.onDone({begin:0,chunk:I}):(l=E.onError)==null||l.call(E,f.status)}getRequestXhr(v){return this.pendingRequests[v].xhr}isPendingRequest(v){return v in this.pendingRequests}abortRequest(v){const b=this.pendingRequests[v].xhr;delete this.pendingRequests[v],b.abort()}}class R{constructor(v){this._source=v,this._manager=new B(v.url,{httpHeaders:v.httpHeaders,withCredentials:v.withCredentials}),this._rangeChunkSize=v.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(v){const b=this._rangeRequestReaders.indexOf(v);b>=0&&this._rangeRequestReaders.splice(b,1)}getFullReader(){return(0,c.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new g(this._manager,this._source),this._fullRequestReader}getRangeReader(v,b){const E=new N(this._manager,v,b);return E.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(E),E}cancelAllRequests(v){var b;(b=this._fullRequestReader)==null||b.cancel(v);for(const E of this._rangeRequestReaders.slice(0))E.cancel(v)}}d.PDFNetworkStream=R;class g{constructor(v,b){this._manager=v;const E={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=b.url,this._fullRequestId=v.requestFull(E),this._headersReceivedCapability=new c.PromiseCapability,this._disableRange=b.disableRange||!1,this._contentLength=b.length,this._rangeChunkSize=b.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const v=this._fullRequestId,b=this._manager.getRequestXhr(v),E=m=>b.getResponseHeader(m),{allowRangeRequests:f,suggestedLength:h}=(0,x.validateRangeRequestCapabilities)({getResponseHeader:E,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});f&&(this._isRangeSupported=!0),this._contentLength=h||this._contentLength,this._filename=(0,x.extractFilenameFromHeader)(E),this._isRangeSupported&&this._manager.abortRequest(v),this._headersReceivedCapability.resolve()}_onDone(v){if(v&&(this._requests.length>0?this._requests.shift().resolve({value:v.chunk,done:!1}):this._cachedChunks.push(v.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const b of this._requests)b.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(v){this._storedError=(0,x.createResponseStatusError)(v,this._url),this._headersReceivedCapability.reject(this._storedError);for(const b of this._requests)b.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(v){var b;(b=this.onProgress)==null||b.call(this,{loaded:v.loaded,total:v.lengthComputable?v.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const v=new c.PromiseCapability;return this._requests.push(v),v.promise}cancel(v){this._done=!0,this._headersReceivedCapability.reject(v);for(const b of this._requests)b.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class N{constructor(v,b,E){this._manager=v;const f={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=v.url,this._requestId=v.requestRange(b,E,f),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){var v;(v=this.onClosed)==null||v.call(this,this)}_onDone(v){const b=v.chunk;this._requests.length>0?this._requests.shift().resolve({value:b,done:!1}):this._queuedChunk=b,this._done=!0;for(const E of this._requests)E.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(v){this._storedError=(0,x.createResponseStatusError)(v,this._url);for(const b of this._requests)b.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(v){var b;this.isStreamingSupported||(b=this.onProgress)==null||b.call(this,{loaded:v.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const b=this._queuedChunk;return this._queuedChunk=null,{value:b,done:!1}}if(this._done)return{value:void 0,done:!0};const v=new c.PromiseCapability;return this._requests.push(v),v.promise}cancel(v){this._done=!0;for(const b of this._requests)b.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.PDFNodeStream=void 0;var c=lt(1),x=lt(20);const ct=/^file:\/\/\/[a-zA-Z]:\//;function V(E){const f=require$$5,h=f.parse(E);return h.protocol==="file:"||h.host?h:/^[a-z]:[/\\]/i.test(E)?f.parse(`file:///${E}`):(h.host||(h.protocol="file:"),h)}class mt{constructor(f){this.source=f,this.url=V(f.url),this.isHttp=this.url.protocol==="http:"||this.url.protocol==="https:",this.isFsUrl=this.url.protocol==="file:",this.httpHeaders=this.isHttp&&f.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){var f;return((f=this._fullRequestReader)==null?void 0:f._loaded)??0}getFullReader(){return(0,c.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new v(this):new N(this),this._fullRequestReader}getRangeReader(f,h){if(h<=this._progressiveDataLength)return null;const m=this.isFsUrl?new b(this,f,h):new O(this,f,h);return this._rangeRequestReaders.push(m),m}cancelAllRequests(f){var h;(h=this._fullRequestReader)==null||h.cancel(f);for(const m of this._rangeRequestReaders.slice(0))m.cancel(f)}}d.PDFNodeStream=mt;class B{constructor(f){this._url=f.url,this._done=!1,this._storedError=null,this.onProgress=null;const h=f.source;this._contentLength=h.length,this._loaded=0,this._filename=null,this._disableRange=h.disableRange||!1,this._rangeChunkSize=h.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!h.disableStream,this._isRangeSupported=!h.disableRange,this._readableStream=null,this._readCapability=new c.PromiseCapability,this._headersCapability=new c.PromiseCapability}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){var m;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const f=this._readableStream.read();return f===null?(this._readCapability=new c.PromiseCapability,this.read()):(this._loaded+=f.length,(m=this.onProgress)==null||m.call(this,{loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(f).buffer,done:!1})}cancel(f){if(!this._readableStream){this._error(f);return}this._readableStream.destroy(f)}_error(f){this._storedError=f,this._readCapability.resolve()}_setReadableStream(f){this._readableStream=f,f.on("readable",()=>{this._readCapability.resolve()}),f.on("end",()=>{f.destroy(),this._done=!0,this._readCapability.resolve()}),f.on("error",h=>{this._error(h)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new c.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class R{constructor(f){this._url=f.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=new c.PromiseCapability;const h=f.source;this._isStreamingSupported=!h.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){var m;if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const f=this._readableStream.read();return f===null?(this._readCapability=new c.PromiseCapability,this.read()):(this._loaded+=f.length,(m=this.onProgress)==null||m.call(this,{loaded:this._loaded}),{value:new Uint8Array(f).buffer,done:!1})}cancel(f){if(!this._readableStream){this._error(f);return}this._readableStream.destroy(f)}_error(f){this._storedError=f,this._readCapability.resolve()}_setReadableStream(f){this._readableStream=f,f.on("readable",()=>{this._readCapability.resolve()}),f.on("end",()=>{f.destroy(),this._done=!0,this._readCapability.resolve()}),f.on("error",h=>{this._error(h)}),this._storedError&&this._readableStream.destroy(this._storedError)}}function g(E,f){return{protocol:E.protocol,auth:E.auth,host:E.hostname,port:E.port,path:E.path,method:"GET",headers:f}}class N extends B{constructor(f){super(f);const h=m=>{if(m.statusCode===404){const l=new c.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=l,this._headersCapability.reject(l);return}this._headersCapability.resolve(),this._setReadableStream(m);const I=l=>this._readableStream.headers[l.toLowerCase()],{allowRangeRequests:A,suggestedLength:r}=(0,x.validateRangeRequestCapabilities)({getResponseHeader:I,isHttp:f.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=A,this._contentLength=r||this._contentLength,this._filename=(0,x.extractFilenameFromHeader)(I)};if(this._request=null,this._url.protocol==="http:"){const m=require$$5;this._request=m.request(g(this._url,f.httpHeaders),h)}else{const m=require$$5;this._request=m.request(g(this._url,f.httpHeaders),h)}this._request.on("error",m=>{this._storedError=m,this._headersCapability.reject(m)}),this._request.end()}}class O extends R{constructor(f,h,m){super(f),this._httpHeaders={};for(const A in f.httpHeaders){const r=f.httpHeaders[A];r!==void 0&&(this._httpHeaders[A]=r)}this._httpHeaders.Range=`bytes=${h}-${m-1}`;const I=A=>{if(A.statusCode===404){const r=new c.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=r;return}this._setReadableStream(A)};if(this._request=null,this._url.protocol==="http:"){const A=require$$5;this._request=A.request(g(this._url,this._httpHeaders),I)}else{const A=require$$5;this._request=A.request(g(this._url,this._httpHeaders),I)}this._request.on("error",A=>{this._storedError=A}),this._request.end()}}class v extends B{constructor(f){super(f);let h=decodeURIComponent(this._url.path);ct.test(this._url.href)&&(h=h.replace(/^\//,""));const m=require$$5;m.lstat(h,(I,A)=>{if(I){I.code==="ENOENT"&&(I=new c.MissingPDFException(`Missing PDF "${h}".`)),this._storedError=I,this._headersCapability.reject(I);return}this._contentLength=A.size,this._setReadableStream(m.createReadStream(h)),this._headersCapability.resolve()})}}class b extends R{constructor(f,h,m){super(f);let I=decodeURIComponent(this._url.path);ct.test(this._url.href)&&(I=I.replace(/^\//,""));const A=require$$5;this._setReadableStream(A.createReadStream(I,{start:h,end:m-1}))}}},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.SVGGraphics=void 0;var c=lt(6),x=lt(1);const ct={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},V="http://www.w3.org/XML/1998/namespace",mt="http://www.w3.org/1999/xlink",B=["butt","round","square"],R=["miter","round","bevel"],g=function(A,r="",l=!1){if(URL.createObjectURL&&typeof Blob<"u"&&!l)return URL.createObjectURL(new Blob([A],{type:r}));const s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let a=`data:${r};base64,`;for(let o=0,L=A.length;o<L;o+=3){const n=A[o]&255,_=A[o+1]&255,F=A[o+2]&255,nt=n>>2,W=(n&3)<<4|_>>4,q=o+1<L?(_&15)<<2|F>>6:64,j=o+2<L?F&63:64;a+=s[nt]+s[W]+s[q]+s[j]}return a},N=function(){const A=new Uint8Array([137,80,78,71,13,10,26,10]),r=12,l=new Int32Array(256);for(let F=0;F<256;F++){let nt=F;for(let W=0;W<8;W++)nt=nt&1?3988292384^nt>>1&2147483647:nt>>1&2147483647;l[F]=nt}function s(F,nt,W){let q=-1;for(let j=nt;j<W;j++){const rt=(q^F[j])&255,C=l[rt];q=q>>>8^C}return q^-1}function a(F,nt,W,q){let j=q;const rt=nt.length;W[j]=rt>>24&255,W[j+1]=rt>>16&255,W[j+2]=rt>>8&255,W[j+3]=rt&255,j+=4,W[j]=F.charCodeAt(0)&255,W[j+1]=F.charCodeAt(1)&255,W[j+2]=F.charCodeAt(2)&255,W[j+3]=F.charCodeAt(3)&255,j+=4,W.set(nt,j),j+=nt.length;const C=s(W,q+4,j);W[j]=C>>24&255,W[j+1]=C>>16&255,W[j+2]=C>>8&255,W[j+3]=C&255}function o(F,nt,W){let q=1,j=0;for(let rt=nt;rt<W;++rt)q=(q+(F[rt]&255))%65521,j=(j+q)%65521;return j<<16|q}function L(F){if(!x.isNodeJS)return n(F);try{const nt=parseInt(process.versions.node)>=8?F:Buffer.from(F),W=require$$5.deflateSync(nt,{level:9});return W instanceof Uint8Array?W:new Uint8Array(W)}catch(nt){(0,x.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+nt)}return n(F)}function n(F){let nt=F.length;const W=65535,q=Math.ceil(nt/W),j=new Uint8Array(2+nt+q*5+4);let rt=0;j[rt++]=120,j[rt++]=156;let C=0;for(;nt>W;)j[rt++]=0,j[rt++]=255,j[rt++]=255,j[rt++]=0,j[rt++]=0,j.set(F.subarray(C,C+W),rt),rt+=W,C+=W,nt-=W;j[rt++]=1,j[rt++]=nt&255,j[rt++]=nt>>8&255,j[rt++]=~nt&65535&255,j[rt++]=(~nt&65535)>>8&255,j.set(F.subarray(C),rt),rt+=F.length-C;const U=o(F,0,F.length);return j[rt++]=U>>24&255,j[rt++]=U>>16&255,j[rt++]=U>>8&255,j[rt++]=U&255,j}function _(F,nt,W,q){const j=F.width,rt=F.height;let C,U,Y;const S=F.data;switch(nt){case x.ImageKind.GRAYSCALE_1BPP:U=0,C=1,Y=j+7>>3;break;case x.ImageKind.RGB_24BPP:U=2,C=8,Y=j*3;break;case x.ImageKind.RGBA_32BPP:U=6,C=8,Y=j*4;break;default:throw new Error("invalid format")}const e=new Uint8Array((1+Y)*rt);let i=0,u=0;for(let ht=0;ht<rt;++ht)e[i++]=0,e.set(S.subarray(u,u+Y),i),u+=Y,i+=Y;if(nt===x.ImageKind.GRAYSCALE_1BPP&&q){i=0;for(let ht=0;ht<rt;ht++){i++;for(let dt=0;dt<Y;dt++)e[i++]^=255}}const T=new Uint8Array([j>>24&255,j>>16&255,j>>8&255,j&255,rt>>24&255,rt>>16&255,rt>>8&255,rt&255,C,U,0,0,0]),P=L(e),k=A.length+r*3+T.length+P.length,G=new Uint8Array(k);let it=0;return G.set(A,it),it+=A.length,a("IHDR",T,G,it),it+=r+T.length,a("IDATA",P,G,it),it+=r+P.length,a("IEND",new Uint8Array(0),G,it),g(G,"image/png",W)}return function(nt,W,q){const j=nt.kind===void 0?x.ImageKind.GRAYSCALE_1BPP:nt.kind;return _(nt,j,W,q)}}();class O{constructor(){this.fontSizeScale=1,this.fontWeight=ct.fontWeight,this.fontSize=0,this.textMatrix=x.IDENTITY_MATRIX,this.fontMatrix=x.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=x.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=ct.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(r,l){this.x=r,this.y=l}}function v(A){let r=[];const l=[];for(const s of A){if(s.fn==="save"){r.push({fnId:92,fn:"group",items:[]}),l.push(r),r=r.at(-1).items;continue}s.fn==="restore"?r=l.pop():r.push(s)}return r}function b(A){if(Number.isInteger(A))return A.toString();const r=A.toFixed(10);let l=r.length-1;if(r[l]!=="0")return r;do l--;while(r[l]==="0");return r.substring(0,r[l]==="."?l:l+1)}function E(A){if(A[4]===0&&A[5]===0){if(A[1]===0&&A[2]===0)return A[0]===1&&A[3]===1?"":`scale(${b(A[0])} ${b(A[3])})`;if(A[0]===A[3]&&A[1]===-A[2]){const r=Math.acos(A[0])*180/Math.PI;return`rotate(${b(r)})`}}else if(A[0]===1&&A[1]===0&&A[2]===0&&A[3]===1)return`translate(${b(A[4])} ${b(A[5])})`;return`matrix(${b(A[0])} ${b(A[1])} ${b(A[2])} ${b(A[3])} ${b(A[4])} ${b(A[5])})`}let f=0,h=0,m=0;class I{constructor(r,l,s=!1){(0,c.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future."),this.svgFactory=new c.DOMSVGFactory,this.current=new O,this.transformMatrix=x.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=r,this.objs=l,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!s,this._operatorIdMapping=[];for(const a in x.OPS)this._operatorIdMapping[x.OPS[a]]=a}getObject(r,l=null){return typeof r=="string"?r.startsWith("g_")?this.commonObjs.get(r):this.objs.get(r):l}save(){this.transformStack.push(this.transformMatrix);const r=this.current;this.extraStack.push(r),this.current=r.clone()}restore(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}group(r){this.save(),this.executeOpTree(r),this.restore()}loadDependencies(r){const l=r.fnArray,s=r.argsArray;for(let a=0,o=l.length;a<o;a++)if(l[a]===x.OPS.dependency)for(const L of s[a]){const n=L.startsWith("g_")?this.commonObjs:this.objs,_=new Promise(F=>{n.get(L,F)});this.current.dependencies.push(_)}return Promise.all(this.current.dependencies)}transform(r,l,s,a,o,L){const n=[r,l,s,a,o,L];this.transformMatrix=x.Util.transform(this.transformMatrix,n),this.tgrp=null}getSVG(r,l){this.viewport=l;const s=this._initialize(l);return this.loadDependencies(r).then(()=>(this.transformMatrix=x.IDENTITY_MATRIX,this.executeOpTree(this.convertOpList(r)),s))}convertOpList(r){const l=this._operatorIdMapping,s=r.argsArray,a=r.fnArray,o=[];for(let L=0,n=a.length;L<n;L++){const _=a[L];o.push({fnId:_,fn:l[_],args:s[L]})}return v(o)}executeOpTree(r){for(const l of r){const s=l.fn,a=l.fnId,o=l.args;switch(a|0){case x.OPS.beginText:this.beginText();break;case x.OPS.dependency:break;case x.OPS.setLeading:this.setLeading(o);break;case x.OPS.setLeadingMoveText:this.setLeadingMoveText(o[0],o[1]);break;case x.OPS.setFont:this.setFont(o);break;case x.OPS.showText:this.showText(o[0]);break;case x.OPS.showSpacedText:this.showText(o[0]);break;case x.OPS.endText:this.endText();break;case x.OPS.moveText:this.moveText(o[0],o[1]);break;case x.OPS.setCharSpacing:this.setCharSpacing(o[0]);break;case x.OPS.setWordSpacing:this.setWordSpacing(o[0]);break;case x.OPS.setHScale:this.setHScale(o[0]);break;case x.OPS.setTextMatrix:this.setTextMatrix(o[0],o[1],o[2],o[3],o[4],o[5]);break;case x.OPS.setTextRise:this.setTextRise(o[0]);break;case x.OPS.setTextRenderingMode:this.setTextRenderingMode(o[0]);break;case x.OPS.setLineWidth:this.setLineWidth(o[0]);break;case x.OPS.setLineJoin:this.setLineJoin(o[0]);break;case x.OPS.setLineCap:this.setLineCap(o[0]);break;case x.OPS.setMiterLimit:this.setMiterLimit(o[0]);break;case x.OPS.setFillRGBColor:this.setFillRGBColor(o[0],o[1],o[2]);break;case x.OPS.setStrokeRGBColor:this.setStrokeRGBColor(o[0],o[1],o[2]);break;case x.OPS.setStrokeColorN:this.setStrokeColorN(o);break;case x.OPS.setFillColorN:this.setFillColorN(o);break;case x.OPS.shadingFill:this.shadingFill(o[0]);break;case x.OPS.setDash:this.setDash(o[0],o[1]);break;case x.OPS.setRenderingIntent:this.setRenderingIntent(o[0]);break;case x.OPS.setFlatness:this.setFlatness(o[0]);break;case x.OPS.setGState:this.setGState(o[0]);break;case x.OPS.fill:this.fill();break;case x.OPS.eoFill:this.eoFill();break;case x.OPS.stroke:this.stroke();break;case x.OPS.fillStroke:this.fillStroke();break;case x.OPS.eoFillStroke:this.eoFillStroke();break;case x.OPS.clip:this.clip("nonzero");break;case x.OPS.eoClip:this.clip("evenodd");break;case x.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case x.OPS.paintImageXObject:this.paintImageXObject(o[0]);break;case x.OPS.paintInlineImageXObject:this.paintInlineImageXObject(o[0]);break;case x.OPS.paintImageMaskXObject:this.paintImageMaskXObject(o[0]);break;case x.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(o[0],o[1]);break;case x.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case x.OPS.closePath:this.closePath();break;case x.OPS.closeStroke:this.closeStroke();break;case x.OPS.closeFillStroke:this.closeFillStroke();break;case x.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case x.OPS.nextLine:this.nextLine();break;case x.OPS.transform:this.transform(o[0],o[1],o[2],o[3],o[4],o[5]);break;case x.OPS.constructPath:this.constructPath(o[0],o[1]);break;case x.OPS.endPath:this.endPath();break;case 92:this.group(l.items);break;default:(0,x.warn)(`Unimplemented operator ${s}`);break}}}setWordSpacing(r){this.current.wordSpacing=r}setCharSpacing(r){this.current.charSpacing=r}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(r,l,s,a,o,L){const n=this.current;n.textMatrix=n.lineMatrix=[r,l,s,a,o,L],n.textMatrixScale=Math.hypot(r,l),n.x=n.lineX=0,n.y=n.lineY=0,n.xcoords=[],n.ycoords=[],n.tspan=this.svgFactory.createElement("svg:tspan"),n.tspan.setAttributeNS(null,"font-family",n.fontFamily),n.tspan.setAttributeNS(null,"font-size",`${b(n.fontSize)}px`),n.tspan.setAttributeNS(null,"y",b(-n.y)),n.txtElement=this.svgFactory.createElement("svg:text"),n.txtElement.append(n.tspan)}beginText(){const r=this.current;r.x=r.lineX=0,r.y=r.lineY=0,r.textMatrix=x.IDENTITY_MATRIX,r.lineMatrix=x.IDENTITY_MATRIX,r.textMatrixScale=1,r.tspan=this.svgFactory.createElement("svg:tspan"),r.txtElement=this.svgFactory.createElement("svg:text"),r.txtgrp=this.svgFactory.createElement("svg:g"),r.xcoords=[],r.ycoords=[]}moveText(r,l){const s=this.current;s.x=s.lineX+=r,s.y=s.lineY+=l,s.xcoords=[],s.ycoords=[],s.tspan=this.svgFactory.createElement("svg:tspan"),s.tspan.setAttributeNS(null,"font-family",s.fontFamily),s.tspan.setAttributeNS(null,"font-size",`${b(s.fontSize)}px`),s.tspan.setAttributeNS(null,"y",b(-s.y))}showText(r){const l=this.current,s=l.font,a=l.fontSize;if(a===0)return;const o=l.fontSizeScale,L=l.charSpacing,n=l.wordSpacing,_=l.fontDirection,F=l.textHScale*_,nt=s.vertical,W=nt?1:-1,q=s.defaultVMetrics,j=a*l.fontMatrix[0];let rt=0;for(const Y of r){if(Y===null){rt+=_*n;continue}else if(typeof Y=="number"){rt+=W*Y*a/1e3;continue}const S=(Y.isSpace?n:0)+L,e=Y.fontChar;let i,u,T=Y.width;if(nt){let k;const G=Y.vmetric||q;k=Y.vmetric?G[1]:T*.5,k=-k*j;const it=G[2]*j;T=G?-G[0]:T,i=k/o,u=(rt+it)/o}else i=rt/o,u=0;(Y.isInFont||s.missingFile)&&(l.xcoords.push(l.x+i),nt&&l.ycoords.push(-l.y+u),l.tspan.textContent+=e);const P=nt?T*j-S*_:T*j+S*_;rt+=P}l.tspan.setAttributeNS(null,"x",l.xcoords.map(b).join(" ")),nt?l.tspan.setAttributeNS(null,"y",l.ycoords.map(b).join(" ")):l.tspan.setAttributeNS(null,"y",b(-l.y)),nt?l.y-=rt:l.x+=rt*F,l.tspan.setAttributeNS(null,"font-family",l.fontFamily),l.tspan.setAttributeNS(null,"font-size",`${b(l.fontSize)}px`),l.fontStyle!==ct.fontStyle&&l.tspan.setAttributeNS(null,"font-style",l.fontStyle),l.fontWeight!==ct.fontWeight&&l.tspan.setAttributeNS(null,"font-weight",l.fontWeight);const C=l.textRenderingMode&x.TextRenderingMode.FILL_STROKE_MASK;if(C===x.TextRenderingMode.FILL||C===x.TextRenderingMode.FILL_STROKE?(l.fillColor!==ct.fillColor&&l.tspan.setAttributeNS(null,"fill",l.fillColor),l.fillAlpha<1&&l.tspan.setAttributeNS(null,"fill-opacity",l.fillAlpha)):l.textRenderingMode===x.TextRenderingMode.ADD_TO_PATH?l.tspan.setAttributeNS(null,"fill","transparent"):l.tspan.setAttributeNS(null,"fill","none"),C===x.TextRenderingMode.STROKE||C===x.TextRenderingMode.FILL_STROKE){const Y=1/(l.textMatrixScale||1);this._setStrokeAttributes(l.tspan,Y)}let U=l.textMatrix;l.textRise!==0&&(U=U.slice(),U[5]+=l.textRise),l.txtElement.setAttributeNS(null,"transform",`${E(U)} scale(${b(F)}, -1)`),l.txtElement.setAttributeNS(V,"xml:space","preserve"),l.txtElement.append(l.tspan),l.txtgrp.append(l.txtElement),this._ensureTransformGroup().append(l.txtElement)}setLeadingMoveText(r,l){this.setLeading(-l),this.moveText(r,l)}addFontStyle(r){if(!r.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.append(this.cssStyle));const l=g(r.data,r.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${r.loadedName}"; src: url(${l}); }
`}setFont(r){const l=this.current,s=this.commonObjs.get(r[0]);let a=r[1];l.font=s,this.embedFonts&&!s.missingFile&&!this.embeddedFonts[s.loadedName]&&(this.addFontStyle(s),this.embeddedFonts[s.loadedName]=s),l.fontMatrix=s.fontMatrix||x.FONT_IDENTITY_MATRIX;let o="normal";s.black?o="900":s.bold&&(o="bold");const L=s.italic?"italic":"normal";a<0?(a=-a,l.fontDirection=-1):l.fontDirection=1,l.fontSize=a,l.fontFamily=s.loadedName,l.fontWeight=o,l.fontStyle=L,l.tspan=this.svgFactory.createElement("svg:tspan"),l.tspan.setAttributeNS(null,"y",b(-l.y)),l.xcoords=[],l.ycoords=[]}endText(){var l;const r=this.current;r.textRenderingMode&x.TextRenderingMode.ADD_TO_PATH_FLAG&&((l=r.txtElement)!=null&&l.hasChildNodes())&&(r.element=r.txtElement,this.clip("nonzero"),this.endPath())}setLineWidth(r){r>0&&(this.current.lineWidth=r)}setLineCap(r){this.current.lineCap=B[r]}setLineJoin(r){this.current.lineJoin=R[r]}setMiterLimit(r){this.current.miterLimit=r}setStrokeAlpha(r){this.current.strokeAlpha=r}setStrokeRGBColor(r,l,s){this.current.strokeColor=x.Util.makeHexColor(r,l,s)}setFillAlpha(r){this.current.fillAlpha=r}setFillRGBColor(r,l,s){this.current.fillColor=x.Util.makeHexColor(r,l,s),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[],this.current.ycoords=[]}setStrokeColorN(r){this.current.strokeColor=this._makeColorN_Pattern(r)}setFillColorN(r){this.current.fillColor=this._makeColorN_Pattern(r)}shadingFill(r){const{width:l,height:s}=this.viewport,a=x.Util.inverseTransform(this.transformMatrix),[o,L,n,_]=x.Util.getAxialAlignedBoundingBox([0,0,l,s],a),F=this.svgFactory.createElement("svg:rect");F.setAttributeNS(null,"x",o),F.setAttributeNS(null,"y",L),F.setAttributeNS(null,"width",n-o),F.setAttributeNS(null,"height",_-L),F.setAttributeNS(null,"fill",this._makeShadingPattern(r)),this.current.fillAlpha<1&&F.setAttributeNS(null,"fill-opacity",this.current.fillAlpha),this._ensureTransformGroup().append(F)}_makeColorN_Pattern(r){return r[0]==="TilingPattern"?this._makeTilingPattern(r):this._makeShadingPattern(r)}_makeTilingPattern(r){const l=r[1],s=r[2],a=r[3]||x.IDENTITY_MATRIX,[o,L,n,_]=r[4],F=r[5],nt=r[6],W=r[7],q=`shading${m++}`,[j,rt,C,U]=x.Util.normalizeRect([...x.Util.applyTransform([o,L],a),...x.Util.applyTransform([n,_],a)]),[Y,S]=x.Util.singularValueDecompose2dScale(a),e=F*Y,i=nt*S,u=this.svgFactory.createElement("svg:pattern");u.setAttributeNS(null,"id",q),u.setAttributeNS(null,"patternUnits","userSpaceOnUse"),u.setAttributeNS(null,"width",e),u.setAttributeNS(null,"height",i),u.setAttributeNS(null,"x",`${j}`),u.setAttributeNS(null,"y",`${rt}`);const T=this.svg,P=this.transformMatrix,k=this.current.fillColor,G=this.current.strokeColor,it=this.svgFactory.create(C-j,U-rt);if(this.svg=it,this.transformMatrix=a,W===2){const ht=x.Util.makeHexColor(...l);this.current.fillColor=ht,this.current.strokeColor=ht}return this.executeOpTree(this.convertOpList(s)),this.svg=T,this.transformMatrix=P,this.current.fillColor=k,this.current.strokeColor=G,u.append(it.childNodes[0]),this.defs.append(u),`url(#${q})`}_makeShadingPattern(r){switch(typeof r=="string"&&(r=this.objs.get(r)),r[0]){case"RadialAxial":const l=`shading${m++}`,s=r[3];let a;switch(r[1]){case"axial":const o=r[4],L=r[5];a=this.svgFactory.createElement("svg:linearGradient"),a.setAttributeNS(null,"id",l),a.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),a.setAttributeNS(null,"x1",o[0]),a.setAttributeNS(null,"y1",o[1]),a.setAttributeNS(null,"x2",L[0]),a.setAttributeNS(null,"y2",L[1]);break;case"radial":const n=r[4],_=r[5],F=r[6],nt=r[7];a=this.svgFactory.createElement("svg:radialGradient"),a.setAttributeNS(null,"id",l),a.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),a.setAttributeNS(null,"cx",_[0]),a.setAttributeNS(null,"cy",_[1]),a.setAttributeNS(null,"r",nt),a.setAttributeNS(null,"fx",n[0]),a.setAttributeNS(null,"fy",n[1]),a.setAttributeNS(null,"fr",F);break;default:throw new Error(`Unknown RadialAxial type: ${r[1]}`)}for(const o of s){const L=this.svgFactory.createElement("svg:stop");L.setAttributeNS(null,"offset",o[0]),L.setAttributeNS(null,"stop-color",o[1]),a.append(L)}return this.defs.append(a),`url(#${l})`;case"Mesh":return(0,x.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${r[0]}`)}}setDash(r,l){this.current.dashArray=r,this.current.dashPhase=l}constructPath(r,l){const s=this.current;let a=s.x,o=s.y,L=[],n=0;for(const _ of r)switch(_|0){case x.OPS.rectangle:a=l[n++],o=l[n++];const F=l[n++],nt=l[n++],W=a+F,q=o+nt;L.push("M",b(a),b(o),"L",b(W),b(o),"L",b(W),b(q),"L",b(a),b(q),"Z");break;case x.OPS.moveTo:a=l[n++],o=l[n++],L.push("M",b(a),b(o));break;case x.OPS.lineTo:a=l[n++],o=l[n++],L.push("L",b(a),b(o));break;case x.OPS.curveTo:a=l[n+4],o=l[n+5],L.push("C",b(l[n]),b(l[n+1]),b(l[n+2]),b(l[n+3]),b(a),b(o)),n+=6;break;case x.OPS.curveTo2:L.push("C",b(a),b(o),b(l[n]),b(l[n+1]),b(l[n+2]),b(l[n+3])),a=l[n+2],o=l[n+3],n+=4;break;case x.OPS.curveTo3:a=l[n+2],o=l[n+3],L.push("C",b(l[n]),b(l[n+1]),b(a),b(o),b(a),b(o)),n+=4;break;case x.OPS.closePath:L.push("Z");break}L=L.join(" "),s.path&&r.length>0&&r[0]!==x.OPS.rectangle&&r[0]!==x.OPS.moveTo?L=s.path.getAttributeNS(null,"d")+L:(s.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().append(s.path)),s.path.setAttributeNS(null,"d",L),s.path.setAttributeNS(null,"fill","none"),s.element=s.path,s.setCurrentPoint(a,o)}endPath(){const r=this.current;if(r.path=null,!this.pendingClip)return;if(!r.element){this.pendingClip=null;return}const l=`clippath${f++}`,s=this.svgFactory.createElement("svg:clipPath");s.setAttributeNS(null,"id",l),s.setAttributeNS(null,"transform",E(this.transformMatrix));const a=r.element.cloneNode(!0);if(this.pendingClip==="evenodd"?a.setAttributeNS(null,"clip-rule","evenodd"):a.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,s.append(a),this.defs.append(s),r.activeClipUrl){r.clipGroup=null;for(const o of this.extraStack)o.clipGroup=null;s.setAttributeNS(null,"clip-path",r.activeClipUrl)}r.activeClipUrl=`url(#${l})`,this.tgrp=null}clip(r){this.pendingClip=r}closePath(){const r=this.current;if(r.path){const l=`${r.path.getAttributeNS(null,"d")}Z`;r.path.setAttributeNS(null,"d",l)}}setLeading(r){this.current.leading=-r}setTextRise(r){this.current.textRise=r}setTextRenderingMode(r){this.current.textRenderingMode=r}setHScale(r){this.current.textHScale=r/100}setRenderingIntent(r){}setFlatness(r){}setGState(r){for(const[l,s]of r)switch(l){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s);break;case"CA":this.setStrokeAlpha(s);break;case"ca":this.setFillAlpha(s);break;default:(0,x.warn)(`Unimplemented graphic state operator ${l}`);break}}fill(){const r=this.current;r.element&&(r.element.setAttributeNS(null,"fill",r.fillColor),r.element.setAttributeNS(null,"fill-opacity",r.fillAlpha),this.endPath())}stroke(){const r=this.current;r.element&&(this._setStrokeAttributes(r.element),r.element.setAttributeNS(null,"fill","none"),this.endPath())}_setStrokeAttributes(r,l=1){const s=this.current;let a=s.dashArray;l!==1&&a.length>0&&(a=a.map(function(o){return l*o})),r.setAttributeNS(null,"stroke",s.strokeColor),r.setAttributeNS(null,"stroke-opacity",s.strokeAlpha),r.setAttributeNS(null,"stroke-miterlimit",b(s.miterLimit)),r.setAttributeNS(null,"stroke-linecap",s.lineCap),r.setAttributeNS(null,"stroke-linejoin",s.lineJoin),r.setAttributeNS(null,"stroke-width",b(l*s.lineWidth)+"px"),r.setAttributeNS(null,"stroke-dasharray",a.map(b).join(" ")),r.setAttributeNS(null,"stroke-dashoffset",b(l*s.dashPhase)+"px")}eoFill(){var r;(r=this.current.element)==null||r.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}fillStroke(){this.stroke(),this.fill()}eoFillStroke(){var r;(r=this.current.element)==null||r.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}closeStroke(){this.closePath(),this.stroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.closePath(),this.eoFillStroke()}paintSolidColorImageMask(){const r=this.svgFactory.createElement("svg:rect");r.setAttributeNS(null,"x","0"),r.setAttributeNS(null,"y","0"),r.setAttributeNS(null,"width","1px"),r.setAttributeNS(null,"height","1px"),r.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().append(r)}paintImageXObject(r){const l=this.getObject(r);if(!l){(0,x.warn)(`Dependent image with object ID ${r} is not ready yet`);return}this.paintInlineImageXObject(l)}paintInlineImageXObject(r,l){const s=r.width,a=r.height,o=N(r,this.forceDataSchema,!!l),L=this.svgFactory.createElement("svg:rect");L.setAttributeNS(null,"x","0"),L.setAttributeNS(null,"y","0"),L.setAttributeNS(null,"width",b(s)),L.setAttributeNS(null,"height",b(a)),this.current.element=L,this.clip("nonzero");const n=this.svgFactory.createElement("svg:image");n.setAttributeNS(mt,"xlink:href",o),n.setAttributeNS(null,"x","0"),n.setAttributeNS(null,"y",b(-a)),n.setAttributeNS(null,"width",b(s)+"px"),n.setAttributeNS(null,"height",b(a)+"px"),n.setAttributeNS(null,"transform",`scale(${b(1/s)} ${b(-1/a)})`),l?l.append(n):this._ensureTransformGroup().append(n)}paintImageMaskXObject(r){const l=this.getObject(r.data,r);if(l.bitmap){(0,x.warn)("paintImageMaskXObject: ImageBitmap support is not implemented, ensure that the `isOffscreenCanvasSupported` API parameter is disabled.");return}const s=this.current,a=l.width,o=l.height,L=s.fillColor;s.maskId=`mask${h++}`;const n=this.svgFactory.createElement("svg:mask");n.setAttributeNS(null,"id",s.maskId);const _=this.svgFactory.createElement("svg:rect");_.setAttributeNS(null,"x","0"),_.setAttributeNS(null,"y","0"),_.setAttributeNS(null,"width",b(a)),_.setAttributeNS(null,"height",b(o)),_.setAttributeNS(null,"fill",L),_.setAttributeNS(null,"mask",`url(#${s.maskId})`),this.defs.append(n),this._ensureTransformGroup().append(_),this.paintInlineImageXObject(l,n)}paintFormXObjectBegin(r,l){if(Array.isArray(r)&&r.length===6&&this.transform(r[0],r[1],r[2],r[3],r[4],r[5]),l){const s=l[2]-l[0],a=l[3]-l[1],o=this.svgFactory.createElement("svg:rect");o.setAttributeNS(null,"x",l[0]),o.setAttributeNS(null,"y",l[1]),o.setAttributeNS(null,"width",b(s)),o.setAttributeNS(null,"height",b(a)),this.current.element=o,this.clip("nonzero"),this.endPath()}}paintFormXObjectEnd(){}_initialize(r){const l=this.svgFactory.create(r.width,r.height),s=this.svgFactory.createElement("svg:defs");l.append(s),this.defs=s;const a=this.svgFactory.createElement("svg:g");return a.setAttributeNS(null,"transform",E(r.transform)),l.append(a),this.svg=a,l}_ensureClipGroup(){if(!this.current.clipGroup){const r=this.svgFactory.createElement("svg:g");r.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.append(r),this.current.clipGroup=r}return this.current.clipGroup}_ensureTransformGroup(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",E(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)),this.tgrp}}d.SVGGraphics=I},(St,d)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.XfaText=void 0;class lt{static textContent(x){const ct=[],V={items:ct,styles:Object.create(null)};function mt(B){var N;if(!B)return;let R=null;const g=B.name;if(g==="#text")R=B.value;else if(lt.shouldBuildText(g))(N=B==null?void 0:B.attributes)!=null&&N.textContent?R=B.attributes.textContent:B.value&&(R=B.value);else return;if(R!==null&&ct.push({str:R}),!!B.children)for(const O of B.children)mt(O)}return mt(x),V}static shouldBuildText(x){return!(x==="textarea"||x==="input"||x==="option"||x==="select")}}d.XfaText=lt},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.TextLayerRenderTask=void 0,d.renderTextLayer=E,d.updateTextLayer=f;var c=lt(1),x=lt(6);const ct=1e5,V=30,mt=.8,B=new Map;function R(h,m){let I;if(m&&c.FeatureTest.isOffscreenCanvasSupported)I=new OffscreenCanvas(h,h).getContext("2d",{alpha:!1});else{const A=document.createElement("canvas");A.width=A.height=h,I=A.getContext("2d",{alpha:!1})}return I}function g(h,m){const I=B.get(h);if(I)return I;const A=R(V,m);A.font=`${V}px ${h}`;const r=A.measureText("");let l=r.fontBoundingBoxAscent,s=Math.abs(r.fontBoundingBoxDescent);if(l){const o=l/(l+s);return B.set(h,o),A.canvas.width=A.canvas.height=0,o}A.strokeStyle="red",A.clearRect(0,0,V,V),A.strokeText("g",0,0);let a=A.getImageData(0,0,V,V).data;s=0;for(let o=a.length-1-3;o>=0;o-=4)if(a[o]>0){s=Math.ceil(o/4/V);break}A.clearRect(0,0,V,V),A.strokeText("A",0,V),a=A.getImageData(0,0,V,V).data,l=0;for(let o=0,L=a.length;o<L;o+=4)if(a[o]>0){l=V-Math.floor(o/4/V);break}if(A.canvas.width=A.canvas.height=0,l){const o=l/(l+s);return B.set(h,o),o}return B.set(h,mt),mt}function N(h,m,I){const A=document.createElement("span"),r={angle:0,canvasWidth:0,hasText:m.str!=="",hasEOL:m.hasEOL,fontSize:0};h._textDivs.push(A);const l=c.Util.transform(h._transform,m.transform);let s=Math.atan2(l[1],l[0]);const a=I[m.fontName];a.vertical&&(s+=Math.PI/2);const o=Math.hypot(l[2],l[3]),L=o*g(a.fontFamily,h._isOffscreenCanvasSupported);let n,_;s===0?(n=l[4],_=l[5]-L):(n=l[4]+L*Math.sin(s),_=l[5]-L*Math.cos(s));const F="calc(var(--scale-factor)*",nt=A.style;h._container===h._rootContainer?(nt.left=`${(100*n/h._pageWidth).toFixed(2)}%`,nt.top=`${(100*_/h._pageHeight).toFixed(2)}%`):(nt.left=`${F}${n.toFixed(2)}px)`,nt.top=`${F}${_.toFixed(2)}px)`),nt.fontSize=`${F}${o.toFixed(2)}px)`,nt.fontFamily=a.fontFamily,r.fontSize=o,A.setAttribute("role","presentation"),A.textContent=m.str,A.dir=m.dir,h._fontInspectorEnabled&&(A.dataset.fontName=m.fontName),s!==0&&(r.angle=s*(180/Math.PI));let W=!1;if(m.str.length>1)W=!0;else if(m.str!==" "&&m.transform[0]!==m.transform[3]){const q=Math.abs(m.transform[0]),j=Math.abs(m.transform[3]);q!==j&&Math.max(q,j)/Math.min(q,j)>1.5&&(W=!0)}W&&(r.canvasWidth=a.vertical?m.height:m.width),h._textDivProperties.set(A,r),h._isReadableStream&&h._layoutText(A)}function O(h){const{div:m,scale:I,properties:A,ctx:r,prevFontSize:l,prevFontFamily:s}=h,{style:a}=m;let o="";if(A.canvasWidth!==0&&A.hasText){const{fontFamily:L}=a,{canvasWidth:n,fontSize:_}=A;(l!==_||s!==L)&&(r.font=`${_*I}px ${L}`,h.prevFontSize=_,h.prevFontFamily=L);const{width:F}=r.measureText(m.textContent);F>0&&(o=`scaleX(${n*I/F})`)}A.angle!==0&&(o=`rotate(${A.angle}deg) ${o}`),o.length>0&&(a.transform=o)}function v(h){if(h._canceled)return;const m=h._textDivs,I=h._capability;if(m.length>ct){I.resolve();return}if(!h._isReadableStream)for(const r of m)h._layoutText(r);I.resolve()}class b{constructor({textContentSource:m,container:I,viewport:A,textDivs:r,textDivProperties:l,textContentItemsStr:s,isOffscreenCanvasSupported:a}){var F;this._textContentSource=m,this._isReadableStream=m instanceof ReadableStream,this._container=this._rootContainer=I,this._textDivs=r||[],this._textContentItemsStr=s||[],this._isOffscreenCanvasSupported=a,this._fontInspectorEnabled=!!((F=globalThis.FontInspector)!=null&&F.enabled),this._reader=null,this._textDivProperties=l||new WeakMap,this._canceled=!1,this._capability=new c.PromiseCapability,this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:A.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:R(0,a)};const{pageWidth:o,pageHeight:L,pageX:n,pageY:_}=A.rawDims;this._transform=[1,0,0,-1,-n,_+L],this._pageWidth=o,this._pageHeight=L,(0,x.setLayerDimensions)(I,A),this._capability.promise.finally(()=>{this._layoutTextParams=null}).catch(()=>{})}get promise(){return this._capability.promise}cancel(){this._canceled=!0,this._reader&&(this._reader.cancel(new c.AbortException("TextLayer task cancelled.")).catch(()=>{}),this._reader=null),this._capability.reject(new c.AbortException("TextLayer task cancelled."))}_processItems(m,I){for(const A of m){if(A.str===void 0){if(A.type==="beginMarkedContentProps"||A.type==="beginMarkedContent"){const r=this._container;this._container=document.createElement("span"),this._container.classList.add("markedContent"),A.id!==null&&this._container.setAttribute("id",`${A.id}`),r.append(this._container)}else A.type==="endMarkedContent"&&(this._container=this._container.parentNode);continue}this._textContentItemsStr.push(A.str),N(this,A,I)}}_layoutText(m){const I=this._layoutTextParams.properties=this._textDivProperties.get(m);if(this._layoutTextParams.div=m,O(this._layoutTextParams),I.hasText&&this._container.append(m),I.hasEOL){const A=document.createElement("br");A.setAttribute("role","presentation"),this._container.append(A)}}_render(){const m=new c.PromiseCapability;let I=Object.create(null);if(this._isReadableStream){const A=()=>{this._reader.read().then(({value:r,done:l})=>{if(l){m.resolve();return}Object.assign(I,r.styles),this._processItems(r.items,I),A()},m.reject)};this._reader=this._textContentSource.getReader(),A()}else if(this._textContentSource){const{items:A,styles:r}=this._textContentSource;this._processItems(A,r),m.resolve()}else throw new Error('No "textContentSource" parameter specified.');m.promise.then(()=>{I=null,v(this)},this._capability.reject)}}d.TextLayerRenderTask=b;function E(h){!h.textContentSource&&(h.textContent||h.textContentStream)&&((0,x.deprecated)("The TextLayerRender `textContent`/`textContentStream` parameters will be removed in the future, please use `textContentSource` instead."),h.textContentSource=h.textContent||h.textContentStream);const{container:m,viewport:I}=h,A=getComputedStyle(m),r=A.getPropertyValue("visibility"),l=parseFloat(A.getPropertyValue("--scale-factor"));r==="visible"&&(!l||Math.abs(l-I.scale)>1e-5)&&console.error("The `--scale-factor` CSS-variable must be set, to the same value as `viewport.scale`, either on the `container`-element itself or higher up in the DOM.");const s=new b(h);return s._render(),s}function f({container:h,viewport:m,textDivs:I,textDivProperties:A,isOffscreenCanvasSupported:r,mustRotate:l=!0,mustRescale:s=!0}){if(l&&(0,x.setLayerDimensions)(h,{rotation:m.rotation}),s){const a=R(0,r),L={prevFontSize:null,prevFontFamily:null,div:null,scale:m.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:a};for(const n of I)L.properties=A.get(n),L.div=n,O(L)}}},(St,d,lt)=>{var g,N,O,v,b,E,f,h,m,I,A,Ke,we,Je,Qe;Object.defineProperty(d,"__esModule",{value:!0}),d.AnnotationEditorLayer=void 0;var c=lt(1),x=lt(4),ct=lt(28),V=lt(33),mt=lt(6),B=lt(34);const o=class o{constructor({uiManager:n,pageIndex:_,div:F,accessibilityManager:nt,annotationLayer:W,viewport:q,l10n:j}){Q(this,A);Q(this,g);Q(this,N,!1);Q(this,O,null);Q(this,v,this.pointerup.bind(this));Q(this,b,this.pointerdown.bind(this));Q(this,E,new Map);Q(this,f,!1);Q(this,h,!1);Q(this,m,!1);Q(this,I);const rt=[ct.FreeTextEditor,V.InkEditor,B.StampEditor];if(!o._initialized){o._initialized=!0;for(const C of rt)C.initialize(j)}n.registerEditorTypes(rt),et(this,I,n),this.pageIndex=_,this.div=F,et(this,g,nt),et(this,O,W),this.viewport=q,t(this,I).addLayer(this)}get isEmpty(){return t(this,E).size===0}updateToolbar(n){t(this,I).updateToolbar(n)}updateMode(n=t(this,I).getMode()){z(this,A,Qe).call(this),n===c.AnnotationEditorType.INK?(this.addInkEditorIfNeeded(!1),this.disableClick()):this.enableClick(),n!==c.AnnotationEditorType.NONE&&(this.div.classList.toggle("freeTextEditing",n===c.AnnotationEditorType.FREETEXT),this.div.classList.toggle("inkEditing",n===c.AnnotationEditorType.INK),this.div.classList.toggle("stampEditing",n===c.AnnotationEditorType.STAMP),this.div.hidden=!1)}addInkEditorIfNeeded(n){if(!n&&t(this,I).getMode()!==c.AnnotationEditorType.INK)return;if(!n){for(const F of t(this,E).values())if(F.isEmpty()){F.setInBackground();return}}z(this,A,we).call(this,{offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(n){t(this,I).setEditingState(n)}addCommands(n){t(this,I).addCommands(n)}enable(){this.div.style.pointerEvents="auto";const n=new Set;for(const F of t(this,E).values())F.enableEditing(),F.annotationElementId&&n.add(F.annotationElementId);if(!t(this,O))return;const _=t(this,O).getEditableAnnotations();for(const F of _){if(F.hide(),t(this,I).isDeletedAnnotationElement(F.data.id)||n.has(F.data.id))continue;const nt=this.deserialize(F);nt&&(this.addOrRebuild(nt),nt.enableEditing())}}disable(){var _;et(this,m,!0),this.div.style.pointerEvents="none";const n=new Set;for(const F of t(this,E).values()){if(F.disableEditing(),!F.annotationElementId||F.serialize()!==null){n.add(F.annotationElementId);continue}(_=this.getEditableAnnotation(F.annotationElementId))==null||_.show(),F.remove()}if(t(this,O)){const F=t(this,O).getEditableAnnotations();for(const nt of F){const{id:W}=nt.data;n.has(W)||t(this,I).isDeletedAnnotationElement(W)||nt.show()}}z(this,A,Qe).call(this),this.isEmpty&&(this.div.hidden=!0),et(this,m,!1)}getEditableAnnotation(n){var _;return((_=t(this,O))==null?void 0:_.getEditableAnnotation(n))||null}setActiveEditor(n){t(this,I).getActive()!==n&&t(this,I).setActiveEditor(n)}enableClick(){this.div.addEventListener("pointerdown",t(this,b)),this.div.addEventListener("pointerup",t(this,v))}disableClick(){this.div.removeEventListener("pointerdown",t(this,b)),this.div.removeEventListener("pointerup",t(this,v))}attach(n){t(this,E).set(n.id,n);const{annotationElementId:_}=n;_&&t(this,I).isDeletedAnnotationElement(_)&&t(this,I).removeDeletedAnnotationElement(n)}detach(n){var _;t(this,E).delete(n.id),(_=t(this,g))==null||_.removePointerInTextLayer(n.contentDiv),!t(this,m)&&n.annotationElementId&&t(this,I).addDeletedAnnotationElement(n)}remove(n){this.detach(n),t(this,I).removeEditor(n),n.div.contains(document.activeElement)&&setTimeout(()=>{t(this,I).focusMainContainer()},0),n.div.remove(),n.isAttachedToDOM=!1,t(this,h)||this.addInkEditorIfNeeded(!1)}changeParent(n){var _;n.parent!==this&&(n.annotationElementId&&(t(this,I).addDeletedAnnotationElement(n.annotationElementId),x.AnnotationEditor.deleteAnnotationElement(n),n.annotationElementId=null),this.attach(n),(_=n.parent)==null||_.detach(n),n.setParent(this),n.div&&n.isAttachedToDOM&&(n.div.remove(),this.div.append(n.div)))}add(n){if(this.changeParent(n),t(this,I).addEditor(n),this.attach(n),!n.isAttachedToDOM){const _=n.render();this.div.append(_),n.isAttachedToDOM=!0}n.fixAndSetPosition(),n.onceAdded(),t(this,I).addToAnnotationStorage(n)}moveEditorInDOM(n){var F;if(!n.isAttachedToDOM)return;const{activeElement:_}=document;n.div.contains(_)&&(n._focusEventsAllowed=!1,setTimeout(()=>{n.div.contains(document.activeElement)?n._focusEventsAllowed=!0:(n.div.addEventListener("focusin",()=>{n._focusEventsAllowed=!0},{once:!0}),_.focus())},0)),n._structTreeParentId=(F=t(this,g))==null?void 0:F.moveElementInDOM(this.div,n.div,n.contentDiv,!0)}addOrRebuild(n){n.needsToBeRebuilt()?n.rebuild():this.add(n)}addUndoableEditor(n){const _=()=>n._uiManager.rebuild(n),F=()=>{n.remove()};this.addCommands({cmd:_,undo:F,mustExec:!1})}getNextId(){return t(this,I).getId()}pasteEditor(n,_){t(this,I).updateToolbar(n),t(this,I).updateMode(n);const{offsetX:F,offsetY:nt}=z(this,A,Je).call(this),W=this.getNextId(),q=z(this,A,Ke).call(this,{parent:this,id:W,x:F,y:nt,uiManager:t(this,I),isCentered:!0,..._});q&&this.add(q)}deserialize(n){switch(n.annotationType??n.annotationEditorType){case c.AnnotationEditorType.FREETEXT:return ct.FreeTextEditor.deserialize(n,this,t(this,I));case c.AnnotationEditorType.INK:return V.InkEditor.deserialize(n,this,t(this,I));case c.AnnotationEditorType.STAMP:return B.StampEditor.deserialize(n,this,t(this,I))}return null}addNewEditor(){z(this,A,we).call(this,z(this,A,Je).call(this),!0)}setSelected(n){t(this,I).setSelected(n)}toggleSelected(n){t(this,I).toggleSelected(n)}isSelected(n){return t(this,I).isSelected(n)}unselect(n){t(this,I).unselect(n)}pointerup(n){const{isMac:_}=c.FeatureTest.platform;if(!(n.button!==0||n.ctrlKey&&_)&&n.target===this.div&&t(this,f)){if(et(this,f,!1),!t(this,N)){et(this,N,!0);return}if(t(this,I).getMode()===c.AnnotationEditorType.STAMP){t(this,I).unselectAll();return}z(this,A,we).call(this,n,!1)}}pointerdown(n){if(t(this,f)){et(this,f,!1);return}const{isMac:_}=c.FeatureTest.platform;if(n.button!==0||n.ctrlKey&&_||n.target!==this.div)return;et(this,f,!0);const F=t(this,I).getActive();et(this,N,!F||F.isEmpty())}findNewParent(n,_,F){const nt=t(this,I).findParent(_,F);return nt===null||nt===this?!1:(nt.changeParent(n),!0)}destroy(){var n,_;((n=t(this,I).getActive())==null?void 0:n.parent)===this&&(t(this,I).commitOrRemove(),t(this,I).setActiveEditor(null));for(const F of t(this,E).values())(_=t(this,g))==null||_.removePointerInTextLayer(F.contentDiv),F.setParent(null),F.isAttachedToDOM=!1,F.div.remove();this.div=null,t(this,E).clear(),t(this,I).removeLayer(this)}render({viewport:n}){this.viewport=n,(0,mt.setLayerDimensions)(this.div,n);for(const _ of t(this,I).getEditors(this.pageIndex))this.add(_);this.updateMode()}update({viewport:n}){t(this,I).commitOrRemove(),this.viewport=n,(0,mt.setLayerDimensions)(this.div,{rotation:n.rotation}),this.updateMode()}get pageDimensions(){const{pageWidth:n,pageHeight:_}=this.viewport.rawDims;return[n,_]}};g=new WeakMap,N=new WeakMap,O=new WeakMap,v=new WeakMap,b=new WeakMap,E=new WeakMap,f=new WeakMap,h=new WeakMap,m=new WeakMap,I=new WeakMap,A=new WeakSet,Ke=function(n){switch(t(this,I).getMode()){case c.AnnotationEditorType.FREETEXT:return new ct.FreeTextEditor(n);case c.AnnotationEditorType.INK:return new V.InkEditor(n);case c.AnnotationEditorType.STAMP:return new B.StampEditor(n)}return null},we=function(n,_){const F=this.getNextId(),nt=z(this,A,Ke).call(this,{parent:this,id:F,x:n.offsetX,y:n.offsetY,uiManager:t(this,I),isCentered:_});return nt&&this.add(nt),nt},Je=function(){const{x:n,y:_,width:F,height:nt}=this.div.getBoundingClientRect(),W=Math.max(0,n),q=Math.max(0,_),j=Math.min(window.innerWidth,n+F),rt=Math.min(window.innerHeight,_+nt),C=(W+j)/2-n,U=(q+rt)/2-_,[Y,S]=this.viewport.rotation%180===0?[C,U]:[U,C];return{offsetX:Y,offsetY:S}},Qe=function(){et(this,h,!0);for(const n of t(this,E).values())n.isEmpty()&&n.remove();et(this,h,!1)},Kt(o,"_initialized",!1);let R=o;d.AnnotationEditorLayer=R},(St,d,lt)=>{var B,R,g,N,O,v,b,E,f,h,Ti,xi,Pi,ge,Ze,ki,ti;Object.defineProperty(d,"__esModule",{value:!0}),d.FreeTextEditor=void 0;var c=lt(1),x=lt(5),ct=lt(4),V=lt(29);const o=class o extends ct.AnnotationEditor{constructor(_){super({..._,name:"freeTextEditor"});Q(this,h);Q(this,B,this.editorDivBlur.bind(this));Q(this,R,this.editorDivFocus.bind(this));Q(this,g,this.editorDivInput.bind(this));Q(this,N,this.editorDivKeydown.bind(this));Q(this,O);Q(this,v,"");Q(this,b,`${this.id}-editor`);Q(this,E);Q(this,f,null);et(this,O,_.color||o._defaultColor||ct.AnnotationEditor._defaultLineColor),et(this,E,_.fontSize||o._defaultFontSize)}static get _keyboardManager(){const _=o.prototype,F=q=>q.isEmpty(),nt=x.AnnotationEditorUIManager.TRANSLATE_SMALL,W=x.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,c.shadow)(this,"_keyboardManager",new x.KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],_.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],_.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],_._translateEmpty,{args:[-nt,0],checker:F}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],_._translateEmpty,{args:[-W,0],checker:F}],[["ArrowRight","mac+ArrowRight"],_._translateEmpty,{args:[nt,0],checker:F}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],_._translateEmpty,{args:[W,0],checker:F}],[["ArrowUp","mac+ArrowUp"],_._translateEmpty,{args:[0,-nt],checker:F}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],_._translateEmpty,{args:[0,-W],checker:F}],[["ArrowDown","mac+ArrowDown"],_._translateEmpty,{args:[0,nt],checker:F}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],_._translateEmpty,{args:[0,W],checker:F}]]))}static initialize(_){ct.AnnotationEditor.initialize(_,{strings:["free_text2_default_content","editor_free_text2_aria_label"]});const F=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(F.getPropertyValue("--freetext-padding"))}static updateDefaultParams(_,F){switch(_){case c.AnnotationEditorParamsType.FREETEXT_SIZE:o._defaultFontSize=F;break;case c.AnnotationEditorParamsType.FREETEXT_COLOR:o._defaultColor=F;break}}updateParams(_,F){switch(_){case c.AnnotationEditorParamsType.FREETEXT_SIZE:z(this,h,Ti).call(this,F);break;case c.AnnotationEditorParamsType.FREETEXT_COLOR:z(this,h,xi).call(this,F);break}}static get defaultPropertiesToUpdate(){return[[c.AnnotationEditorParamsType.FREETEXT_SIZE,o._defaultFontSize],[c.AnnotationEditorParamsType.FREETEXT_COLOR,o._defaultColor||ct.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[c.AnnotationEditorParamsType.FREETEXT_SIZE,t(this,E)],[c.AnnotationEditorParamsType.FREETEXT_COLOR,t(this,O)]]}_translateEmpty(_,F){this._uiManager.translateSelectedEditors(_,F,!0)}getInitialTranslation(){const _=this.parentScale;return[-o._internalPadding*_,-(o._internalPadding+t(this,E))*_]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){this.isInEditMode()||(this.parent.setEditingState(!1),this.parent.updateToolbar(c.AnnotationEditorType.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.editorDiv.addEventListener("keydown",t(this,N)),this.editorDiv.addEventListener("focus",t(this,R)),this.editorDiv.addEventListener("blur",t(this,B)),this.editorDiv.addEventListener("input",t(this,g)))}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",t(this,b)),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",t(this,N)),this.editorDiv.removeEventListener("focus",t(this,R)),this.editorDiv.removeEventListener("blur",t(this,B)),this.editorDiv.removeEventListener("input",t(this,g)),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freeTextEditing"))}focusin(_){this._focusEventsAllowed&&(super.focusin(_),_.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(){var _;if(this.width){z(this,h,ti).call(this);return}this.enableEditMode(),this.editorDiv.focus(),(_=this._initialOptions)!=null&&_.isCentered&&this.center(),this._initialOptions=null}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freeTextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const _=t(this,v),F=et(this,v,z(this,h,Pi).call(this).trimEnd());if(_===F)return;const nt=W=>{if(et(this,v,W),!W){this.remove();return}z(this,h,Ze).call(this),this._uiManager.rebuild(this),z(this,h,ge).call(this)};this.addCommands({cmd:()=>{nt(F)},undo:()=>{nt(_)},mustExec:!1}),z(this,h,ge).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(_){this.enterInEditMode()}keydown(_){_.target===this.div&&_.key==="Enter"&&(this.enterInEditMode(),_.preventDefault())}editorDivKeydown(_){o._keyboardManager.exec(this,_)}editorDivFocus(_){this.isEditing=!0}editorDivBlur(_){this.isEditing=!1}editorDivInput(_){this.parent.div.classList.toggle("freeTextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let _,F;this.width&&(_=this.x,F=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",t(this,b)),this.enableEditing(),ct.AnnotationEditor._l10nPromise.get("editor_free_text2_aria_label").then(W=>{var q;return(q=this.editorDiv)==null?void 0:q.setAttribute("aria-label",W)}),ct.AnnotationEditor._l10nPromise.get("free_text2_default_content").then(W=>{var q;return(q=this.editorDiv)==null?void 0:q.setAttribute("default-content",W)}),this.editorDiv.contentEditable=!0;const{style:nt}=this.editorDiv;if(nt.fontSize=`calc(${t(this,E)}px * var(--scale-factor))`,nt.color=t(this,O),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),(0,x.bindEvents)(this,this.div,["dblclick","keydown"]),this.width){const[W,q]=this.parentDimensions;if(this.annotationElementId){const{position:j}=t(this,f);let[rt,C]=this.getInitialTranslation();[rt,C]=this.pageTranslationToScreen(rt,C);const[U,Y]=this.pageDimensions,[S,e]=this.pageTranslation;let i,u;switch(this.rotation){case 0:i=_+(j[0]-S)/U,u=F+this.height-(j[1]-e)/Y;break;case 90:i=_+(j[0]-S)/U,u=F-(j[1]-e)/Y,[rt,C]=[C,-rt];break;case 180:i=_-this.width+(j[0]-S)/U,u=F-(j[1]-e)/Y,[rt,C]=[-rt,-C];break;case 270:i=_+(j[0]-S-this.height*Y)/U,u=F+(j[1]-e-this.width*U)/Y,[rt,C]=[-C,rt];break}this.setAt(i*W,u*q,rt,C)}else this.setAt(_*W,F*q,this.width*W,this.height*q);z(this,h,Ze).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}get contentDiv(){return this.editorDiv}static deserialize(_,F,nt){let W=null;if(_ instanceof V.FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:j,fontColor:rt},rect:C,rotation:U,id:Y},textContent:S,textPosition:e,parent:{page:{pageNumber:i}}}=_;if(!S||S.length===0)return null;W=_={annotationType:c.AnnotationEditorType.FREETEXT,color:Array.from(rt),fontSize:j,value:S.join(`
`),position:e,pageIndex:i-1,rect:C,rotation:U,id:Y,deleted:!1}}const q=super.deserialize(_,F,nt);return et(q,E,_.fontSize),et(q,O,c.Util.makeHexColor(..._.color)),et(q,v,_.value),q.annotationElementId=_.id||null,et(q,f,W),q}serialize(_=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const F=o._internalPadding*this.parentScale,nt=this.getRect(F,F),W=ct.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:t(this,O)),q={annotationType:c.AnnotationEditorType.FREETEXT,color:W,fontSize:t(this,E),value:t(this,v),pageIndex:this.pageIndex,rect:nt,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return _?q:this.annotationElementId&&!z(this,h,ki).call(this,q)?null:(q.id=this.annotationElementId,q)}};B=new WeakMap,R=new WeakMap,g=new WeakMap,N=new WeakMap,O=new WeakMap,v=new WeakMap,b=new WeakMap,E=new WeakMap,f=new WeakMap,h=new WeakSet,Ti=function(_){const F=W=>{this.editorDiv.style.fontSize=`calc(${W}px * var(--scale-factor))`,this.translate(0,-(W-t(this,E))*this.parentScale),et(this,E,W),z(this,h,ge).call(this)},nt=t(this,E);this.addCommands({cmd:()=>{F(_)},undo:()=>{F(nt)},mustExec:!0,type:c.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},xi=function(_){const F=t(this,O);this.addCommands({cmd:()=>{et(this,O,this.editorDiv.style.color=_)},undo:()=>{et(this,O,this.editorDiv.style.color=F)},mustExec:!0,type:c.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},Pi=function(){const _=this.editorDiv.getElementsByTagName("div");if(_.length===0)return this.editorDiv.innerText;const F=[];for(const nt of _)F.push(nt.innerText.replace(/\r\n?|\n/,""));return F.join(`
`)},ge=function(){const[_,F]=this.parentDimensions;let nt;if(this.isAttachedToDOM)nt=this.div.getBoundingClientRect();else{const{currentLayer:W,div:q}=this,j=q.style.display;q.style.display="hidden",W.div.append(this.div),nt=q.getBoundingClientRect(),q.remove(),q.style.display=j}this.rotation%180===this.parentRotation%180?(this.width=nt.width/_,this.height=nt.height/F):(this.width=nt.height/_,this.height=nt.width/F),this.fixAndSetPosition()},Ze=function(){if(this.editorDiv.replaceChildren(),!!t(this,v))for(const _ of t(this,v).split(`
`)){const F=document.createElement("div");F.append(_?document.createTextNode(_):document.createElement("br")),this.editorDiv.append(F)}},ki=function(_){const{value:F,fontSize:nt,color:W,rect:q,pageIndex:j}=t(this,f);return _.value!==F||_.fontSize!==nt||_.rect.some((rt,C)=>Math.abs(rt-q[C])>=1)||_.color.some((rt,C)=>rt!==W[C])||_.pageIndex!==j},ti=function(_=!1){if(!this.annotationElementId)return;if(z(this,h,ge).call(this),!_&&(this.width===0||this.height===0)){setTimeout(()=>z(this,h,ti).call(this,!0),0);return}const F=o._internalPadding*this.parentScale;t(this,f).rect=this.getRect(F,F)},Kt(o,"_freeTextDefaultContent",""),Kt(o,"_internalPadding",0),Kt(o,"_defaultColor",null),Kt(o,"_defaultFontSize",10),Kt(o,"_type","freetext");let mt=o;d.FreeTextEditor=mt},(St,d,lt)=>{var u,P,ne,Fi,it,ht,dt,pt,_t,yt,K,Z,p,M,X,J,ut,vt,At,$,Tt,wt,Mi,Ce,ei,ii,Nt,$t,qt,bt,tt,st,kt,si,Gt,D,ft,Ct,Ri,ni;Object.defineProperty(d,"__esModule",{value:!0}),d.StampAnnotationElement=d.InkAnnotationElement=d.FreeTextAnnotationElement=d.AnnotationLayer=void 0;var c=lt(1),x=lt(6),ct=lt(3),V=lt(30),mt=lt(31),B=lt(32);const R=1e3,g=9,N=new WeakSet;function O(Pt){return{width:Pt[2]-Pt[0],height:Pt[3]-Pt[1]}}class v{static create(w){switch(w.data.annotationType){case c.AnnotationType.LINK:return new E(w);case c.AnnotationType.TEXT:return new f(w);case c.AnnotationType.WIDGET:switch(w.data.fieldType){case"Tx":return new m(w);case"Btn":return w.data.radioButton?new r(w):w.data.checkBox?new A(w):new l(w);case"Ch":return new s(w);case"Sig":return new I(w)}return new h(w);case c.AnnotationType.POPUP:return new a(w);case c.AnnotationType.FREETEXT:return new L(w);case c.AnnotationType.LINE:return new n(w);case c.AnnotationType.SQUARE:return new _(w);case c.AnnotationType.CIRCLE:return new F(w);case c.AnnotationType.POLYLINE:return new nt(w);case c.AnnotationType.CARET:return new q(w);case c.AnnotationType.INK:return new j(w);case c.AnnotationType.POLYGON:return new W(w);case c.AnnotationType.HIGHLIGHT:return new rt(w);case c.AnnotationType.UNDERLINE:return new C(w);case c.AnnotationType.SQUIGGLY:return new U(w);case c.AnnotationType.STRIKEOUT:return new Y(w);case c.AnnotationType.STAMP:return new S(w);case c.AnnotationType.FILEATTACHMENT:return new e(w);default:return new b(w)}}}const T=class T{constructor(w,{isRenderable:y=!1,ignoreBorder:H=!1,createQuadrilaterals:at=!1}={}){Q(this,u,!1);this.isRenderable=y,this.data=w.data,this.layer=w.layer,this.linkService=w.linkService,this.downloadManager=w.downloadManager,this.imageResourcesPath=w.imageResourcesPath,this.renderForms=w.renderForms,this.svgFactory=w.svgFactory,this.annotationStorage=w.annotationStorage,this.enableScripting=w.enableScripting,this.hasJSActions=w.hasJSActions,this._fieldObjects=w.fieldObjects,this.parent=w.parent,y&&(this.container=this._createContainer(H)),at&&this._createQuadrilaterals()}static _hasPopupData({titleObj:w,contentsObj:y,richText:H}){return!!(w!=null&&w.str||y!=null&&y.str||H!=null&&H.str)}get hasPopupData(){return T._hasPopupData(this.data)}_createContainer(w){const{data:y,parent:{page:H,viewport:at}}=this,ot=document.createElement("section");ot.setAttribute("data-annotation-id",y.id),this instanceof h||(ot.tabIndex=R),ot.style.zIndex=this.parent.zIndex++,this.data.popupRef&&ot.setAttribute("aria-haspopup","dialog"),y.noRotate&&ot.classList.add("norotate");const{pageWidth:gt,pageHeight:Et,pageX:It,pageY:Mt}=at.rawDims;if(!y.rect||this instanceof a){const{rotation:Ut}=y;return!y.hasOwnCanvas&&Ut!==0&&this.setRotation(Ut,ot),ot}const{width:xt,height:Ht}=O(y.rect),Rt=c.Util.normalizeRect([y.rect[0],H.view[3]-y.rect[1]+H.view[1],y.rect[2],H.view[3]-y.rect[3]+H.view[1]]);if(!w&&y.borderStyle.width>0){ot.style.borderWidth=`${y.borderStyle.width}px`;const Ut=y.borderStyle.horizontalCornerRadius,Wt=y.borderStyle.verticalCornerRadius;if(Ut>0||Wt>0){const Yt=`calc(${Ut}px * var(--scale-factor)) / calc(${Wt}px * var(--scale-factor))`;ot.style.borderRadius=Yt}else if(this instanceof r){const Yt=`calc(${xt}px * var(--scale-factor)) / calc(${Ht}px * var(--scale-factor))`;ot.style.borderRadius=Yt}switch(y.borderStyle.style){case c.AnnotationBorderStyleType.SOLID:ot.style.borderStyle="solid";break;case c.AnnotationBorderStyleType.DASHED:ot.style.borderStyle="dashed";break;case c.AnnotationBorderStyleType.BEVELED:(0,c.warn)("Unimplemented border style: beveled");break;case c.AnnotationBorderStyleType.INSET:(0,c.warn)("Unimplemented border style: inset");break;case c.AnnotationBorderStyleType.UNDERLINE:ot.style.borderBottomStyle="solid";break}const Vt=y.borderColor||null;Vt?(et(this,u,!0),ot.style.borderColor=c.Util.makeHexColor(Vt[0]|0,Vt[1]|0,Vt[2]|0)):ot.style.borderWidth=0}ot.style.left=`${100*(Rt[0]-It)/gt}%`,ot.style.top=`${100*(Rt[1]-Mt)/Et}%`;const{rotation:Lt}=y;return y.hasOwnCanvas||Lt===0?(ot.style.width=`${100*xt/gt}%`,ot.style.height=`${100*Ht/Et}%`):this.setRotation(Lt,ot),ot}setRotation(w,y=this.container){if(!this.data.rect)return;const{pageWidth:H,pageHeight:at}=this.parent.viewport.rawDims,{width:ot,height:gt}=O(this.data.rect);let Et,It;w%180===0?(Et=100*ot/H,It=100*gt/at):(Et=100*gt/H,It=100*ot/at),y.style.width=`${Et}%`,y.style.height=`${It}%`,y.setAttribute("data-main-rotation",(360-w)%360)}get _commonActions(){const w=(y,H,at)=>{const ot=at.detail[y],gt=ot[0],Et=ot.slice(1);at.target.style[H]=V.ColorConverters[`${gt}_HTML`](Et),this.annotationStorage.setValue(this.data.id,{[H]:V.ColorConverters[`${gt}_rgb`](Et)})};return(0,c.shadow)(this,"_commonActions",{display:y=>{const{display:H}=y.detail,at=H%2===1;this.container.style.visibility=at?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:at,noPrint:H===1||H===2})},print:y=>{this.annotationStorage.setValue(this.data.id,{noPrint:!y.detail.print})},hidden:y=>{const{hidden:H}=y.detail;this.container.style.visibility=H?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:H,noView:H})},focus:y=>{setTimeout(()=>y.target.focus({preventScroll:!1}),0)},userName:y=>{y.target.title=y.detail.userName},readonly:y=>{y.target.disabled=y.detail.readonly},required:y=>{this._setRequired(y.target,y.detail.required)},bgColor:y=>{w("bgColor","backgroundColor",y)},fillColor:y=>{w("fillColor","backgroundColor",y)},fgColor:y=>{w("fgColor","color",y)},textColor:y=>{w("textColor","color",y)},borderColor:y=>{w("borderColor","borderColor",y)},strokeColor:y=>{w("strokeColor","borderColor",y)},rotation:y=>{const H=y.detail.rotation;this.setRotation(H),this.annotationStorage.setValue(this.data.id,{rotation:H})}})}_dispatchEventFromSandbox(w,y){const H=this._commonActions;for(const at of Object.keys(y.detail)){const ot=w[at]||H[at];ot==null||ot(y)}}_setDefaultPropertiesFromJS(w){if(!this.enableScripting)return;const y=this.annotationStorage.getRawValue(this.data.id);if(!y)return;const H=this._commonActions;for(const[at,ot]of Object.entries(y)){const gt=H[at];if(gt){const Et={detail:{[at]:ot},target:w};gt(Et),delete y[at]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:w}=this.data;if(!w)return;const[y,H,at,ot]=this.data.rect;if(w.length===1){const[,{x:Wt,y:Vt},{x:Yt,y:Qt}]=w[0];if(at===Wt&&ot===Vt&&y===Yt&&H===Qt)return}const{style:gt}=this.container;let Et;if(t(this,u)){const{borderColor:Wt,borderWidth:Vt}=gt;gt.borderWidth=0,Et=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${Wt}" stroke-width="${Vt}">`],this.container.classList.add("hasBorder")}const It=at-y,Mt=ot-H,{svgFactory:xt}=this,Ht=xt.createElement("svg");Ht.classList.add("quadrilateralsContainer"),Ht.setAttribute("width",0),Ht.setAttribute("height",0);const Rt=xt.createElement("defs");Ht.append(Rt);const Lt=xt.createElement("clipPath"),Ut=`clippath_${this.data.id}`;Lt.setAttribute("id",Ut),Lt.setAttribute("clipPathUnits","objectBoundingBox"),Rt.append(Lt);for(const[,{x:Wt,y:Vt},{x:Yt,y:Qt}]of w){const Jt=xt.createElement("rect"),te=(Yt-y)/It,ie=(ot-Vt)/Mt,se=(Wt-Yt)/It,fi=(Vt-Qt)/Mt;Jt.setAttribute("x",te),Jt.setAttribute("y",ie),Jt.setAttribute("width",se),Jt.setAttribute("height",fi),Lt.append(Jt),Et==null||Et.push(`<rect vector-effect="non-scaling-stroke" x="${te}" y="${ie}" width="${se}" height="${fi}"/>`)}t(this,u)&&(Et.push("</g></svg>')"),gt.backgroundImage=Et.join("")),this.container.append(Ht),this.container.style.clipPath=`url(#${Ut})`}_createPopup(){const{container:w,data:y}=this;w.setAttribute("aria-haspopup","dialog");const H=new a({data:{color:y.color,titleObj:y.titleObj,modificationDate:y.modificationDate,contentsObj:y.contentsObj,richText:y.richText,parentRect:y.rect,borderStyle:0,id:`popup_${y.id}`,rotation:y.rotation},parent:this.parent,elements:[this]});this.parent.div.append(H.render())}render(){(0,c.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(w,y=null){const H=[];if(this._fieldObjects){const at=this._fieldObjects[w];if(at)for(const{page:ot,id:gt,exportValues:Et}of at){if(ot===-1||gt===y)continue;const It=typeof Et=="string"?Et:null,Mt=document.querySelector(`[data-element-id="${gt}"]`);if(Mt&&!N.has(Mt)){(0,c.warn)(`_getElementsByName - element not allowed: ${gt}`);continue}H.push({id:gt,exportValue:It,domElement:Mt})}return H}for(const at of document.getElementsByName(w)){const{exportValue:ot}=at,gt=at.getAttribute("data-element-id");gt!==y&&N.has(at)&&H.push({id:gt,exportValue:ot,domElement:at})}return H}show(){var w;this.container&&(this.container.hidden=!1),(w=this.popup)==null||w.maybeShow()}hide(){var w;this.container&&(this.container.hidden=!0),(w=this.popup)==null||w.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const w=this.getElementsToTriggerPopup();if(Array.isArray(w))for(const y of w)y.classList.add("highlightArea");else w.classList.add("highlightArea")}_editOnDoubleClick(){const{annotationEditorType:w,data:{id:y}}=this;this.container.addEventListener("dblclick",()=>{var H;(H=this.linkService.eventBus)==null||H.dispatch("switchannotationeditormode",{source:this,mode:w,editId:y})})}};u=new WeakMap;let b=T;class E extends b{constructor(y,H=null){super(y,{isRenderable:!0,ignoreBorder:!!(H!=null&&H.ignoreBorder),createQuadrilaterals:!0});Q(this,P);this.isTooltipOnly=y.data.isTooltipOnly}render(){const{data:y,linkService:H}=this,at=document.createElement("a");at.setAttribute("data-element-id",y.id);let ot=!1;return y.url?(H.addLinkAttributes(at,y.url,y.newWindow),ot=!0):y.action?(this._bindNamedAction(at,y.action),ot=!0):y.attachment?(this._bindAttachment(at,y.attachment),ot=!0):y.setOCGState?(z(this,P,Fi).call(this,at,y.setOCGState),ot=!0):y.dest?(this._bindLink(at,y.dest),ot=!0):(y.actions&&(y.actions.Action||y.actions["Mouse Up"]||y.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(at,y),ot=!0),y.resetForm?(this._bindResetFormAction(at,y.resetForm),ot=!0):this.isTooltipOnly&&!ot&&(this._bindLink(at,""),ot=!0)),this.container.classList.add("linkAnnotation"),ot&&this.container.append(at),this.container}_bindLink(y,H){y.href=this.linkService.getDestinationHash(H),y.onclick=()=>(H&&this.linkService.goToDestination(H),!1),(H||H==="")&&z(this,P,ne).call(this)}_bindNamedAction(y,H){y.href=this.linkService.getAnchorUrl(""),y.onclick=()=>(this.linkService.executeNamedAction(H),!1),z(this,P,ne).call(this)}_bindAttachment(y,H){y.href=this.linkService.getAnchorUrl(""),y.onclick=()=>{var at;return(at=this.downloadManager)==null||at.openOrDownloadData(this.container,H.content,H.filename),!1},z(this,P,ne).call(this)}_bindJSAction(y,H){y.href=this.linkService.getAnchorUrl("");const at=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const ot of Object.keys(H.actions)){const gt=at.get(ot);gt&&(y[gt]=()=>{var Et;return(Et=this.linkService.eventBus)==null||Et.dispatch("dispatcheventinsandbox",{source:this,detail:{id:H.id,name:ot}}),!1})}y.onclick||(y.onclick=()=>!1),z(this,P,ne).call(this)}_bindResetFormAction(y,H){const at=y.onclick;if(at||(y.href=this.linkService.getAnchorUrl("")),z(this,P,ne).call(this),!this._fieldObjects){(0,c.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),at||(y.onclick=()=>!1);return}y.onclick=()=>{var Ht;at==null||at();const{fields:ot,refs:gt,include:Et}=H,It=[];if(ot.length!==0||gt.length!==0){const Rt=new Set(gt);for(const Lt of ot){const Ut=this._fieldObjects[Lt]||[];for(const{id:Wt}of Ut)Rt.add(Wt)}for(const Lt of Object.values(this._fieldObjects))for(const Ut of Lt)Rt.has(Ut.id)===Et&&It.push(Ut)}else for(const Rt of Object.values(this._fieldObjects))It.push(...Rt);const Mt=this.annotationStorage,xt=[];for(const Rt of It){const{id:Lt}=Rt;switch(xt.push(Lt),Rt.type){case"text":{const Wt=Rt.defaultValue||"";Mt.setValue(Lt,{value:Wt});break}case"checkbox":case"radiobutton":{const Wt=Rt.defaultValue===Rt.exportValues;Mt.setValue(Lt,{value:Wt});break}case"combobox":case"listbox":{const Wt=Rt.defaultValue||"";Mt.setValue(Lt,{value:Wt});break}default:continue}const Ut=document.querySelector(`[data-element-id="${Lt}"]`);if(Ut){if(!N.has(Ut)){(0,c.warn)(`_bindResetFormAction - element not allowed: ${Lt}`);continue}}else continue;Ut.dispatchEvent(new Event("resetform"))}return this.enableScripting&&((Ht=this.linkService.eventBus)==null||Ht.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:xt,name:"ResetForm"}})),!1}}}P=new WeakSet,ne=function(){this.container.setAttribute("data-internal-link","")},Fi=function(y,H){y.href=this.linkService.getAnchorUrl(""),y.onclick=()=>(this.linkService.executeSetOCGState(H),!1),z(this,P,ne).call(this)};class f extends b{constructor(w){super(w,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const w=document.createElement("img");return w.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",w.alt="[{{type}} Annotation]",w.dataset.l10nId="text_annotation_type",w.dataset.l10nArgs=JSON.stringify({type:this.data.name}),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(w),this.container}}class h extends b{render(){return this.data.alternativeText&&(this.container.title=this.data.alternativeText),this.container}showElementAndHideCanvas(w){var y;this.data.hasOwnCanvas&&(((y=w.previousSibling)==null?void 0:y.nodeName)==="CANVAS"&&(w.previousSibling.hidden=!0),w.hidden=!1)}_getKeyModifier(w){const{isWin:y,isMac:H}=c.FeatureTest.platform;return y&&w.ctrlKey||H&&w.metaKey}_setEventListener(w,y,H,at,ot){H.includes("mouse")?w.addEventListener(H,gt=>{var Et;(Et=this.linkService.eventBus)==null||Et.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:at,value:ot(gt),shift:gt.shiftKey,modifier:this._getKeyModifier(gt)}})}):w.addEventListener(H,gt=>{var Et;if(H==="blur"){if(!y.focused||!gt.relatedTarget)return;y.focused=!1}else if(H==="focus"){if(y.focused)return;y.focused=!0}ot&&((Et=this.linkService.eventBus)==null||Et.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:at,value:ot(gt)}}))})}_setEventListeners(w,y,H,at){var ot,gt,Et;for(const[It,Mt]of H)(Mt==="Action"||(ot=this.data.actions)!=null&&ot[Mt])&&((Mt==="Focus"||Mt==="Blur")&&(y||(y={focused:!1})),this._setEventListener(w,y,It,Mt,at),Mt==="Focus"&&!((gt=this.data.actions)!=null&&gt.Blur)?this._setEventListener(w,y,"blur","Blur",null):Mt==="Blur"&&!((Et=this.data.actions)!=null&&Et.Focus)&&this._setEventListener(w,y,"focus","Focus",null))}_setBackgroundColor(w){const y=this.data.backgroundColor||null;w.style.backgroundColor=y===null?"transparent":c.Util.makeHexColor(y[0],y[1],y[2])}_setTextStyle(w){const y=["left","center","right"],{fontColor:H}=this.data.defaultAppearanceData,at=this.data.defaultAppearanceData.fontSize||g,ot=w.style;let gt;const Et=2,It=Mt=>Math.round(10*Mt)/10;if(this.data.multiLine){const Mt=Math.abs(this.data.rect[3]-this.data.rect[1]-Et),xt=Math.round(Mt/(c.LINE_FACTOR*at))||1,Ht=Mt/xt;gt=Math.min(at,It(Ht/c.LINE_FACTOR))}else{const Mt=Math.abs(this.data.rect[3]-this.data.rect[1]-Et);gt=Math.min(at,It(Mt/c.LINE_FACTOR))}ot.fontSize=`calc(${gt}px * var(--scale-factor))`,ot.color=c.Util.makeHexColor(H[0],H[1],H[2]),this.data.textAlignment!==null&&(ot.textAlign=y[this.data.textAlignment])}_setRequired(w,y){y?w.setAttribute("required",!0):w.removeAttribute("required"),w.setAttribute("aria-required",y)}}class m extends h{constructor(w){const y=w.renderForms||!w.data.hasAppearance&&!!w.data.fieldValue;super(w,{isRenderable:y})}setPropertyOnSiblings(w,y,H,at){const ot=this.annotationStorage;for(const gt of this._getElementsByName(w.name,w.id))gt.domElement&&(gt.domElement[y]=H),ot.setValue(gt.id,{[at]:H})}render(){var at,ot;const w=this.annotationStorage,y=this.data.id;this.container.classList.add("textWidgetAnnotation");let H=null;if(this.renderForms){const gt=w.getValue(y,{value:this.data.fieldValue});let Et=gt.value||"";const It=w.getValue(y,{charLimit:this.data.maxLen}).charLimit;It&&Et.length>It&&(Et=Et.slice(0,It));let Mt=gt.formattedValue||((at=this.data.textContent)==null?void 0:at.join(`
`))||null;Mt&&this.data.comb&&(Mt=Mt.replaceAll(/\s+/g,""));const xt={userValue:Et,formattedValue:Mt,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(H=document.createElement("textarea"),H.textContent=Mt??Et,this.data.doNotScroll&&(H.style.overflowY="hidden")):(H=document.createElement("input"),H.type="text",H.setAttribute("value",Mt??Et),this.data.doNotScroll&&(H.style.overflowX="hidden")),this.data.hasOwnCanvas&&(H.hidden=!0),N.add(H),H.setAttribute("data-element-id",y),H.disabled=this.data.readOnly,H.name=this.data.fieldName,H.tabIndex=R,this._setRequired(H,this.data.required),It&&(H.maxLength=It),H.addEventListener("input",Rt=>{w.setValue(y,{value:Rt.target.value}),this.setPropertyOnSiblings(H,"value",Rt.target.value,"value"),xt.formattedValue=null}),H.addEventListener("resetform",Rt=>{const Lt=this.data.defaultFieldValue??"";H.value=xt.userValue=Lt,xt.formattedValue=null});let Ht=Rt=>{const{formattedValue:Lt}=xt;Lt!=null&&(Rt.target.value=Lt),Rt.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){H.addEventListener("focus",Lt=>{if(xt.focused)return;const{target:Ut}=Lt;xt.userValue&&(Ut.value=xt.userValue),xt.lastCommittedValue=Ut.value,xt.commitKey=1,xt.focused=!0}),H.addEventListener("updatefromsandbox",Lt=>{this.showElementAndHideCanvas(Lt.target);const Ut={value(Wt){xt.userValue=Wt.detail.value??"",w.setValue(y,{value:xt.userValue.toString()}),Wt.target.value=xt.userValue},formattedValue(Wt){const{formattedValue:Vt}=Wt.detail;xt.formattedValue=Vt,Vt!=null&&Wt.target!==document.activeElement&&(Wt.target.value=Vt),w.setValue(y,{formattedValue:Vt})},selRange(Wt){Wt.target.setSelectionRange(...Wt.detail.selRange)},charLimit:Wt=>{var Jt;const{charLimit:Vt}=Wt.detail,{target:Yt}=Wt;if(Vt===0){Yt.removeAttribute("maxLength");return}Yt.setAttribute("maxLength",Vt);let Qt=xt.userValue;!Qt||Qt.length<=Vt||(Qt=Qt.slice(0,Vt),Yt.value=xt.userValue=Qt,w.setValue(y,{value:Qt}),(Jt=this.linkService.eventBus)==null||Jt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:y,name:"Keystroke",value:Qt,willCommit:!0,commitKey:1,selStart:Yt.selectionStart,selEnd:Yt.selectionEnd}}))}};this._dispatchEventFromSandbox(Ut,Lt)}),H.addEventListener("keydown",Lt=>{var Vt;xt.commitKey=1;let Ut=-1;if(Lt.key==="Escape"?Ut=0:Lt.key==="Enter"&&!this.data.multiLine?Ut=2:Lt.key==="Tab"&&(xt.commitKey=3),Ut===-1)return;const{value:Wt}=Lt.target;xt.lastCommittedValue!==Wt&&(xt.lastCommittedValue=Wt,xt.userValue=Wt,(Vt=this.linkService.eventBus)==null||Vt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:y,name:"Keystroke",value:Wt,willCommit:!0,commitKey:Ut,selStart:Lt.target.selectionStart,selEnd:Lt.target.selectionEnd}}))});const Rt=Ht;Ht=null,H.addEventListener("blur",Lt=>{var Wt;if(!xt.focused||!Lt.relatedTarget)return;xt.focused=!1;const{value:Ut}=Lt.target;xt.userValue=Ut,xt.lastCommittedValue!==Ut&&((Wt=this.linkService.eventBus)==null||Wt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:y,name:"Keystroke",value:Ut,willCommit:!0,commitKey:xt.commitKey,selStart:Lt.target.selectionStart,selEnd:Lt.target.selectionEnd}})),Rt(Lt)}),(ot=this.data.actions)!=null&&ot.Keystroke&&H.addEventListener("beforeinput",Lt=>{var ie;xt.lastCommittedValue=null;const{data:Ut,target:Wt}=Lt,{value:Vt,selectionStart:Yt,selectionEnd:Qt}=Wt;let Jt=Yt,te=Qt;switch(Lt.inputType){case"deleteWordBackward":{const se=Vt.substring(0,Yt).match(/\w*[^\w]*$/);se&&(Jt-=se[0].length);break}case"deleteWordForward":{const se=Vt.substring(Yt).match(/^[^\w]*\w*/);se&&(te+=se[0].length);break}case"deleteContentBackward":Yt===Qt&&(Jt-=1);break;case"deleteContentForward":Yt===Qt&&(te+=1);break}Lt.preventDefault(),(ie=this.linkService.eventBus)==null||ie.dispatch("dispatcheventinsandbox",{source:this,detail:{id:y,name:"Keystroke",value:Vt,change:Ut||"",willCommit:!1,selStart:Jt,selEnd:te}})}),this._setEventListeners(H,xt,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],Lt=>Lt.target.value)}if(Ht&&H.addEventListener("blur",Ht),this.data.comb){const Lt=(this.data.rect[2]-this.data.rect[0])/It;H.classList.add("comb"),H.style.letterSpacing=`calc(${Lt}px * var(--scale-factor) - 1ch)`}}else H=document.createElement("div"),H.textContent=this.data.fieldValue,H.style.verticalAlign="middle",H.style.display="table-cell";return this._setTextStyle(H),this._setBackgroundColor(H),this._setDefaultPropertiesFromJS(H),this.container.append(H),this.container}}class I extends h{constructor(w){super(w,{isRenderable:!!w.data.hasOwnCanvas})}}class A extends h{constructor(w){super(w,{isRenderable:w.renderForms})}render(){const w=this.annotationStorage,y=this.data,H=y.id;let at=w.getValue(H,{value:y.exportValue===y.fieldValue}).value;typeof at=="string"&&(at=at!=="Off",w.setValue(H,{value:at})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const ot=document.createElement("input");return N.add(ot),ot.setAttribute("data-element-id",H),ot.disabled=y.readOnly,this._setRequired(ot,this.data.required),ot.type="checkbox",ot.name=y.fieldName,at&&ot.setAttribute("checked",!0),ot.setAttribute("exportValue",y.exportValue),ot.tabIndex=R,ot.addEventListener("change",gt=>{const{name:Et,checked:It}=gt.target;for(const Mt of this._getElementsByName(Et,H)){const xt=It&&Mt.exportValue===y.exportValue;Mt.domElement&&(Mt.domElement.checked=xt),w.setValue(Mt.id,{value:xt})}w.setValue(H,{value:It})}),ot.addEventListener("resetform",gt=>{const Et=y.defaultFieldValue||"Off";gt.target.checked=Et===y.exportValue}),this.enableScripting&&this.hasJSActions&&(ot.addEventListener("updatefromsandbox",gt=>{const Et={value(It){It.target.checked=It.detail.value!=="Off",w.setValue(H,{value:It.target.checked})}};this._dispatchEventFromSandbox(Et,gt)}),this._setEventListeners(ot,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],gt=>gt.target.checked)),this._setBackgroundColor(ot),this._setDefaultPropertiesFromJS(ot),this.container.append(ot),this.container}}class r extends h{constructor(w){super(w,{isRenderable:w.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const w=this.annotationStorage,y=this.data,H=y.id;let at=w.getValue(H,{value:y.fieldValue===y.buttonValue}).value;typeof at=="string"&&(at=at!==y.buttonValue,w.setValue(H,{value:at}));const ot=document.createElement("input");if(N.add(ot),ot.setAttribute("data-element-id",H),ot.disabled=y.readOnly,this._setRequired(ot,this.data.required),ot.type="radio",ot.name=y.fieldName,at&&ot.setAttribute("checked",!0),ot.tabIndex=R,ot.addEventListener("change",gt=>{const{name:Et,checked:It}=gt.target;for(const Mt of this._getElementsByName(Et,H))w.setValue(Mt.id,{value:!1});w.setValue(H,{value:It})}),ot.addEventListener("resetform",gt=>{const Et=y.defaultFieldValue;gt.target.checked=Et!=null&&Et===y.buttonValue}),this.enableScripting&&this.hasJSActions){const gt=y.buttonValue;ot.addEventListener("updatefromsandbox",Et=>{const It={value:Mt=>{const xt=gt===Mt.detail.value;for(const Ht of this._getElementsByName(Mt.target.name)){const Rt=xt&&Ht.id===H;Ht.domElement&&(Ht.domElement.checked=Rt),w.setValue(Ht.id,{value:Rt})}}};this._dispatchEventFromSandbox(It,Et)}),this._setEventListeners(ot,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],Et=>Et.target.checked)}return this._setBackgroundColor(ot),this._setDefaultPropertiesFromJS(ot),this.container.append(ot),this.container}}class l extends E{constructor(w){super(w,{ignoreBorder:w.data.hasAppearance})}render(){const w=super.render();w.classList.add("buttonWidgetAnnotation","pushButton"),this.data.alternativeText&&(w.title=this.data.alternativeText);const y=w.lastChild;return this.enableScripting&&this.hasJSActions&&y&&(this._setDefaultPropertiesFromJS(y),y.addEventListener("updatefromsandbox",H=>{this._dispatchEventFromSandbox({},H)})),w}}class s extends h{constructor(w){super(w,{isRenderable:w.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const w=this.annotationStorage,y=this.data.id,H=w.getValue(y,{value:this.data.fieldValue}),at=document.createElement("select");N.add(at),at.setAttribute("data-element-id",y),at.disabled=this.data.readOnly,this._setRequired(at,this.data.required),at.name=this.data.fieldName,at.tabIndex=R;let ot=this.data.combo&&this.data.options.length>0;this.data.combo||(at.size=this.data.options.length,this.data.multiSelect&&(at.multiple=!0)),at.addEventListener("resetform",xt=>{const Ht=this.data.defaultFieldValue;for(const Rt of at.options)Rt.selected=Rt.value===Ht});for(const xt of this.data.options){const Ht=document.createElement("option");Ht.textContent=xt.displayValue,Ht.value=xt.exportValue,H.value.includes(xt.exportValue)&&(Ht.setAttribute("selected",!0),ot=!1),at.append(Ht)}let gt=null;if(ot){const xt=document.createElement("option");xt.value=" ",xt.setAttribute("hidden",!0),xt.setAttribute("selected",!0),at.prepend(xt),gt=()=>{xt.remove(),at.removeEventListener("input",gt),gt=null},at.addEventListener("input",gt)}const Et=xt=>{const Ht=xt?"value":"textContent",{options:Rt,multiple:Lt}=at;return Lt?Array.prototype.filter.call(Rt,Ut=>Ut.selected).map(Ut=>Ut[Ht]):Rt.selectedIndex===-1?null:Rt[Rt.selectedIndex][Ht]};let It=Et(!1);const Mt=xt=>{const Ht=xt.target.options;return Array.prototype.map.call(Ht,Rt=>({displayValue:Rt.textContent,exportValue:Rt.value}))};return this.enableScripting&&this.hasJSActions?(at.addEventListener("updatefromsandbox",xt=>{const Ht={value(Rt){gt==null||gt();const Lt=Rt.detail.value,Ut=new Set(Array.isArray(Lt)?Lt:[Lt]);for(const Wt of at.options)Wt.selected=Ut.has(Wt.value);w.setValue(y,{value:Et(!0)}),It=Et(!1)},multipleSelection(Rt){at.multiple=!0},remove(Rt){const Lt=at.options,Ut=Rt.detail.remove;Lt[Ut].selected=!1,at.remove(Ut),Lt.length>0&&Array.prototype.findIndex.call(Lt,Vt=>Vt.selected)===-1&&(Lt[0].selected=!0),w.setValue(y,{value:Et(!0),items:Mt(Rt)}),It=Et(!1)},clear(Rt){for(;at.length!==0;)at.remove(0);w.setValue(y,{value:null,items:[]}),It=Et(!1)},insert(Rt){const{index:Lt,displayValue:Ut,exportValue:Wt}=Rt.detail.insert,Vt=at.children[Lt],Yt=document.createElement("option");Yt.textContent=Ut,Yt.value=Wt,Vt?Vt.before(Yt):at.append(Yt),w.setValue(y,{value:Et(!0),items:Mt(Rt)}),It=Et(!1)},items(Rt){const{items:Lt}=Rt.detail;for(;at.length!==0;)at.remove(0);for(const Ut of Lt){const{displayValue:Wt,exportValue:Vt}=Ut,Yt=document.createElement("option");Yt.textContent=Wt,Yt.value=Vt,at.append(Yt)}at.options.length>0&&(at.options[0].selected=!0),w.setValue(y,{value:Et(!0),items:Mt(Rt)}),It=Et(!1)},indices(Rt){const Lt=new Set(Rt.detail.indices);for(const Ut of Rt.target.options)Ut.selected=Lt.has(Ut.index);w.setValue(y,{value:Et(!0)}),It=Et(!1)},editable(Rt){Rt.target.disabled=!Rt.detail.editable}};this._dispatchEventFromSandbox(Ht,xt)}),at.addEventListener("input",xt=>{var Rt;const Ht=Et(!0);w.setValue(y,{value:Ht}),xt.preventDefault(),(Rt=this.linkService.eventBus)==null||Rt.dispatch("dispatcheventinsandbox",{source:this,detail:{id:y,name:"Keystroke",value:It,changeEx:Ht,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(at,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],xt=>xt.target.value)):at.addEventListener("input",function(xt){w.setValue(y,{value:Et(!0)})}),this.data.combo&&this._setTextStyle(at),this._setBackgroundColor(at),this._setDefaultPropertiesFromJS(at),this.container.append(at),this.container}}class a extends b{constructor(w){const{data:y,elements:H}=w;super(w,{isRenderable:b._hasPopupData(y)}),this.elements=H}render(){this.container.classList.add("popupAnnotation");const w=new o({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),y=[];for(const H of this.elements)H.popup=w,y.push(H.data.id),H.addHighlightArea();return this.container.setAttribute("aria-controls",y.map(H=>`${c.AnnotationPrefix}${H}`).join(",")),this.container}}class o{constructor({container:w,color:y,elements:H,titleObj:at,modificationDate:ot,contentsObj:gt,richText:Et,parent:It,rect:Mt,parentRect:xt,open:Ht}){Q(this,wt);Q(this,it,null);Q(this,ht,z(this,wt,Mi).bind(this));Q(this,dt,z(this,wt,ii).bind(this));Q(this,pt,z(this,wt,ei).bind(this));Q(this,_t,z(this,wt,Ce).bind(this));Q(this,yt,null);Q(this,K,null);Q(this,Z,null);Q(this,p,null);Q(this,M,null);Q(this,X,null);Q(this,J,!1);Q(this,ut,null);Q(this,vt,null);Q(this,At,null);Q(this,$,null);Q(this,Tt,!1);var Lt;et(this,K,w),et(this,$,at),et(this,Z,gt),et(this,At,Et),et(this,M,It),et(this,yt,y),et(this,vt,Mt),et(this,X,xt),et(this,p,H);const Rt=x.PDFDateString.toDateObject(ot);Rt&&et(this,it,It.l10n.get("annotation_date_string",{date:Rt.toLocaleDateString(),time:Rt.toLocaleTimeString()})),this.trigger=H.flatMap(Ut=>Ut.getElementsToTriggerPopup());for(const Ut of this.trigger)Ut.addEventListener("click",t(this,_t)),Ut.addEventListener("mouseenter",t(this,pt)),Ut.addEventListener("mouseleave",t(this,dt)),Ut.classList.add("popupTriggerArea");for(const Ut of H)(Lt=Ut.container)==null||Lt.addEventListener("keydown",t(this,ht));t(this,K).hidden=!0,Ht&&z(this,wt,Ce).call(this)}render(){if(t(this,ut))return;const{page:{view:w},viewport:{rawDims:{pageWidth:y,pageHeight:H,pageX:at,pageY:ot}}}=t(this,M),gt=et(this,ut,document.createElement("div"));if(gt.className="popup",t(this,yt)){const Jt=gt.style.outlineColor=c.Util.makeHexColor(...t(this,yt));CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?gt.style.backgroundColor=`color-mix(in srgb, ${Jt} 30%, white)`:gt.style.backgroundColor=c.Util.makeHexColor(...t(this,yt).map(ie=>Math.floor(.7*(255-ie)+ie)))}const Et=document.createElement("span");Et.className="header";const It=document.createElement("h1");if(Et.append(It),{dir:It.dir,str:It.textContent}=t(this,$),gt.append(Et),t(this,it)){const Jt=document.createElement("span");Jt.classList.add("popupDate"),t(this,it).then(te=>{Jt.textContent=te}),Et.append(Jt)}const Mt=t(this,Z),xt=t(this,At);if(xt!=null&&xt.str&&(!(Mt!=null&&Mt.str)||Mt.str===xt.str))B.XfaLayer.render({xfaHtml:xt.html,intent:"richText",div:gt}),gt.lastChild.classList.add("richText","popupContent");else{const Jt=this._formatContents(Mt);gt.append(Jt)}let Ht=!!t(this,X),Rt=Ht?t(this,X):t(this,vt);for(const Jt of t(this,p))if(!Rt||c.Util.intersect(Jt.data.rect,Rt)!==null){Rt=Jt.data.rect,Ht=!0;break}const Lt=c.Util.normalizeRect([Rt[0],w[3]-Rt[1]+w[1],Rt[2],w[3]-Rt[3]+w[1]]),Wt=Ht?Rt[2]-Rt[0]+5:0,Vt=Lt[0]+Wt,Yt=Lt[1],{style:Qt}=t(this,K);Qt.left=`${100*(Vt-at)/y}%`,Qt.top=`${100*(Yt-ot)/H}%`,t(this,K).append(gt)}_formatContents({str:w,dir:y}){const H=document.createElement("p");H.classList.add("popupContent"),H.dir=y;const at=w.split(/(?:\r\n?|\n)/);for(let ot=0,gt=at.length;ot<gt;++ot){const Et=at[ot];H.append(document.createTextNode(Et)),ot<gt-1&&H.append(document.createElement("br"))}return H}forceHide(){et(this,Tt,this.isVisible),t(this,Tt)&&(t(this,K).hidden=!0)}maybeShow(){t(this,Tt)&&(et(this,Tt,!1),t(this,K).hidden=!1)}get isVisible(){return t(this,K).hidden===!1}}it=new WeakMap,ht=new WeakMap,dt=new WeakMap,pt=new WeakMap,_t=new WeakMap,yt=new WeakMap,K=new WeakMap,Z=new WeakMap,p=new WeakMap,M=new WeakMap,X=new WeakMap,J=new WeakMap,ut=new WeakMap,vt=new WeakMap,At=new WeakMap,$=new WeakMap,Tt=new WeakMap,wt=new WeakSet,Mi=function(w){w.altKey||w.shiftKey||w.ctrlKey||w.metaKey||(w.key==="Enter"||w.key==="Escape"&&t(this,J))&&z(this,wt,Ce).call(this)},Ce=function(){et(this,J,!t(this,J)),t(this,J)?(z(this,wt,ei).call(this),t(this,K).addEventListener("click",t(this,_t)),t(this,K).addEventListener("keydown",t(this,ht))):(z(this,wt,ii).call(this),t(this,K).removeEventListener("click",t(this,_t)),t(this,K).removeEventListener("keydown",t(this,ht)))},ei=function(){t(this,ut)||this.render(),this.isVisible?t(this,J)&&t(this,K).classList.add("focused"):(t(this,K).hidden=!1,t(this,K).style.zIndex=parseInt(t(this,K).style.zIndex)+1e3)},ii=function(){t(this,K).classList.remove("focused"),!(t(this,J)||!this.isVisible)&&(t(this,K).hidden=!0,t(this,K).style.zIndex=parseInt(t(this,K).style.zIndex)-1e3)};class L extends b{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0}),this.textContent=w.data.textContent,this.textPosition=w.data.textPosition,this.annotationEditorType=c.AnnotationEditorType.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const w=document.createElement("div");w.classList.add("annotationTextContent"),w.setAttribute("role","comment");for(const y of this.textContent){const H=document.createElement("span");H.textContent=y,w.append(H)}this.container.append(w)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}d.FreeTextAnnotationElement=L;class n extends b{constructor(y){super(y,{isRenderable:!0,ignoreBorder:!0});Q(this,Nt,null)}render(){this.container.classList.add("lineAnnotation");const y=this.data,{width:H,height:at}=O(y.rect),ot=this.svgFactory.create(H,at,!0),gt=et(this,Nt,this.svgFactory.createElement("svg:line"));return gt.setAttribute("x1",y.rect[2]-y.lineCoordinates[0]),gt.setAttribute("y1",y.rect[3]-y.lineCoordinates[1]),gt.setAttribute("x2",y.rect[2]-y.lineCoordinates[2]),gt.setAttribute("y2",y.rect[3]-y.lineCoordinates[3]),gt.setAttribute("stroke-width",y.borderStyle.width||1),gt.setAttribute("stroke","transparent"),gt.setAttribute("fill","transparent"),ot.append(gt),this.container.append(ot),!y.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,Nt)}addHighlightArea(){this.container.classList.add("highlightArea")}}Nt=new WeakMap;class _ extends b{constructor(y){super(y,{isRenderable:!0,ignoreBorder:!0});Q(this,$t,null)}render(){this.container.classList.add("squareAnnotation");const y=this.data,{width:H,height:at}=O(y.rect),ot=this.svgFactory.create(H,at,!0),gt=y.borderStyle.width,Et=et(this,$t,this.svgFactory.createElement("svg:rect"));return Et.setAttribute("x",gt/2),Et.setAttribute("y",gt/2),Et.setAttribute("width",H-gt),Et.setAttribute("height",at-gt),Et.setAttribute("stroke-width",gt||1),Et.setAttribute("stroke","transparent"),Et.setAttribute("fill","transparent"),ot.append(Et),this.container.append(ot),!y.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,$t)}addHighlightArea(){this.container.classList.add("highlightArea")}}$t=new WeakMap;class F extends b{constructor(y){super(y,{isRenderable:!0,ignoreBorder:!0});Q(this,qt,null)}render(){this.container.classList.add("circleAnnotation");const y=this.data,{width:H,height:at}=O(y.rect),ot=this.svgFactory.create(H,at,!0),gt=y.borderStyle.width,Et=et(this,qt,this.svgFactory.createElement("svg:ellipse"));return Et.setAttribute("cx",H/2),Et.setAttribute("cy",at/2),Et.setAttribute("rx",H/2-gt/2),Et.setAttribute("ry",at/2-gt/2),Et.setAttribute("stroke-width",gt||1),Et.setAttribute("stroke","transparent"),Et.setAttribute("fill","transparent"),ot.append(Et),this.container.append(ot),!y.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,qt)}addHighlightArea(){this.container.classList.add("highlightArea")}}qt=new WeakMap;class nt extends b{constructor(y){super(y,{isRenderable:!0,ignoreBorder:!0});Q(this,bt,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const y=this.data,{width:H,height:at}=O(y.rect),ot=this.svgFactory.create(H,at,!0);let gt=[];for(const It of y.vertices){const Mt=It.x-y.rect[0],xt=y.rect[3]-It.y;gt.push(Mt+","+xt)}gt=gt.join(" ");const Et=et(this,bt,this.svgFactory.createElement(this.svgElementName));return Et.setAttribute("points",gt),Et.setAttribute("stroke-width",y.borderStyle.width||1),Et.setAttribute("stroke","transparent"),Et.setAttribute("fill","transparent"),ot.append(Et),this.container.append(ot),!y.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return t(this,bt)}addHighlightArea(){this.container.classList.add("highlightArea")}}bt=new WeakMap;class W extends nt{constructor(w){super(w),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class q extends b{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class j extends b{constructor(y){super(y,{isRenderable:!0,ignoreBorder:!0});Q(this,tt,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=c.AnnotationEditorType.INK}render(){this.container.classList.add(this.containerClassName);const y=this.data,{width:H,height:at}=O(y.rect),ot=this.svgFactory.create(H,at,!0);for(const gt of y.inkLists){let Et=[];for(const Mt of gt){const xt=Mt.x-y.rect[0],Ht=y.rect[3]-Mt.y;Et.push(`${xt},${Ht}`)}Et=Et.join(" ");const It=this.svgFactory.createElement(this.svgElementName);t(this,tt).push(It),It.setAttribute("points",Et),It.setAttribute("stroke-width",y.borderStyle.width||1),It.setAttribute("stroke","transparent"),It.setAttribute("fill","transparent"),!y.popupRef&&this.hasPopupData&&this._createPopup(),ot.append(It)}return this.container.append(ot),this.container}getElementsToTriggerPopup(){return t(this,tt)}addHighlightArea(){this.container.classList.add("highlightArea")}}tt=new WeakMap,d.InkAnnotationElement=j;class rt extends b{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}class C extends b{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class U extends b{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class Y extends b{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class S extends b{constructor(w){super(w,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}d.StampAnnotationElement=S;class e extends b{constructor(y){var ot;super(y,{isRenderable:!0});Q(this,kt);Q(this,st,null);const{filename:H,content:at}=this.data.file;this.filename=(0,x.getFilenameFromUrl)(H,!0),this.content=at,(ot=this.linkService.eventBus)==null||ot.dispatch("fileattachmentannotation",{source:this,filename:H,content:at})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:y,data:H}=this;let at;H.hasAppearance||H.fillAlpha===0?at=document.createElement("div"):(at=document.createElement("img"),at.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(H.name)?"paperclip":"pushpin"}.svg`,H.fillAlpha&&H.fillAlpha<1&&(at.style=`filter: opacity(${Math.round(H.fillAlpha*100)}%);`)),at.addEventListener("dblclick",z(this,kt,si).bind(this)),et(this,st,at);const{isMac:ot}=c.FeatureTest.platform;return y.addEventListener("keydown",gt=>{gt.key==="Enter"&&(ot?gt.metaKey:gt.ctrlKey)&&z(this,kt,si).call(this)}),!H.popupRef&&this.hasPopupData?this._createPopup():at.classList.add("popupTriggerArea"),y.append(at),y}getElementsToTriggerPopup(){return t(this,st)}addHighlightArea(){this.container.classList.add("highlightArea")}}st=new WeakMap,kt=new WeakSet,si=function(){var y;(y=this.downloadManager)==null||y.openOrDownloadData(this.container,this.content,this.filename)};class i{constructor({div:w,accessibilityManager:y,annotationCanvasMap:H,l10n:at,page:ot,viewport:gt}){Q(this,Ct);Q(this,Gt,null);Q(this,D,null);Q(this,ft,new Map);this.div=w,et(this,Gt,y),et(this,D,H),this.l10n=at,this.page=ot,this.viewport=gt,this.zIndex=0,this.l10n||(this.l10n=mt.NullL10n)}async render(w){const{annotations:y}=w,H=this.div;(0,x.setLayerDimensions)(H,this.viewport);const at=new Map,ot={data:null,layer:H,linkService:w.linkService,downloadManager:w.downloadManager,imageResourcesPath:w.imageResourcesPath||"",renderForms:w.renderForms!==!1,svgFactory:new x.DOMSVGFactory,annotationStorage:w.annotationStorage||new ct.AnnotationStorage,enableScripting:w.enableScripting===!0,hasJSActions:w.hasJSActions,fieldObjects:w.fieldObjects,parent:this,elements:null};for(const gt of y){if(gt.noHTML)continue;const Et=gt.annotationType===c.AnnotationType.POPUP;if(Et){const xt=at.get(gt.id);if(!xt)continue;ot.elements=xt}else{const{width:xt,height:Ht}=O(gt.rect);if(xt<=0||Ht<=0)continue}ot.data=gt;const It=v.create(ot);if(!It.isRenderable)continue;if(!Et&&gt.popupRef){const xt=at.get(gt.popupRef);xt?xt.push(It):at.set(gt.popupRef,[It])}It.annotationEditorType>0&&t(this,ft).set(It.data.id,It);const Mt=It.render();gt.hidden&&(Mt.style.visibility="hidden"),z(this,Ct,Ri).call(this,Mt,gt.id)}z(this,Ct,ni).call(this),await this.l10n.translate(H)}update({viewport:w}){const y=this.div;this.viewport=w,(0,x.setLayerDimensions)(y,{rotation:w.rotation}),z(this,Ct,ni).call(this),y.hidden=!1}getEditableAnnotations(){return Array.from(t(this,ft).values())}getEditableAnnotation(w){return t(this,ft).get(w)}}Gt=new WeakMap,D=new WeakMap,ft=new WeakMap,Ct=new WeakSet,Ri=function(w,y){var at;const H=w.firstChild||w;H.id=`${c.AnnotationPrefix}${y}`,this.div.append(w),(at=t(this,Gt))==null||at.moveElementInDOM(this.div,w,H,!1)},ni=function(){if(!t(this,D))return;const w=this.div;for(const[y,H]of t(this,D)){const at=w.querySelector(`[data-annotation-id="${y}"]`);if(!at)continue;const{firstChild:ot}=at;ot?ot.nodeName==="CANVAS"?ot.replaceWith(H):ot.before(H):at.append(H)}t(this,D).clear()},d.AnnotationLayer=i},(St,d)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.ColorConverters=void 0;function lt(ct){return Math.floor(Math.max(0,Math.min(1,ct))*255).toString(16).padStart(2,"0")}function c(ct){return Math.max(0,Math.min(255,255*ct))}class x{static CMYK_G([V,mt,B,R]){return["G",1-Math.min(1,.3*V+.59*B+.11*mt+R)]}static G_CMYK([V]){return["CMYK",0,0,0,1-V]}static G_RGB([V]){return["RGB",V,V,V]}static G_rgb([V]){return V=c(V),[V,V,V]}static G_HTML([V]){const mt=lt(V);return`#${mt}${mt}${mt}`}static RGB_G([V,mt,B]){return["G",.3*V+.59*mt+.11*B]}static RGB_rgb(V){return V.map(c)}static RGB_HTML(V){return`#${V.map(lt).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([V,mt,B,R]){return["RGB",1-Math.min(1,V+R),1-Math.min(1,B+R),1-Math.min(1,mt+R)]}static CMYK_rgb([V,mt,B,R]){return[c(1-Math.min(1,V+R)),c(1-Math.min(1,B+R)),c(1-Math.min(1,mt+R))]}static CMYK_HTML(V){const mt=this.CMYK_RGB(V).slice(1);return this.RGB_HTML(mt)}static RGB_CMYK([V,mt,B]){const R=1-V,g=1-mt,N=1-B,O=Math.min(R,g,N);return["CMYK",R,g,N,O]}}d.ColorConverters=x},(St,d)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.NullL10n=void 0,d.getL10nFallback=c;const lt={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",rendering_error:"An error occurred while rendering the page.",annotation_date_string:"{{date}}, {{time}}",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text2_default_content:"Start typing…",editor_free_text2_aria_label:"Text Editor",editor_ink2_aria_label:"Draw Editor",editor_ink_canvas_aria_label:"User-created image",editor_alt_text_button_label:"Alt text",editor_alt_text_edit_button_label:"Edit alt text",editor_alt_text_decorative_tooltip:"Marked as decorative"};lt.print_progress_percent="{{progress}}%";function c(V,mt){switch(V){case"find_match_count":V=`find_match_count[${mt.total===1?"one":"other"}]`;break;case"find_match_count_limit":V=`find_match_count_limit[${mt.limit===1?"one":"other"}]`;break}return lt[V]||""}function x(V,mt){return mt?V.replaceAll(/\{\{\s*(\w+)\s*\}\}/g,(B,R)=>R in mt?mt[R]:"{{"+R+"}}"):V}const ct={async getLanguage(){return"en-us"},async getDirection(){return"ltr"},async get(V,mt=null,B=c(V,mt)){return x(B,mt)},async translate(V){}};d.NullL10n=ct},(St,d,lt)=>{Object.defineProperty(d,"__esModule",{value:!0}),d.XfaLayer=void 0;var c=lt(25);class x{static setupStorage(V,mt,B,R,g){const N=R.getValue(mt,{value:null});switch(B.name){case"textarea":if(N.value!==null&&(V.textContent=N.value),g==="print")break;V.addEventListener("input",O=>{R.setValue(mt,{value:O.target.value})});break;case"input":if(B.attributes.type==="radio"||B.attributes.type==="checkbox"){if(N.value===B.attributes.xfaOn?V.setAttribute("checked",!0):N.value===B.attributes.xfaOff&&V.removeAttribute("checked"),g==="print")break;V.addEventListener("change",O=>{R.setValue(mt,{value:O.target.checked?O.target.getAttribute("xfaOn"):O.target.getAttribute("xfaOff")})})}else{if(N.value!==null&&V.setAttribute("value",N.value),g==="print")break;V.addEventListener("input",O=>{R.setValue(mt,{value:O.target.value})})}break;case"select":if(N.value!==null){V.setAttribute("value",N.value);for(const O of B.children)O.attributes.value===N.value?O.attributes.selected=!0:O.attributes.hasOwnProperty("selected")&&delete O.attributes.selected}V.addEventListener("input",O=>{const v=O.target.options,b=v.selectedIndex===-1?"":v[v.selectedIndex].value;R.setValue(mt,{value:b})});break}}static setAttributes({html:V,element:mt,storage:B=null,intent:R,linkService:g}){const{attributes:N}=mt,O=V instanceof HTMLAnchorElement;N.type==="radio"&&(N.name=`${N.name}-${R}`);for(const[v,b]of Object.entries(N))if(b!=null)switch(v){case"class":b.length&&V.setAttribute(v,b.join(" "));break;case"dataId":break;case"id":V.setAttribute("data-element-id",b);break;case"style":Object.assign(V.style,b);break;case"textContent":V.textContent=b;break;default:(!O||v!=="href"&&v!=="newWindow")&&V.setAttribute(v,b)}O&&g.addLinkAttributes(V,N.href,N.newWindow),B&&N.dataId&&this.setupStorage(V,N.dataId,mt,B)}static render(V){var E;const mt=V.annotationStorage,B=V.linkService,R=V.xfaHtml,g=V.intent||"display",N=document.createElement(R.name);R.attributes&&this.setAttributes({html:N,element:R,intent:g,linkService:B});const O=[[R,-1,N]],v=V.div;if(v.append(N),V.viewport){const f=`matrix(${V.viewport.transform.join(",")})`;v.style.transform=f}g!=="richText"&&v.setAttribute("class","xfaLayer xfaFont");const b=[];for(;O.length>0;){const[f,h,m]=O.at(-1);if(h+1===f.children.length){O.pop();continue}const I=f.children[++O.at(-1)[1]];if(I===null)continue;const{name:A}=I;if(A==="#text"){const l=document.createTextNode(I.value);b.push(l),m.append(l);continue}const r=(E=I==null?void 0:I.attributes)!=null&&E.xmlns?document.createElementNS(I.attributes.xmlns,A):document.createElement(A);if(m.append(r),I.attributes&&this.setAttributes({html:r,element:I,storage:mt,intent:g,linkService:B}),I.children&&I.children.length>0)O.push([I,-1,r]);else if(I.value){const l=document.createTextNode(I.value);c.XfaText.shouldBuildText(A)&&b.push(l),r.append(l)}}for(const f of v.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))f.setAttribute("readOnly",!0);return{textDivs:b}}static update(V){const mt=`matrix(${V.viewport.transform.join(",")})`;V.div.style.transform=mt,V.div.hidden=!1}}d.XfaLayer=x},(St,d,lt)=>{var R,g,N,O,v,b,E,f,h,m,I,A,r,l,s,Di,Ii,Li,Oi,ri,Ni,ai,Bi,Ui,ji,Hi,Wi,ee,oi,Te,xe,le,li,Pe,P,Gi,ci,zi,Xi,hi,ke,ce;Object.defineProperty(d,"__esModule",{value:!0}),d.InkEditor=void 0;var c=lt(1),x=lt(4),ct=lt(29),V=lt(6),mt=lt(5);const yt=class yt extends x.AnnotationEditor{constructor(p){super({...p,name:"inkEditor"});Q(this,s);Q(this,R,0);Q(this,g,0);Q(this,N,this.canvasPointermove.bind(this));Q(this,O,this.canvasPointerleave.bind(this));Q(this,v,this.canvasPointerup.bind(this));Q(this,b,this.canvasPointerdown.bind(this));Q(this,E,new Path2D);Q(this,f,!1);Q(this,h,!1);Q(this,m,!1);Q(this,I,null);Q(this,A,0);Q(this,r,0);Q(this,l,null);this.color=p.color||null,this.thickness=p.thickness||null,this.opacity=p.opacity||null,this.paths=[],this.bezierPath2D=[],this.allRawPaths=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0,this._willKeepAspectRatio=!0}static initialize(p){x.AnnotationEditor.initialize(p,{strings:["editor_ink_canvas_aria_label","editor_ink2_aria_label"]})}static updateDefaultParams(p,M){switch(p){case c.AnnotationEditorParamsType.INK_THICKNESS:yt._defaultThickness=M;break;case c.AnnotationEditorParamsType.INK_COLOR:yt._defaultColor=M;break;case c.AnnotationEditorParamsType.INK_OPACITY:yt._defaultOpacity=M/100;break}}updateParams(p,M){switch(p){case c.AnnotationEditorParamsType.INK_THICKNESS:z(this,s,Di).call(this,M);break;case c.AnnotationEditorParamsType.INK_COLOR:z(this,s,Ii).call(this,M);break;case c.AnnotationEditorParamsType.INK_OPACITY:z(this,s,Li).call(this,M);break}}static get defaultPropertiesToUpdate(){return[[c.AnnotationEditorParamsType.INK_THICKNESS,yt._defaultThickness],[c.AnnotationEditorParamsType.INK_COLOR,yt._defaultColor||x.AnnotationEditor._defaultLineColor],[c.AnnotationEditorParamsType.INK_OPACITY,Math.round(yt._defaultOpacity*100)]]}get propertiesToUpdate(){return[[c.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||yt._defaultThickness],[c.AnnotationEditorParamsType.INK_COLOR,this.color||yt._defaultColor||x.AnnotationEditor._defaultLineColor],[c.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??yt._defaultOpacity))]]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.canvas||(z(this,s,Te).call(this),z(this,s,xe).call(this)),this.isAttachedToDOM||(this.parent.add(this),z(this,s,le).call(this)),z(this,s,ce).call(this)))}remove(){this.canvas!==null&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,t(this,I).disconnect(),et(this,I,null),super.remove())}setParent(p){!this.parent&&p?this._uiManager.removeShouldRescale(this):this.parent&&p===null&&this._uiManager.addShouldRescale(this),super.setParent(p)}onScaleChanging(){const[p,M]=this.parentDimensions,X=this.width*p,J=this.height*M;this.setDimensions(X,J)}enableEditMode(){t(this,f)||this.canvas===null||(super.enableEditMode(),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",t(this,b)))}disableEditMode(){!this.isInEditMode()||this.canvas===null||(super.disableEditMode(),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",t(this,b)))}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return this.paths.length===0||this.paths.length===1&&this.paths[0].length===0}commit(){t(this,f)||(super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),et(this,f,!0),this.div.classList.add("disabled"),z(this,s,ce).call(this,!0),this.makeResizable(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0}))}focusin(p){this._focusEventsAllowed&&(super.focusin(p),this.enableEditMode())}canvasPointerdown(p){p.button!==0||!this.isInEditMode()||t(this,f)||(this.setInForeground(),p.preventDefault(),p.type!=="mouse"&&this.div.focus(),z(this,s,Ni).call(this,p.offsetX,p.offsetY))}canvasPointermove(p){p.preventDefault(),z(this,s,ai).call(this,p.offsetX,p.offsetY)}canvasPointerup(p){p.preventDefault(),z(this,s,oi).call(this,p)}canvasPointerleave(p){z(this,s,oi).call(this,p)}get isResizable(){return!this.isEmpty()&&t(this,f)}render(){if(this.div)return this.div;let p,M;this.width&&(p=this.x,M=this.y),super.render(),x.AnnotationEditor._l10nPromise.get("editor_ink2_aria_label").then(At=>{var $;return($=this.div)==null?void 0:$.setAttribute("aria-label",At)});const[X,J,ut,vt]=z(this,s,Oi).call(this);if(this.setAt(X,J,0,0),this.setDims(ut,vt),z(this,s,Te).call(this),this.width){const[At,$]=this.parentDimensions;this.setAspectRatio(this.width*At,this.height*$),this.setAt(p*At,M*$,this.width*At,this.height*$),et(this,m,!0),z(this,s,le).call(this),this.setDims(this.width*At,this.height*$),z(this,s,ee).call(this),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return z(this,s,xe).call(this),this.div}setDimensions(p,M){const X=Math.round(p),J=Math.round(M);if(t(this,A)===X&&t(this,r)===J)return;et(this,A,X),et(this,r,J),this.canvas.style.visibility="hidden";const[ut,vt]=this.parentDimensions;this.width=p/ut,this.height=M/vt,this.fixAndSetPosition(),t(this,f)&&z(this,s,li).call(this,p,M),z(this,s,le).call(this),z(this,s,ee).call(this),this.canvas.style.visibility="visible",this.fixDims()}static deserialize(p,M,X){var Nt,$t,qt;if(p instanceof ct.InkAnnotationElement)return null;const J=super.deserialize(p,M,X);J.thickness=p.thickness,J.color=c.Util.makeHexColor(...p.color),J.opacity=p.opacity;const[ut,vt]=J.pageDimensions,At=J.width*ut,$=J.height*vt,Tt=J.parentScale,wt=p.thickness/2;et(J,f,!0),et(J,A,Math.round(At)),et(J,r,Math.round($));const{paths:jt,rect:Bt,rotation:Xt}=p;for(let{bezier:bt}of jt){bt=z(Nt=yt,P,zi).call(Nt,bt,Bt,Xt);const tt=[];J.paths.push(tt);let st=Tt*(bt[0]-wt),kt=Tt*(bt[1]-wt);for(let Gt=2,D=bt.length;Gt<D;Gt+=6){const ft=Tt*(bt[Gt]-wt),Ct=Tt*(bt[Gt+1]-wt),Dt=Tt*(bt[Gt+2]-wt),Ot=Tt*(bt[Gt+3]-wt),Pt=Tt*(bt[Gt+4]-wt),w=Tt*(bt[Gt+5]-wt);tt.push([[st,kt],[ft,Ct],[Dt,Ot],[Pt,w]]),st=Pt,kt=w}const zt=z(this,P,Gi).call(this,tt);J.bezierPath2D.push(zt)}const Ft=z($t=J,s,hi).call($t);return et(J,g,Math.max(x.AnnotationEditor.MIN_SIZE,Ft[2]-Ft[0])),et(J,R,Math.max(x.AnnotationEditor.MIN_SIZE,Ft[3]-Ft[1])),z(qt=J,s,li).call(qt,At,$),J}serialize(){if(this.isEmpty())return null;const p=this.getRect(0,0),M=x.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:c.AnnotationEditorType.INK,color:M,thickness:this.thickness,opacity:this.opacity,paths:z(this,s,Xi).call(this,this.scaleFactor/this.parentScale,this.translationX,this.translationY,p),pageIndex:this.pageIndex,rect:p,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}};R=new WeakMap,g=new WeakMap,N=new WeakMap,O=new WeakMap,v=new WeakMap,b=new WeakMap,E=new WeakMap,f=new WeakMap,h=new WeakMap,m=new WeakMap,I=new WeakMap,A=new WeakMap,r=new WeakMap,l=new WeakMap,s=new WeakSet,Di=function(p){const M=this.thickness;this.addCommands({cmd:()=>{this.thickness=p,z(this,s,ce).call(this)},undo:()=>{this.thickness=M,z(this,s,ce).call(this)},mustExec:!0,type:c.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})},Ii=function(p){const M=this.color;this.addCommands({cmd:()=>{this.color=p,z(this,s,ee).call(this)},undo:()=>{this.color=M,z(this,s,ee).call(this)},mustExec:!0,type:c.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})},Li=function(p){p/=100;const M=this.opacity;this.addCommands({cmd:()=>{this.opacity=p,z(this,s,ee).call(this)},undo:()=>{this.opacity=M,z(this,s,ee).call(this)},mustExec:!0,type:c.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})},Oi=function(){const{parentRotation:p,parentDimensions:[M,X]}=this;switch(p){case 90:return[0,X,X,M];case 180:return[M,X,M,X];case 270:return[M,0,X,M];default:return[0,0,M,X]}},ri=function(){const{ctx:p,color:M,opacity:X,thickness:J,parentScale:ut,scaleFactor:vt}=this;p.lineWidth=J*ut/vt,p.lineCap="round",p.lineJoin="round",p.miterLimit=10,p.strokeStyle=`${M}${(0,mt.opacityToHex)(X)}`},Ni=function(p,M){this.canvas.addEventListener("contextmenu",V.noContextMenu),this.canvas.addEventListener("pointerleave",t(this,O)),this.canvas.addEventListener("pointermove",t(this,N)),this.canvas.addEventListener("pointerup",t(this,v)),this.canvas.removeEventListener("pointerdown",t(this,b)),this.isEditing=!0,t(this,m)||(et(this,m,!0),z(this,s,le).call(this),this.thickness||(this.thickness=yt._defaultThickness),this.color||(this.color=yt._defaultColor||x.AnnotationEditor._defaultLineColor),this.opacity??(this.opacity=yt._defaultOpacity)),this.currentPath.push([p,M]),et(this,h,!1),z(this,s,ri).call(this),et(this,l,()=>{z(this,s,ji).call(this),t(this,l)&&window.requestAnimationFrame(t(this,l))}),window.requestAnimationFrame(t(this,l))},ai=function(p,M){const[X,J]=this.currentPath.at(-1);if(this.currentPath.length>1&&p===X&&M===J)return;const ut=this.currentPath;let vt=t(this,E);if(ut.push([p,M]),et(this,h,!0),ut.length<=2){vt.moveTo(...ut[0]),vt.lineTo(p,M);return}ut.length===3&&(et(this,E,vt=new Path2D),vt.moveTo(...ut[0])),z(this,s,Hi).call(this,vt,...ut.at(-3),...ut.at(-2),p,M)},Bi=function(){if(this.currentPath.length===0)return;const p=this.currentPath.at(-1);t(this,E).lineTo(...p)},Ui=function(p,M){et(this,l,null),p=Math.min(Math.max(p,0),this.canvas.width),M=Math.min(Math.max(M,0),this.canvas.height),z(this,s,ai).call(this,p,M),z(this,s,Bi).call(this);let X;if(this.currentPath.length!==1)X=z(this,s,Wi).call(this);else{const $=[p,M];X=[[$,$.slice(),$.slice(),$]]}const J=t(this,E),ut=this.currentPath;this.currentPath=[],et(this,E,new Path2D);const vt=()=>{this.allRawPaths.push(ut),this.paths.push(X),this.bezierPath2D.push(J),this.rebuild()},At=()=>{this.allRawPaths.pop(),this.paths.pop(),this.bezierPath2D.pop(),this.paths.length===0?this.remove():(this.canvas||(z(this,s,Te).call(this),z(this,s,xe).call(this)),z(this,s,ce).call(this))};this.addCommands({cmd:vt,undo:At,mustExec:!0})},ji=function(){if(!t(this,h))return;et(this,h,!1);const p=Math.ceil(this.thickness*this.parentScale),M=this.currentPath.slice(-3),X=M.map(vt=>vt[0]),J=M.map(vt=>vt[1]);Math.min(...X)-p,Math.max(...X)+p,Math.min(...J)-p,Math.max(...J)+p;const{ctx:ut}=this;ut.save(),ut.clearRect(0,0,this.canvas.width,this.canvas.height);for(const vt of this.bezierPath2D)ut.stroke(vt);ut.stroke(t(this,E)),ut.restore()},Hi=function(p,M,X,J,ut,vt,At){const $=(M+J)/2,Tt=(X+ut)/2,wt=(J+vt)/2,jt=(ut+At)/2;p.bezierCurveTo($+2*(J-$)/3,Tt+2*(ut-Tt)/3,wt+2*(J-wt)/3,jt+2*(ut-jt)/3,wt,jt)},Wi=function(){const p=this.currentPath;if(p.length<=2)return[[p[0],p[0],p.at(-1),p.at(-1)]];const M=[];let X,[J,ut]=p[0];for(X=1;X<p.length-2;X++){const[Bt,Xt]=p[X],[Ft,Nt]=p[X+1],$t=(Bt+Ft)/2,qt=(Xt+Nt)/2,bt=[J+2*(Bt-J)/3,ut+2*(Xt-ut)/3],tt=[$t+2*(Bt-$t)/3,qt+2*(Xt-qt)/3];M.push([[J,ut],bt,tt,[$t,qt]]),[J,ut]=[$t,qt]}const[vt,At]=p[X],[$,Tt]=p[X+1],wt=[J+2*(vt-J)/3,ut+2*(At-ut)/3],jt=[$+2*(vt-$)/3,Tt+2*(At-Tt)/3];return M.push([[J,ut],wt,jt,[$,Tt]]),M},ee=function(){if(this.isEmpty()){z(this,s,Pe).call(this);return}z(this,s,ri).call(this);const{canvas:p,ctx:M}=this;M.setTransform(1,0,0,1,0,0),M.clearRect(0,0,p.width,p.height),z(this,s,Pe).call(this);for(const X of this.bezierPath2D)M.stroke(X)},oi=function(p){this.canvas.removeEventListener("pointerleave",t(this,O)),this.canvas.removeEventListener("pointermove",t(this,N)),this.canvas.removeEventListener("pointerup",t(this,v)),this.canvas.addEventListener("pointerdown",t(this,b)),setTimeout(()=>{this.canvas.removeEventListener("contextmenu",V.noContextMenu)},10),z(this,s,Ui).call(this,p.offsetX,p.offsetY),this.addToAnnotationStorage(),this.setInBackground()},Te=function(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",x.AnnotationEditor._l10nPromise.get("editor_ink_canvas_aria_label").then(p=>{var M;return(M=this.canvas)==null?void 0:M.setAttribute("aria-label",p)}),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")},xe=function(){et(this,I,new ResizeObserver(p=>{const M=p[0].contentRect;M.width&&M.height&&this.setDimensions(M.width,M.height)})),t(this,I).observe(this.div)},le=function(){if(!t(this,m))return;const[p,M]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*p),this.canvas.height=Math.ceil(this.height*M),z(this,s,Pe).call(this)},li=function(p,M){const X=z(this,s,ke).call(this),J=(p-X)/t(this,g),ut=(M-X)/t(this,R);this.scaleFactor=Math.min(J,ut)},Pe=function(){const p=z(this,s,ke).call(this)/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+p,this.translationY*this.scaleFactor+p)},P=new WeakSet,Gi=function(p){const M=new Path2D;for(let X=0,J=p.length;X<J;X++){const[ut,vt,At,$]=p[X];X===0&&M.moveTo(...ut),M.bezierCurveTo(vt[0],vt[1],At[0],At[1],$[0],$[1])}return M},ci=function(p,M,X){const[J,ut,vt,At]=M;switch(X){case 0:for(let $=0,Tt=p.length;$<Tt;$+=2)p[$]+=J,p[$+1]=At-p[$+1];break;case 90:for(let $=0,Tt=p.length;$<Tt;$+=2){const wt=p[$];p[$]=p[$+1]+J,p[$+1]=wt+ut}break;case 180:for(let $=0,Tt=p.length;$<Tt;$+=2)p[$]=vt-p[$],p[$+1]+=ut;break;case 270:for(let $=0,Tt=p.length;$<Tt;$+=2){const wt=p[$];p[$]=vt-p[$+1],p[$+1]=At-wt}break;default:throw new Error("Invalid rotation")}return p},zi=function(p,M,X){const[J,ut,vt,At]=M;switch(X){case 0:for(let $=0,Tt=p.length;$<Tt;$+=2)p[$]-=J,p[$+1]=At-p[$+1];break;case 90:for(let $=0,Tt=p.length;$<Tt;$+=2){const wt=p[$];p[$]=p[$+1]-ut,p[$+1]=wt-J}break;case 180:for(let $=0,Tt=p.length;$<Tt;$+=2)p[$]=vt-p[$],p[$+1]-=ut;break;case 270:for(let $=0,Tt=p.length;$<Tt;$+=2){const wt=p[$];p[$]=At-p[$+1],p[$+1]=vt-wt}break;default:throw new Error("Invalid rotation")}return p},Xi=function(p,M,X,J){var Tt,wt;const ut=[],vt=this.thickness/2,At=p*M+vt,$=p*X+vt;for(const jt of this.paths){const Bt=[],Xt=[];for(let Ft=0,Nt=jt.length;Ft<Nt;Ft++){const[$t,qt,bt,tt]=jt[Ft],st=p*$t[0]+At,kt=p*$t[1]+$,zt=p*qt[0]+At,Gt=p*qt[1]+$,D=p*bt[0]+At,ft=p*bt[1]+$,Ct=p*tt[0]+At,Dt=p*tt[1]+$;Ft===0&&(Bt.push(st,kt),Xt.push(st,kt)),Bt.push(zt,Gt,D,ft,Ct,Dt),Xt.push(zt,Gt),Ft===Nt-1&&Xt.push(Ct,Dt)}ut.push({bezier:z(Tt=yt,P,ci).call(Tt,Bt,J,this.rotation),points:z(wt=yt,P,ci).call(wt,Xt,J,this.rotation)})}return ut},hi=function(){let p=1/0,M=-1/0,X=1/0,J=-1/0;for(const ut of this.paths)for(const[vt,At,$,Tt]of ut){const wt=c.Util.bezierBoundingBox(...vt,...At,...$,...Tt);p=Math.min(p,wt[0]),X=Math.min(X,wt[1]),M=Math.max(M,wt[2]),J=Math.max(J,wt[3])}return[p,X,M,J]},ke=function(){return t(this,f)?Math.ceil(this.thickness*this.parentScale):0},ce=function(p=!1){if(this.isEmpty())return;if(!t(this,f)){z(this,s,ee).call(this);return}const M=z(this,s,hi).call(this),X=z(this,s,ke).call(this);et(this,g,Math.max(x.AnnotationEditor.MIN_SIZE,M[2]-M[0])),et(this,R,Math.max(x.AnnotationEditor.MIN_SIZE,M[3]-M[1]));const J=Math.ceil(X+t(this,g)*this.scaleFactor),ut=Math.ceil(X+t(this,R)*this.scaleFactor),[vt,At]=this.parentDimensions;this.width=J/vt,this.height=ut/At,this.setAspectRatio(J,ut);const $=this.translationX,Tt=this.translationY;this.translationX=-M[0],this.translationY=-M[1],z(this,s,le).call(this),z(this,s,ee).call(this),et(this,A,J),et(this,r,ut),this.setDims(J,ut);const wt=p?X/this.scaleFactor/2:0;this.translate($-this.translationX-wt,Tt-this.translationY-wt)},Q(yt,P),Kt(yt,"_defaultColor",null),Kt(yt,"_defaultOpacity",1),Kt(yt,"_defaultThickness",1),Kt(yt,"_type","ink");let B=yt;d.InkEditor=B},(St,d,lt)=>{var B,R,g,N,O,v,b,E,f,h,m,me,be,Fe,di,$i,Vi,ui,Me,qi;Object.defineProperty(d,"__esModule",{value:!0}),d.StampEditor=void 0;var c=lt(1),x=lt(4),ct=lt(6),V=lt(29);const _=class _ extends x.AnnotationEditor{constructor(W){super({...W,name:"stampEditor"});Q(this,m);Q(this,B,null);Q(this,R,null);Q(this,g,null);Q(this,N,null);Q(this,O,null);Q(this,v,null);Q(this,b,null);Q(this,E,null);Q(this,f,!1);Q(this,h,!1);et(this,N,W.bitmapUrl),et(this,O,W.bitmapFile)}static initialize(W){x.AnnotationEditor.initialize(W)}static get supportedTypes(){const W=["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"];return(0,c.shadow)(this,"supportedTypes",W.map(q=>`image/${q}`))}static get supportedTypesStr(){return(0,c.shadow)(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(W){return this.supportedTypes.includes(W)}static paste(W,q){q.pasteEditor(c.AnnotationEditorType.STAMP,{bitmapFile:W.getAsFile()})}remove(){var W,q;t(this,R)&&(et(this,B,null),this._uiManager.imageManager.deleteId(t(this,R)),(W=t(this,v))==null||W.remove(),et(this,v,null),(q=t(this,b))==null||q.disconnect(),et(this,b,null)),super.remove()}rebuild(){if(!this.parent){t(this,R)&&z(this,m,Fe).call(this);return}super.rebuild(),this.div!==null&&(t(this,R)&&z(this,m,Fe).call(this),this.isAttachedToDOM||this.parent.add(this))}onceAdded(){this._isDraggable=!0,this.div.focus()}isEmpty(){return!(t(this,g)||t(this,B)||t(this,N)||t(this,O))}get isResizable(){return!0}render(){if(this.div)return this.div;let W,q;if(this.width&&(W=this.x,q=this.y),super.render(),this.div.hidden=!0,t(this,B)?z(this,m,di).call(this):z(this,m,Fe).call(this),this.width){const[j,rt]=this.parentDimensions;this.setAt(W*j,q*rt,this.width*j,this.height*rt)}return this.div}static deserialize(W,q,j){if(W instanceof V.StampAnnotationElement)return null;const rt=super.deserialize(W,q,j),{rect:C,bitmapUrl:U,bitmapId:Y,isSvg:S,accessibilityData:e}=W;Y&&j.imageManager.isValidId(Y)?et(rt,R,Y):et(rt,N,U),et(rt,f,S);const[i,u]=rt.pageDimensions;return rt.width=(C[2]-C[0])/i,rt.height=(C[3]-C[1])/u,e&&(rt.altTextData=e),rt}serialize(W=!1,q=null){if(this.isEmpty())return null;const j={annotationType:c.AnnotationEditorType.STAMP,bitmapId:t(this,R),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:t(this,f),structTreeParentId:this._structTreeParentId};if(W)return j.bitmapUrl=z(this,m,Me).call(this,!0),j.accessibilityData=this.altTextData,j;const{decorative:rt,altText:C}=this.altTextData;if(!rt&&C&&(j.accessibilityData={type:"Figure",alt:C}),q===null)return j;q.stamps||(q.stamps=new Map);const U=t(this,f)?(j.rect[2]-j.rect[0])*(j.rect[3]-j.rect[1]):null;if(!q.stamps.has(t(this,R)))q.stamps.set(t(this,R),{area:U,serialized:j}),j.bitmap=z(this,m,Me).call(this,!1);else if(t(this,f)){const Y=q.stamps.get(t(this,R));U>Y.area&&(Y.area=U,Y.serialized.bitmap.close(),Y.serialized.bitmap=z(this,m,Me).call(this,!1))}return j}};B=new WeakMap,R=new WeakMap,g=new WeakMap,N=new WeakMap,O=new WeakMap,v=new WeakMap,b=new WeakMap,E=new WeakMap,f=new WeakMap,h=new WeakMap,m=new WeakSet,me=function(W,q=!1){if(!W){this.remove();return}et(this,B,W.bitmap),q||(et(this,R,W.id),et(this,f,W.isSvg)),z(this,m,di).call(this)},be=function(){et(this,g,null),this._uiManager.enableWaiting(!1),t(this,v)&&this.div.focus()},Fe=function(){if(t(this,R)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(t(this,R)).then(q=>z(this,m,me).call(this,q,!0)).finally(()=>z(this,m,be).call(this));return}if(t(this,N)){const q=t(this,N);et(this,N,null),this._uiManager.enableWaiting(!0),et(this,g,this._uiManager.imageManager.getFromUrl(q).then(j=>z(this,m,me).call(this,j)).finally(()=>z(this,m,be).call(this)));return}if(t(this,O)){const q=t(this,O);et(this,O,null),this._uiManager.enableWaiting(!0),et(this,g,this._uiManager.imageManager.getFromFile(q).then(j=>z(this,m,me).call(this,j)).finally(()=>z(this,m,be).call(this)));return}const W=document.createElement("input");W.type="file",W.accept=_.supportedTypesStr,et(this,g,new Promise(q=>{W.addEventListener("change",async()=>{if(!W.files||W.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const j=await this._uiManager.imageManager.getFromFile(W.files[0]);z(this,m,me).call(this,j)}q()}),W.addEventListener("cancel",()=>{this.remove(),q()})}).finally(()=>z(this,m,be).call(this))),W.click()},di=function(){const{div:W}=this;let{width:q,height:j}=t(this,B);const[rt,C]=this.pageDimensions,U=.75;if(this.width)q=this.width*rt,j=this.height*C;else if(q>U*rt||j>U*C){const i=Math.min(U*rt/q,U*C/j);q*=i,j*=i}const[Y,S]=this.parentDimensions;this.setDims(q*Y/rt,j*S/C),this._uiManager.enableWaiting(!1);const e=et(this,v,document.createElement("canvas"));W.append(e),W.hidden=!1,z(this,m,ui).call(this,q,j),z(this,m,qi).call(this),t(this,h)||(this.parent.addUndoableEditor(this),et(this,h,!0)),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"inserted_image"}}}),this.addAltTextButton()},$i=function(W,q){var U;const[j,rt]=this.parentDimensions;this.width=W/j,this.height=q/rt,this.setDims(W,q),(U=this._initialOptions)!=null&&U.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,t(this,E)!==null&&clearTimeout(t(this,E)),et(this,E,setTimeout(()=>{et(this,E,null),z(this,m,ui).call(this,W,q)},200))},Vi=function(W,q){const{width:j,height:rt}=t(this,B);let C=j,U=rt,Y=t(this,B);for(;C>2*W||U>2*q;){const S=C,e=U;C>2*W&&(C=C>=16384?Math.floor(C/2)-1:Math.ceil(C/2)),U>2*q&&(U=U>=16384?Math.floor(U/2)-1:Math.ceil(U/2));const i=new OffscreenCanvas(C,U);i.getContext("2d").drawImage(Y,0,0,S,e,0,0,C,U),Y=i.transferToImageBitmap()}return Y},ui=function(W,q){W=Math.ceil(W),q=Math.ceil(q);const j=t(this,v);if(!j||j.width===W&&j.height===q)return;j.width=W,j.height=q;const rt=t(this,f)?t(this,B):z(this,m,Vi).call(this,W,q),C=j.getContext("2d");C.filter=this._uiManager.hcmFilter,C.drawImage(rt,0,0,rt.width,rt.height,0,0,W,q)},Me=function(W){if(W){if(t(this,f)){const rt=this._uiManager.imageManager.getSvgUrl(t(this,R));if(rt)return rt}const q=document.createElement("canvas");return{width:q.width,height:q.height}=t(this,B),q.getContext("2d").drawImage(t(this,B),0,0),q.toDataURL()}if(t(this,f)){const[q,j]=this.pageDimensions,rt=Math.round(this.width*q*ct.PixelsPerInch.PDF_TO_CSS_UNITS),C=Math.round(this.height*j*ct.PixelsPerInch.PDF_TO_CSS_UNITS),U=new OffscreenCanvas(rt,C);return U.getContext("2d").drawImage(t(this,B),0,0,t(this,B).width,t(this,B).height,0,0,rt,C),U.transferToImageBitmap()}return structuredClone(t(this,B))},qi=function(){et(this,b,new ResizeObserver(W=>{const q=W[0].contentRect;q.width&&q.height&&z(this,m,$i).call(this,q.width,q.height)})),t(this,b).observe(this.div)},Kt(_,"_type","stamp");let mt=_;d.StampEditor=mt}],__webpack_module_cache__={};function __w_pdfjs_require__(St){var d=__webpack_module_cache__[St];if(d!==void 0)return d.exports;var lt=__webpack_module_cache__[St]={exports:{}};return __webpack_modules__[St](lt,lt.exports,__w_pdfjs_require__),lt.exports}var __webpack_exports__={};return(()=>{var St=__webpack_exports__;Object.defineProperty(St,"__esModule",{value:!0}),Object.defineProperty(St,"AbortException",{enumerable:!0,get:function(){return d.AbortException}}),Object.defineProperty(St,"AnnotationEditorLayer",{enumerable:!0,get:function(){return ct.AnnotationEditorLayer}}),Object.defineProperty(St,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return d.AnnotationEditorParamsType}}),Object.defineProperty(St,"AnnotationEditorType",{enumerable:!0,get:function(){return d.AnnotationEditorType}}),Object.defineProperty(St,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return V.AnnotationEditorUIManager}}),Object.defineProperty(St,"AnnotationLayer",{enumerable:!0,get:function(){return mt.AnnotationLayer}}),Object.defineProperty(St,"AnnotationMode",{enumerable:!0,get:function(){return d.AnnotationMode}}),Object.defineProperty(St,"CMapCompressionType",{enumerable:!0,get:function(){return d.CMapCompressionType}}),Object.defineProperty(St,"DOMSVGFactory",{enumerable:!0,get:function(){return c.DOMSVGFactory}}),Object.defineProperty(St,"FeatureTest",{enumerable:!0,get:function(){return d.FeatureTest}}),Object.defineProperty(St,"GlobalWorkerOptions",{enumerable:!0,get:function(){return B.GlobalWorkerOptions}}),Object.defineProperty(St,"ImageKind",{enumerable:!0,get:function(){return d.ImageKind}}),Object.defineProperty(St,"InvalidPDFException",{enumerable:!0,get:function(){return d.InvalidPDFException}}),Object.defineProperty(St,"MissingPDFException",{enumerable:!0,get:function(){return d.MissingPDFException}}),Object.defineProperty(St,"OPS",{enumerable:!0,get:function(){return d.OPS}}),Object.defineProperty(St,"PDFDataRangeTransport",{enumerable:!0,get:function(){return lt.PDFDataRangeTransport}}),Object.defineProperty(St,"PDFDateString",{enumerable:!0,get:function(){return c.PDFDateString}}),Object.defineProperty(St,"PDFWorker",{enumerable:!0,get:function(){return lt.PDFWorker}}),Object.defineProperty(St,"PasswordResponses",{enumerable:!0,get:function(){return d.PasswordResponses}}),Object.defineProperty(St,"PermissionFlag",{enumerable:!0,get:function(){return d.PermissionFlag}}),Object.defineProperty(St,"PixelsPerInch",{enumerable:!0,get:function(){return c.PixelsPerInch}}),Object.defineProperty(St,"PromiseCapability",{enumerable:!0,get:function(){return d.PromiseCapability}}),Object.defineProperty(St,"RenderingCancelledException",{enumerable:!0,get:function(){return c.RenderingCancelledException}}),Object.defineProperty(St,"SVGGraphics",{enumerable:!0,get:function(){return lt.SVGGraphics}}),Object.defineProperty(St,"UnexpectedResponseException",{enumerable:!0,get:function(){return d.UnexpectedResponseException}}),Object.defineProperty(St,"Util",{enumerable:!0,get:function(){return d.Util}}),Object.defineProperty(St,"VerbosityLevel",{enumerable:!0,get:function(){return d.VerbosityLevel}}),Object.defineProperty(St,"XfaLayer",{enumerable:!0,get:function(){return R.XfaLayer}}),Object.defineProperty(St,"build",{enumerable:!0,get:function(){return lt.build}}),Object.defineProperty(St,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return d.createValidAbsoluteUrl}}),Object.defineProperty(St,"getDocument",{enumerable:!0,get:function(){return lt.getDocument}}),Object.defineProperty(St,"getFilenameFromUrl",{enumerable:!0,get:function(){return c.getFilenameFromUrl}}),Object.defineProperty(St,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return c.getPdfFilenameFromUrl}}),Object.defineProperty(St,"getXfaPageViewport",{enumerable:!0,get:function(){return c.getXfaPageViewport}}),Object.defineProperty(St,"isDataScheme",{enumerable:!0,get:function(){return c.isDataScheme}}),Object.defineProperty(St,"isPdfFile",{enumerable:!0,get:function(){return c.isPdfFile}}),Object.defineProperty(St,"loadScript",{enumerable:!0,get:function(){return c.loadScript}}),Object.defineProperty(St,"noContextMenu",{enumerable:!0,get:function(){return c.noContextMenu}}),Object.defineProperty(St,"normalizeUnicode",{enumerable:!0,get:function(){return d.normalizeUnicode}}),Object.defineProperty(St,"renderTextLayer",{enumerable:!0,get:function(){return x.renderTextLayer}}),Object.defineProperty(St,"setLayerDimensions",{enumerable:!0,get:function(){return c.setLayerDimensions}}),Object.defineProperty(St,"shadow",{enumerable:!0,get:function(){return d.shadow}}),Object.defineProperty(St,"updateTextLayer",{enumerable:!0,get:function(){return x.updateTextLayer}}),Object.defineProperty(St,"version",{enumerable:!0,get:function(){return lt.version}});var d=__w_pdfjs_require__(1),lt=__w_pdfjs_require__(2),c=__w_pdfjs_require__(6),x=__w_pdfjs_require__(26),ct=__w_pdfjs_require__(27),V=__w_pdfjs_require__(5),mt=__w_pdfjs_require__(29),B=__w_pdfjs_require__(14),R=__w_pdfjs_require__(32)})(),__webpack_exports__})())})(pdf$2);var pdfExports=pdf$2.exports;const pdf=getDefaultExportFromCjs(pdfExports),pdf$1=_mergeNamespaces({__proto__:null,default:pdf},[pdfExports]);export{pdfExports as a,pdf$1 as p};
