const { app, BrowserWindow, ipcMain, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let backendProcess;

// Configuration pour développement
const isDev = process.env.NODE_ENV === 'development';
const isHotReload = process.env.ELECTRON_HOT_RELOAD === 'true';

console.log('🚀 Démarrage WeMa IA Electron');
console.log(`📋 Mode: ${isDev ? 'Développement' : 'Production'}`);
console.log(`🔥 Hot Reload: ${isHotReload ? 'Activé' : 'Désactivé'}`);

function createWindow() {
  console.log('🖥️ Création de la fenêtre principale...');
  
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false // Pour développement seulement
    },
    icon: path.join(__dirname, 'wema-logo-light.png'),
    title: 'WeMa IA - Assistant IA Professionnel',
    show: false,
    titleBarStyle: 'default'
  });

  // Charger l'application
  if (isDev) {
    console.log('🌐 Chargement depuis serveur de développement...');
    mainWindow.loadURL('http://localhost:5173');
    
    // Ouvrir DevTools en développement
    mainWindow.webContents.openDevTools();
    
    // Hot reload - recharger quand les fichiers changent
    if (isHotReload) {
      console.log('🔥 Hot reload activé');
      mainWindow.webContents.on('did-finish-load', () => {
        console.log('✅ Page chargée');
      });
    }
  } else {
    console.log('📁 Chargement depuis fichiers statiques...');
    mainWindow.loadFile(path.join(__dirname, 'dist/index.html'));
  }

  // Afficher la fenêtre quand elle est prête
  mainWindow.once('ready-to-show', () => {
    console.log('✅ Fenêtre prête - Affichage');
    mainWindow.show();
    
    if (isDev) {
      mainWindow.focus();
    }
  });

  // Gestion des erreurs de chargement
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('❌ Erreur de chargement:', errorCode, errorDescription);
  });

  // Ouvrir les liens externes dans le navigateur par défaut
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    console.log('🔗 Ouverture lien externe:', url);
    shell.openExternal(url);
    return { action: 'deny' };
  });

  // Intercepter les clics sur les liens
  mainWindow.webContents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    console.log('🔗 Navigation externe:', navigationUrl);
    shell.openExternal(navigationUrl);
  });

  // Démarrer le backend Python
  if (!process.env.DISABLE_BACKEND) {
    startBackend();
  } else {
    console.log('🔧 Backend Python désactivé (DISABLE_BACKEND=true)');
  }
}

function startBackend() {
  console.log('🐍 Démarrage du backend Python...');
  
  try {
    const backendPath = path.join(__dirname, '../py_backend');
    console.log(`📁 Chemin backend: ${backendPath}`);
    
    // Détection Python
    const pythonCommands = ['python', 'python3', 'py'];
    let pythonCmd = 'python';
    
    for (const cmd of pythonCommands) {
      try {
        require('child_process').execSync(`${cmd} --version`, { stdio: 'pipe' });
        pythonCmd = cmd;
        console.log(`✅ Python trouvé: ${cmd}`);
        break;
      } catch (e) {
        console.log(`⚠️ ${cmd} non trouvé`);
      }
    }
    
    backendProcess = spawn(pythonCmd, ['main.py'], {
      cwd: backendPath,
      stdio: ['pipe', 'pipe', 'pipe'],
      env: { ...process.env, PYTHONUNBUFFERED: '1' }
    });
    
    backendProcess.stdout.on('data', (data) => {
      const message = data.toString().trim();
      if (message) {
        console.log('🐍 Backend:', message);
      }
    });
    
    backendProcess.stderr.on('data', (data) => {
      const message = data.toString().trim();
      if (message && !message.includes('DeprecationWarning')) {
        console.log('🐍 Backend Info:', message);
      }
    });
    
    backendProcess.on('close', (code) => {
      console.log(`🐍 Backend fermé avec le code: ${code}`);
    });
    
    backendProcess.on('error', (error) => {
      console.error('❌ Erreur backend:', error.message);
    });
    
    console.log('✅ Backend Python démarré');
    
  } catch (error) {
    console.error('❌ Impossible de démarrer le backend:', error.message);
  }
}

// Événements de l'application
app.whenReady().then(() => {
  console.log('🚀 Electron prêt');
  createWindow();
});

app.on('window-all-closed', () => {
  console.log('🔒 Fermeture de toutes les fenêtres');
  
  // Arrêter le backend
  if (backendProcess && !backendProcess.killed) {
    console.log('🛑 Arrêt du backend Python...');
    backendProcess.kill('SIGTERM');
    
    // Force kill après 3 secondes
    setTimeout(() => {
      if (!backendProcess.killed) {
        backendProcess.kill('SIGKILL');
      }
    }, 3000);
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
  console.error('❌ Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Promesse rejetée:', reason);
});

console.log('📋 Configuration Electron chargée');
