#!/usr/bin/env python3
"""
🚀 Test de performance : Streaming vs Non-streaming
Compare les performances entre les deux modes
"""

import requests
import time

def test_streaming():
    """Test avec streaming"""
    print("🔄 Test STREAMING...")
    
    start_time = time.time()
    response = requests.post(
        'http://localhost:1234/v1/chat/completions',
        json={
            "model": "qwen3-4b",
            "messages": [{"role": "user", "content": "Écris un court paragraphe sur l'intelligence artificielle."}],
            "max_tokens": 200,
            "stream": True
        },
        stream=True,
        timeout=120
    )
    
    content = ""
    first_token_time = None
    
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            if line_str.startswith('data: '):
                data_str = line_str[6:]
                if data_str.strip() == '[DONE]':
                    break
                try:
                    import json
                    data = json.loads(data_str)
                    if 'choices' in data and len(data['choices']) > 0:
                        delta = data['choices'][0].get('delta', {})
                        if 'content' in delta:
                            if first_token_time is None:
                                first_token_time = time.time()
                            content += delta['content']
                except:
                    pass
    
    total_time = time.time() - start_time
    first_token_delay = first_token_time - start_time if first_token_time else 0
    
    print(f"   ⏱️ Temps total: {total_time:.2f}s")
    print(f"   🚀 Premier token: {first_token_delay:.2f}s")
    print(f"   📝 Contenu: {len(content)} caractères")
    
    return total_time, first_token_delay, len(content)

def test_non_streaming():
    """Test sans streaming"""
    print("⚡ Test NON-STREAMING...")
    
    start_time = time.time()
    response = requests.post(
        'http://localhost:1234/v1/chat/completions',
        json={
            "model": "qwen3-4b",
            "messages": [{"role": "user", "content": "Écris un court paragraphe sur l'intelligence artificielle."}],
            "max_tokens": 200,
            "stream": False
        },
        timeout=120
    )
    
    total_time = time.time() - start_time
    
    if response.status_code == 200:
        result = response.json()
        content = result.get('choices', [{}])[0].get('message', {}).get('content', '')
        
        print(f"   ⏱️ Temps total: {total_time:.2f}s")
        print(f"   📝 Contenu: {len(content)} caractères")
        
        return total_time, 0, len(content)
    else:
        print(f"   ❌ Erreur: {response.status_code}")
        return total_time, 0, 0

def main():
    """Comparaison des performances"""
    print("🚀 Comparaison Streaming vs Non-streaming")
    print("=" * 50)
    
    # Test non-streaming
    non_stream_time, _, non_stream_chars = test_non_streaming()
    print()
    
    # Test streaming
    stream_time, first_token_delay, stream_chars = test_streaming()
    print()
    
    # Comparaison
    print("📊 RÉSULTATS:")
    print(f"   Non-streaming: {non_stream_time:.2f}s")
    print(f"   Streaming:     {stream_time:.2f}s (premier token: {first_token_delay:.2f}s)")
    
    if non_stream_time < stream_time:
        gain = ((stream_time - non_stream_time) / stream_time) * 100
        print(f"   🚀 Non-streaming est {gain:.1f}% plus rapide !")
    else:
        loss = ((non_stream_time - stream_time) / non_stream_time) * 100
        print(f"   🔄 Streaming est {loss:.1f}% plus rapide")
    
    print(f"\n💡 RECOMMANDATION:")
    if first_token_delay > 10:
        print("   ❌ Streaming trop lent (>10s premier token) → Utiliser NON-STREAMING")
    elif non_stream_time < stream_time:
        print("   ✅ Non-streaming plus rapide → Utiliser NON-STREAMING")
    else:
        print("   🔄 Streaming acceptable → Garder STREAMING")

if __name__ == "__main__":
    main()
