/**
 * Initializes a third-party proxy module for HTTP(S) requests. Call this function before
 * using the {@link download} and {@link downloadArtifact} APIs if you need proxy support.
 *
 * If the `ELECTRON_GET_USE_PROXY` environment variable is set to `true`, this function will be
 * called automatically for `@electron/get` requests.
 *
 * @category Utility
 * @see {@link https://github.com/gajus/global-agent?tab=readme-ov-file#environment-variables | `global-agent`}
 * documentation for available environment variables.
 *
 * @example
 * ```sh
 * export GLOBAL_AGENT_HTTPS_PROXY="$HTTPS_PROXY"
 * ```
 */
export declare function initializeProxy(): void;
