#!/usr/bin/env python3
"""
🚀 Test des optimisations de streaming frontend
Mesure les performances après optimisation
"""

import requests
import time
import json

def test_streaming_performance():
    """Test de performance du streaming optimisé"""
    print("🚀 Test du streaming optimisé...")
    
    start_time = time.time()
    
    # Test avec une requête plus complexe pour voir l'amélioration
    response = requests.post(
        'http://localhost:1234/v1/chat/completions',
        json={
            "model": "qwen3-4b",
            "messages": [
                {
                    "role": "user", 
                    "content": "Écris un article détaillé de 300 mots sur les avantages de l'intelligence artificielle dans l'éducation. Inclus des exemples concrets et des statistiques."
                }
            ],
            "max_tokens": 500,
            "stream": True,
            "temperature": 0.7
        },
        stream=True,
        timeout=120
    )
    
    content = ""
    chunks_received = 0
    first_token_time = None
    chunk_times = []
    
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            if line_str.startswith('data: '):
                data_str = line_str[6:]
                if data_str.strip() == '[DONE]':
                    break
                try:
                    data = json.loads(data_str)
                    if 'choices' in data and len(data['choices']) > 0:
                        delta = data['choices'][0].get('delta', {})
                        if 'content' in delta:
                            chunk_time = time.time()
                            if first_token_time is None:
                                first_token_time = chunk_time
                            
                            content += delta['content']
                            chunks_received += 1
                            chunk_times.append(chunk_time - start_time)
                except:
                    pass
    
    total_time = time.time() - start_time
    first_token_delay = first_token_time - start_time if first_token_time else 0
    
    # Calcul des métriques
    avg_chunk_interval = 0
    if len(chunk_times) > 1:
        intervals = [chunk_times[i] - chunk_times[i-1] for i in range(1, len(chunk_times))]
        avg_chunk_interval = sum(intervals) / len(intervals)
    
    tokens_per_second = len(content.split()) / total_time if total_time > 0 else 0
    
    print(f"   ⏱️ Temps total: {total_time:.2f}s")
    print(f"   🚀 Premier token: {first_token_delay:.2f}s")
    print(f"   📦 Chunks reçus: {chunks_received}")
    print(f"   📝 Contenu: {len(content)} caractères, {len(content.split())} mots")
    print(f"   ⚡ Vitesse: {tokens_per_second:.1f} mots/seconde")
    print(f"   📊 Intervalle moyen entre chunks: {avg_chunk_interval*1000:.1f}ms")
    
    return {
        'total_time': total_time,
        'first_token_delay': first_token_delay,
        'chunks_received': chunks_received,
        'content_length': len(content),
        'words_count': len(content.split()),
        'tokens_per_second': tokens_per_second,
        'avg_chunk_interval': avg_chunk_interval
    }

def test_frontend_simulation():
    """Simule le comportement du frontend optimisé"""
    print("🎯 Simulation du frontend optimisé...")
    
    # Simuler le debouncing côté frontend
    chunks = []
    updates = 0
    last_update_time = 0
    UPDATE_INTERVAL = 0.1  # 100ms comme dans l'optimiseur
    
    start_time = time.time()
    
    response = requests.post(
        'http://localhost:1234/v1/chat/completions',
        json={
            "model": "qwen3-4b",
            "messages": [{"role": "user", "content": "Compte de 1 à 50 en expliquant chaque nombre."}],
            "max_tokens": 300,
            "stream": True
        },
        stream=True,
        timeout=60
    )
    
    content_buffer = ""
    
    for line in response.iter_lines():
        if line:
            line_str = line.decode('utf-8')
            if line_str.startswith('data: '):
                data_str = line_str[6:]
                if data_str.strip() == '[DONE]':
                    break
                try:
                    data = json.loads(data_str)
                    if 'choices' in data and len(data['choices']) > 0:
                        delta = data['choices'][0].get('delta', {})
                        if 'content' in delta:
                            content_buffer += delta['content']
                            chunks.append(delta['content'])
                            
                            # Simuler le debouncing
                            now = time.time()
                            if (now - last_update_time) >= UPDATE_INTERVAL or len(content_buffer) >= 100:
                                # Simuler une mise à jour UI
                                updates += 1
                                last_update_time = now
                                content_buffer = ""  # Reset buffer
                except:
                    pass
    
    total_time = time.time() - start_time
    
    print(f"   ⏱️ Temps total: {total_time:.2f}s")
    print(f"   📦 Chunks reçus: {len(chunks)}")
    print(f"   🔄 Mises à jour UI: {updates}")
    print(f"   📊 Réduction des updates: {((len(chunks) - updates) / len(chunks) * 100):.1f}%")
    
    return {
        'total_chunks': len(chunks),
        'ui_updates': updates,
        'reduction_percentage': ((len(chunks) - updates) / len(chunks) * 100) if chunks else 0
    }

def main():
    """Test complet des optimisations"""
    print("🚀 Test des optimisations de streaming frontend")
    print("=" * 60)
    
    # Test 1: Performance générale
    perf_results = test_streaming_performance()
    print()
    
    # Test 2: Simulation frontend
    frontend_results = test_frontend_simulation()
    print()
    
    # Analyse des résultats
    print("📊 ANALYSE DES OPTIMISATIONS:")
    print(f"   🚀 Premier token: {perf_results['first_token_delay']:.2f}s")
    
    if perf_results['first_token_delay'] < 5:
        print("   ✅ Excellent - Premier token très rapide")
    elif perf_results['first_token_delay'] < 10:
        print("   ✅ Bon - Premier token acceptable")
    else:
        print("   ⚠️ Lent - Premier token pourrait être amélioré")
    
    print(f"   ⚡ Vitesse: {perf_results['tokens_per_second']:.1f} mots/seconde")
    
    if perf_results['tokens_per_second'] > 10:
        print("   ✅ Excellent - Vitesse de génération rapide")
    elif perf_results['tokens_per_second'] > 5:
        print("   ✅ Bon - Vitesse acceptable")
    else:
        print("   ⚠️ Lent - Vitesse pourrait être améliorée")
    
    print(f"   🔄 Réduction des updates UI: {frontend_results['reduction_percentage']:.1f}%")
    
    if frontend_results['reduction_percentage'] > 70:
        print("   ✅ Excellent - Optimisation UI très efficace")
    elif frontend_results['reduction_percentage'] > 50:
        print("   ✅ Bon - Optimisation UI efficace")
    else:
        print("   ⚠️ Modéré - Optimisation UI pourrait être améliorée")
    
    print(f"\n💡 RECOMMANDATIONS:")
    if perf_results['first_token_delay'] > 10:
        print("   - Vérifier la configuration du modèle LM Studio")
        print("   - Considérer un modèle plus petit pour de meilleures performances")
    
    if frontend_results['reduction_percentage'] < 50:
        print("   - Augmenter l'intervalle de debouncing")
        print("   - Optimiser davantage les re-renders React")
    
    if perf_results['tokens_per_second'] < 5:
        print("   - Vérifier les ressources système (CPU/GPU)")
        print("   - Optimiser les paramètres du modèle")

if __name__ == "__main__":
    main()
