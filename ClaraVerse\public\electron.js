const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');

let mainWindow;
let backendProcess;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../assets/icons/wema-logo.png'),
    title: 'WeMa IA',
    show: false
  });

  const isDev = process.env.NODE_ENV === 'development';
  
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  }

  // Afficher la fenêtre quand elle est prête
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Démarrer le backend Python (optionnel pour test)
  try {
    startBackend();
  } catch (error) {
    console.log('⚠️ Backend Python non démarré:', error.message);
    // L'application peut fonctionner sans backend pour les tests
  }
}

function startBackend() {
  try {
    const backendPath = path.join(__dirname, '../py_backend');

    // Essayer différents chemins Python
    const pythonPaths = [
      'python',
      'python3',
      'C:\\Users\\<USER>\\anaconda3\\python.exe',
      'C:\\Python\\python.exe',
      'C:\\Python39\\python.exe',
      'C:\\Python310\\python.exe',
      'C:\\Python311\\python.exe',
      'C:\\Python312\\python.exe',
      'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe'
    ];

    let pythonCmd = 'python';

    // Vérifier quel Python est disponible
    for (const pyPath of pythonPaths) {
      try {
        const { execSync } = require('child_process');
        execSync(`"${pyPath}" --version`, { stdio: 'ignore' });
        pythonCmd = pyPath;
        console.log(`Python trouvé: ${pyPath}`);
        break;
      } catch (e) {
        // Continuer avec le suivant
      }
    }

    backendProcess = spawn(pythonCmd, ['main.py'], {
      cwd: backendPath,
      stdio: 'pipe'
    });
    
    backendProcess.stdout.on('data', (data) => {
      console.log('Backend:', data.toString());
    });
    
    backendProcess.stderr.on('data', (data) => {
      console.error('Backend Error:', data.toString());
    });

    backendProcess.on('error', (error) => {
      console.error('Erreur backend:', error);

      // Afficher une boîte de dialogue d'erreur plus informative
      const { dialog } = require('electron');
      dialog.showErrorBox(
        'Erreur de démarrage WeMa IA',
        `Impossible de démarrer le backend Python.\n\n` +
        `Erreur: ${error.message}\n\n` +
        `Assurez-vous que Python est installé sur votre système.\n` +
        `Vous pouvez télécharger Python depuis: https://python.org`
      );
    });

  } catch (error) {
    console.error('Erreur démarrage backend:', error);

    // Afficher une boîte de dialogue d'erreur
    const { dialog } = require('electron');
    dialog.showErrorBox(
      'Erreur de démarrage WeMa IA',
      `Impossible de démarrer le backend Python.\n\n` +
      `Erreur: ${error.message}\n\n` +
      `Assurez-vous que Python est installé sur votre système.`
    );
  }
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  // Arrêter le backend
  if (backendProcess && !backendProcess.killed) {
    backendProcess.kill();
  }
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
