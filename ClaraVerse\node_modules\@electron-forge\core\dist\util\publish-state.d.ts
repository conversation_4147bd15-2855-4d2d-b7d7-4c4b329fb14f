import { ForgeMakeResult } from '@electron-forge/shared-types';
export default class PublishState {
    static loadFromDirectory(directory: string, rootDir: string): Promise<PublishState[][]>;
    static saveToDirectory(directory: string, artifacts: ForgeMakeResult[], rootDir: string): Promise<void>;
    private dir;
    private path;
    private hasHash;
    state: ForgeMakeResult;
    constructor(filePath: string, hasHash?: boolean);
    generateHash(): string;
    load(): Promise<void>;
    saveToDisk(): Promise<void>;
}
//# sourceMappingURL=publish-state.d.ts.map