"""
🔍 Internet Search Tools - Backend Python
Outils de recherche internet pour WeMa IA
"""

import asyncio
import logging
from typing import Dict, Any, List
from services.internet_search_service import search_internet, quick_internet_search

logger = logging.getLogger(__name__)

class InternetSearchTools:
    """Outils de recherche internet pour l'agent WeMa IA"""
    
    @staticmethod
    async def internet_search(
        query: str,
        search_type: str = "general",
        max_results: int = 6,
        time_range: str = None,
        model_context: str = "32b",  # Nouveau paramètre pour adapter selon le modèle
        **kwargs
    ) -> Dict[str, Any]:
        """
        🔍 Outil de recherche internet intelligente avec adaptation selon le modèle

        Args:
            query: Requête de recherche
            search_type: Type de recherche (general, news, scientific, technical)
            max_results: Nombre maximum de résultats
            time_range: Période de recherche (day, month, year)
            model_context: Contexte du modèle ("32b" ou "14b") pour adapter les paramètres
        """
        try:
            logger.info(f"🔍 Recherche internet: '{query}' (type: {search_type}, modèle: {model_context})")

            # 🚀 ADAPTATION INTELLIGENTE SELON LE MODÈLE
            if model_context == "14b":
                # Modèle 14B : Plus de contexte disponible (60K tokens)
                max_pages_to_scrape = 5  # Plus de pages
                content_per_page = 2000  # Plus de contenu par page
                max_search_results = min(max_results, 15)  # Plus de résultats
                logger.info("🚀 Mode 14B: Configuration étendue (5 pages, 2000 chars/page)")
            else:
                # Modèle 32B : Contexte limité (10K tokens)
                max_pages_to_scrape = 2  # Moins de pages
                content_per_page = 800   # Contenu réduit
                max_search_results = min(max_results, 10)  # Résultats standards
                logger.info("⚡ Mode 32B: Configuration optimisée (2 pages, 800 chars/page)")

            # Essayer d'abord la vraie recherche
            try:
                response = await search_internet(
                    query=query,
                    search_type=search_type,
                    max_results=max_search_results
                )

                if not response.error and response.results:
                    # Vraie recherche réussie - maintenant scraper le contenu
                    logger.info(f"🌐 Scraping du contenu des {len(response.results)} résultats...")

                    # Extraire les URLs pour le scraping (adapté selon le modèle)
                    urls_to_scrape = [result.url for result in response.results[:max_pages_to_scrape]]

                    try:
                        from services.web_scraper_service import scrape_search_results
                        scraped_contents = await scrape_search_results(urls_to_scrape)

                        logger.info(f"🔍 Scraping résultat: {len(scraped_contents) if scraped_contents else 0} contenus")

                        if scraped_contents:
                            # Formater avec le contenu scrapé
                            result_text = f"🔍 **Recherche internet avec contenu détaillé:** '{query}'\n\n"

                            for i, scraped in enumerate(scraped_contents, 1):
                                result_text += f"**{i}. {scraped.title}**\n"
                                result_text += f"🔗 {scraped.url}\n"

                                if scraped.meta_description:
                                    result_text += f"📝 {scraped.meta_description}\n"

                                if scraped.content:
                                    # 🚀 ADAPTATION INTELLIGENTE DU CONTENU SELON LE MODÈLE
                                    content_preview = scraped.content[:content_per_page]
                                    if len(scraped.content) > content_per_page:
                                        content_preview += "..."
                                    result_text += f"📖 {content_preview}\n"

                                result_text += f"📊 {scraped.word_count} mots • Temps: {scraped.scrape_time:.1f}s\n\n"

                            result_text += f"_✅ Contenu détaillé extrait de {len(scraped_contents)} page(s)_"
                        else:
                            # Si le scraping échoue, retourner les résultats de base sans contenu scrapé
                            logger.warning("🌐 Scraping échoué, utilisation des résultats de base")
                            result_text = f"🔍 **Résultats de recherche pour:** '{query}'\n\n"

                            for i, result in enumerate(response.results[:max_results], 1):
                                result_text += f"**{i}. {result.title}**\n"
                                result_text += f"🔗 {result.url}\n"
                                if result.snippet:
                                    result_text += f"📝 {result.snippet}\n"
                                result_text += "\n"

                            result_text += f"_🔍 {len(response.results)} résultat(s) trouvé(s) via SearXNG_"

                            return {
                                "success": True,
                                "result": result_text,
                                "metadata": {
                                    "query": query,
                                    "search_type": search_type,
                                    "results_count": len(response.results),
                                    "sources": [result.url for result in response.results[:max_results]],
                                    "scraped_content": []  # Pas de contenu scrapé dans cette branche
                                },
                                "tool": "internet_search"
                            }

                    except Exception as scrape_error:
                        logger.error(f"❌ Erreur scraping: {scrape_error}")
                        return {
                            "success": False,
                            "error": f"Erreur lors de l'extraction du contenu: {str(scrape_error)}",
                            "tool": "internet_search"
                        }

                    return {
                        "success": True,
                        "result": result_text,
                        "metadata": {
                            "query": query,
                            "search_type": search_type,
                            "results_count": len(response.results),
                            "sources": [r.url for r in response.results],
                            "scraped_content": [
                                {
                                    "url": scraped.url,
                                    "title": scraped.title,
                                    "content": scraped.content,
                                    "meta_description": scraped.meta_description,
                                    "word_count": scraped.word_count,
                                    "scrape_time": scraped.scrape_time
                                } for scraped in scraped_contents
                            ] if scraped_contents else []
                        },
                        "tool": "internet_search"
                    }
            except Exception as search_error:
                logger.warning(f"⚠️ Vraie recherche échouée, utilisation de résultats d'exemple: {search_error}")

            # PAS DE FALLBACK - Retourner une erreur claire
            return {
                "success": False,
                "error": "Service de recherche internet indisponible. Veuillez vérifier que SearXNG est démarré.",
                "message": f"❌ Impossible de rechercher '{query}' - SearXNG non disponible",
                "tool": "internet_search"
            }

        except Exception as e:
            logger.error(f"❌ Erreur outil recherche internet: {e}")
            return {
                "success": False,
                "error": f"Erreur inattendue: {str(e)}",
                "tool": "internet_search"
            }
    
    @staticmethod
    async def news_search(
        query: str,
        time_range: str = "month",
        max_results: int = 6,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Outil de recherche d'actualités
        
        Args:
            query: Sujet d'actualité à rechercher
            time_range: Période (day, month, year)
            max_results: Nombre maximum d'articles
        """
        try:
            logger.info(f"📰 Recherche actualités: '{query}' (période: {time_range})")
            
            response = await search_internet(
                query=query,
                search_type="news",
                max_results=min(max_results, 8)
            )
            
            if response.error:
                return {
                    "success": False,
                    "error": response.error,
                    "tool": "news_search"
                }
            
            if not response.results:
                result_text = f"📰 Aucune actualité trouvée pour: '{query}'"
            else:
                result_text = f"📰 **Actualités:** '{query}'\n\n"
                
                for i, result in enumerate(response.results, 1):
                    result_text += f"**{i}. {result.title}**\n"
                    if result.published_date:
                        result_text += f"📅 {result.published_date}\n"
                    if result.content:
                        content = result.content[:150] + "..." if len(result.content) > 150 else result.content
                        result_text += f"{content}\n"
                    result_text += f"🔗 {result.url}\n\n"
                
                result_text += f"_Actualités récentes via SearXNG_"
            
            return {
                "success": True,
                "result": result_text,
                "metadata": {
                    "query": query,
                    "search_type": "news",
                    "time_range": time_range,
                    "results_count": len(response.results)
                },
                "tool": "news_search"
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur recherche actualités: {e}")
            return {
                "success": False,
                "error": f"Erreur inattendue: {str(e)}",
                "tool": "news_search"
            }
    
    @staticmethod
    async def technical_search(
        query: str,
        max_results: int = 6,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Outil de recherche technique/informatique
        
        Args:
            query: Question ou problème technique
            max_results: Nombre maximum de résultats
        """
        try:
            logger.info(f"💻 Recherche technique: '{query}'")
            
            response = await search_internet(
                query=query,
                search_type="technical",
                max_results=min(max_results, 8)
            )
            
            if response.error:
                return {
                    "success": False,
                    "error": response.error,
                    "tool": "technical_search"
                }
            
            if not response.results:
                result_text = f"💻 Aucune ressource technique trouvée pour: '{query}'"
            else:
                result_text = f"💻 **Recherche technique:** '{query}'\n\n"
                
                for i, result in enumerate(response.results, 1):
                    result_text += f"**{i}. {result.title}**\n"
                    if result.content:
                        content = result.content[:180] + "..." if len(result.content) > 180 else result.content
                        result_text += f"{content}\n"
                    if result.engine:
                        result_text += f"📊 Source: {result.engine}\n"
                    result_text += f"🔗 {result.url}\n\n"
                
                result_text += f"_Ressources techniques via SearXNG_"
            
            return {
                "success": True,
                "result": result_text,
                "metadata": {
                    "query": query,
                    "search_type": "technical",
                    "results_count": len(response.results)
                },
                "tool": "technical_search"
            }
            
        except Exception as e:
            logger.error(f"❌ Erreur recherche technique: {e}")
            return {
                "success": False,
                "error": f"Erreur inattendue: {str(e)}",
                "tool": "technical_search"
            }

# Dictionnaire des outils disponibles
INTERNET_SEARCH_TOOLS = {
    "internet_search": {
        "function": InternetSearchTools.internet_search,
        "description": "Recherche des informations sur internet via SearXNG. Utilise cet outil quand l'utilisateur demande des informations récentes, des actualités, ou des données que tu ne connais pas.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "La requête de recherche à effectuer"
                },
                "search_type": {
                    "type": "string",
                    "description": "Type de recherche: general, news, scientific, technical",
                    "default": "general"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Nombre maximum de résultats à retourner (1-10)",
                    "default": 6
                },
                "time_range": {
                    "type": "string",
                    "description": "Période de recherche: day, month, year (pour les actualités)",
                    "default": "month"
                }
            },
            "required": ["query"]
        }
    },
    
    "news_search": {
        "function": InternetSearchTools.news_search,
        "description": "Recherche des actualités récentes sur internet. Utilise cet outil pour des informations d'actualité, des événements récents, ou des nouvelles.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Le sujet d'actualité à rechercher"
                },
                "time_range": {
                    "type": "string",
                    "description": "Période: day (aujourd'hui), month (ce mois), year (cette année)",
                    "default": "month"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Nombre maximum d'articles (1-8)",
                    "default": 6
                }
            },
            "required": ["query"]
        }
    },
    
    "technical_search": {
        "function": InternetSearchTools.technical_search,
        "description": "Recherche des informations techniques, de programmation, ou informatiques. Utilise cet outil pour des questions de code, développement, ou techniques.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "La question ou problème technique à rechercher"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Nombre maximum de résultats (1-8)",
                    "default": 6
                }
            },
            "required": ["query"]
        }
    }
}
