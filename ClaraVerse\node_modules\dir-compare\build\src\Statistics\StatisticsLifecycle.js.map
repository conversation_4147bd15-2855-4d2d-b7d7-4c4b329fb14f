{"version": 3, "file": "StatisticsLifecycle.js", "sourceRoot": "", "sources": ["../../../src/Statistics/StatisticsLifecycle.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACU,QAAA,mBAAmB,GAAG;IAC/B,SAAS,CAAC,OAAmB;QACzB,IAAI,iBAAiB,GAAkC,SAAS,CAAA;QAChE,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,iBAAiB,GAAG;gBAChB,gBAAgB,EAAE,CAAC;gBACnB,aAAa,EAAE,CAAC;gBAChB,YAAY,EAAE,CAAC;gBACf,aAAa,EAAE,CAAC;gBAChB,mBAAmB,EAAE,CAAC;gBACtB,aAAa,EAAE,CAAC;aACnB,CAAA;SACJ;QACD,MAAM,qBAAqB,GAA0B;YACjD,eAAe,EAAE,CAAC;YAClB,gBAAgB,EAAE,CAAC;YACnB,mBAAmB,EAAE,CAAC;YACtB,gBAAgB,EAAE,CAAC;SACtB,CAAA;QACD,MAAM,0BAA0B,GAA+B;YAC3D,oBAAoB,EAAE,CAAC;YACvB,qBAAqB,EAAE,CAAC;YACxB,wBAAwB,EAAE,CAAC;YAC3B,qBAAqB,EAAE,CAAC;SAC3B,CAAA;QACD,OAAO;YACH,QAAQ,EAAE,CAAC;YACX,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;YACR,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,CAAC;YACb,SAAS,EAAE,CAAC;YACZ,UAAU,EAAE,CAAC;YACb,YAAY,EAAE,CAAC;YACf,SAAS,EAAE,CAAC;YACZ,QAAQ,EAAE,CAAC;YACX,SAAS,EAAE,CAAC;YACZ,WAAW,EAAE,qBAAqB;YAClC,QAAQ,EAAE,iBAAiB;YAC3B,gBAAgB,EAAE,0BAA0B;SAC/C,CAAA;IACL,CAAC;IAED,kBAAkB,CAAC,iBAAoC,EAAE,OAAmB;QACxE,MAAM,UAAU,GAAe,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAA;QAE5E,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,KAAK,CAAA;QACjF,UAAU,CAAC,gBAAgB,GAAG,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,UAAU,CAAA;QACrG,UAAU,CAAC,eAAe,GAAG,UAAU,CAAC,YAAY,GAAG,UAAU,CAAC,QAAQ,GAAG,UAAU,CAAC,SAAS,CAAA;QACjG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,WAAW,CAAA;QAC5D,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,gBAAgB,CAAA;QAC3E,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,eAAe,CAAA;QACxE,MAAM,gBAAgB,GAAG,UAAU,CAAC,WAAW,CAAA;QAC/C,gBAAgB,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,eAAe,GAAG,gBAAgB,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,mBAAmB,CAAA;QAC/I,MAAM,qBAAqB,GAAG,UAAU,CAAC,gBAAgB,CAAA;QACzD,qBAAqB,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,oBAAoB,GAAG,qBAAqB,CAAC,qBAAqB,GAAG,qBAAqB,CAAC,wBAAwB,CAAA;QACvL,UAAU,CAAC,IAAI,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QAEvD,IAAI,OAAO,CAAC,cAAc,EAAE;YACxB,MAAM,iBAAiB,GAAG,UAAU,CAAC,QAA6B,CAAA;YAClE,iBAAiB,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,gBAAgB;gBACtE,iBAAiB,CAAC,YAAY,GAAG,iBAAiB,CAAC,aAAa,CAAA;YACpE,iBAAiB,CAAC,aAAa,GAAG,iBAAiB,CAAC,mBAAmB,GAAG,iBAAiB,CAAC,aAAa,CAAA;SAC5G;QAED,OAAO,UAAU,CAAA;IACrB,CAAC;CAEJ,CAAA"}