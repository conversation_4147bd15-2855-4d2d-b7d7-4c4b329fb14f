import{_ as S,l as z,c as at,M as Nt,ad as mt,K as tt,i as et,ae as Dt,af as Ot,ag as At,d as It,an as Ct,a6 as Rt}from"./index-BRXgCuaM.js";import{c as pt}from"./cytoscape.esm-BY7v1xhs.js";import{c as ot,a as xt}from"./vendor-BEryHLmj.js";import"./pdfjs-CcP0jMWS.js";var dt={exports:{}},it={exports:{}},nt={exports:{}},ut;function Mt(){return ut||(ut=1,function(I,w){(function(D,y){I.exports=y()})(ot,function(){return function(f){var D={};function y(r){if(D[r])return D[r].exports;var t=D[r]={i:r,l:!1,exports:{}};return f[r].call(t.exports,t,t.exports,y),t.l=!0,t.exports}return y.m=f,y.c=D,y.i=function(r){return r},y.d=function(r,t,e){y.o(r,t)||Object.defineProperty(r,t,{configurable:!1,enumerable:!0,get:e})},y.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return y.d(t,"a",t),t},y.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)},y.p="",y(y.s=26)}([function(f,D,y){function r(){}r.QUALITY=1,r.DEFAULT_CREATE_BENDS_AS_NEEDED=!1,r.DEFAULT_INCREMENTAL=!1,r.DEFAULT_ANIMATION_ON_LAYOUT=!0,r.DEFAULT_ANIMATION_DURING_LAYOUT=!1,r.DEFAULT_ANIMATION_PERIOD=50,r.DEFAULT_UNIFORM_LEAF_NODE_SIZES=!1,r.DEFAULT_GRAPH_MARGIN=15,r.NODE_DIMENSIONS_INCLUDE_LABELS=!1,r.SIMPLE_NODE_SIZE=40,r.SIMPLE_NODE_HALF_SIZE=r.SIMPLE_NODE_SIZE/2,r.EMPTY_COMPOUND_NODE_SIZE=40,r.MIN_EDGE_LENGTH=1,r.WORLD_BOUNDARY=1e6,r.INITIAL_WORLD_BOUNDARY=r.WORLD_BOUNDARY/1e3,r.WORLD_CENTER_X=1200,r.WORLD_CENTER_Y=900,f.exports=r},function(f,D,y){var r=y(2),t=y(8),e=y(9);function i(g,a,v){r.call(this,v),this.isOverlapingSourceAndTarget=!1,this.vGraphObject=v,this.bendpoints=[],this.source=g,this.target=a}i.prototype=Object.create(r.prototype);for(var o in r)i[o]=r[o];i.prototype.getSource=function(){return this.source},i.prototype.getTarget=function(){return this.target},i.prototype.isInterGraph=function(){return this.isInterGraph},i.prototype.getLength=function(){return this.length},i.prototype.isOverlapingSourceAndTarget=function(){return this.isOverlapingSourceAndTarget},i.prototype.getBendpoints=function(){return this.bendpoints},i.prototype.getLca=function(){return this.lca},i.prototype.getSourceInLca=function(){return this.sourceInLca},i.prototype.getTargetInLca=function(){return this.targetInLca},i.prototype.getOtherEnd=function(g){if(this.source===g)return this.target;if(this.target===g)return this.source;throw"Node is not incident with this edge"},i.prototype.getOtherEndInGraph=function(g,a){for(var v=this.getOtherEnd(g),n=a.getGraphManager().getRoot();;){if(v.getOwner()==a)return v;if(v.getOwner()==n)break;v=v.getOwner().getParent()}return null},i.prototype.updateLength=function(){var g=new Array(4);this.isOverlapingSourceAndTarget=t.getIntersection(this.target.getRect(),this.source.getRect(),g),this.isOverlapingSourceAndTarget||(this.lengthX=g[0]-g[2],this.lengthY=g[1]-g[3],Math.abs(this.lengthX)<1&&(this.lengthX=e.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=e.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY))},i.prototype.updateLengthSimple=function(){this.lengthX=this.target.getCenterX()-this.source.getCenterX(),this.lengthY=this.target.getCenterY()-this.source.getCenterY(),Math.abs(this.lengthX)<1&&(this.lengthX=e.sign(this.lengthX)),Math.abs(this.lengthY)<1&&(this.lengthY=e.sign(this.lengthY)),this.length=Math.sqrt(this.lengthX*this.lengthX+this.lengthY*this.lengthY)},f.exports=i},function(f,D,y){function r(t){this.vGraphObject=t}f.exports=r},function(f,D,y){var r=y(2),t=y(10),e=y(13),i=y(0),o=y(16),g=y(4);function a(n,c,l,E){l==null&&E==null&&(E=c),r.call(this,E),n.graphManager!=null&&(n=n.graphManager),this.estimatedSize=t.MIN_VALUE,this.inclusionTreeDepth=t.MAX_VALUE,this.vGraphObject=E,this.edges=[],this.graphManager=n,l!=null&&c!=null?this.rect=new e(c.x,c.y,l.width,l.height):this.rect=new e}a.prototype=Object.create(r.prototype);for(var v in r)a[v]=r[v];a.prototype.getEdges=function(){return this.edges},a.prototype.getChild=function(){return this.child},a.prototype.getOwner=function(){return this.owner},a.prototype.getWidth=function(){return this.rect.width},a.prototype.setWidth=function(n){this.rect.width=n},a.prototype.getHeight=function(){return this.rect.height},a.prototype.setHeight=function(n){this.rect.height=n},a.prototype.getCenterX=function(){return this.rect.x+this.rect.width/2},a.prototype.getCenterY=function(){return this.rect.y+this.rect.height/2},a.prototype.getCenter=function(){return new g(this.rect.x+this.rect.width/2,this.rect.y+this.rect.height/2)},a.prototype.getLocation=function(){return new g(this.rect.x,this.rect.y)},a.prototype.getRect=function(){return this.rect},a.prototype.getDiagonal=function(){return Math.sqrt(this.rect.width*this.rect.width+this.rect.height*this.rect.height)},a.prototype.getHalfTheDiagonal=function(){return Math.sqrt(this.rect.height*this.rect.height+this.rect.width*this.rect.width)/2},a.prototype.setRect=function(n,c){this.rect.x=n.x,this.rect.y=n.y,this.rect.width=c.width,this.rect.height=c.height},a.prototype.setCenter=function(n,c){this.rect.x=n-this.rect.width/2,this.rect.y=c-this.rect.height/2},a.prototype.setLocation=function(n,c){this.rect.x=n,this.rect.y=c},a.prototype.moveBy=function(n,c){this.rect.x+=n,this.rect.y+=c},a.prototype.getEdgeListToNode=function(n){var c=[],l=this;return l.edges.forEach(function(E){if(E.target==n){if(E.source!=l)throw"Incorrect edge source!";c.push(E)}}),c},a.prototype.getEdgesBetween=function(n){var c=[],l=this;return l.edges.forEach(function(E){if(!(E.source==l||E.target==l))throw"Incorrect edge source and/or target";(E.target==n||E.source==n)&&c.push(E)}),c},a.prototype.getNeighborsList=function(){var n=new Set,c=this;return c.edges.forEach(function(l){if(l.source==c)n.add(l.target);else{if(l.target!=c)throw"Incorrect incidency!";n.add(l.source)}}),n},a.prototype.withChildren=function(){var n=new Set,c,l;if(n.add(this),this.child!=null)for(var E=this.child.getNodes(),T=0;T<E.length;T++)c=E[T],l=c.withChildren(),l.forEach(function(m){n.add(m)});return n},a.prototype.getNoOfChildren=function(){var n=0,c;if(this.child==null)n=1;else for(var l=this.child.getNodes(),E=0;E<l.length;E++)c=l[E],n+=c.getNoOfChildren();return n==0&&(n=1),n},a.prototype.getEstimatedSize=function(){if(this.estimatedSize==t.MIN_VALUE)throw"assert failed";return this.estimatedSize},a.prototype.calcEstimatedSize=function(){return this.child==null?this.estimatedSize=(this.rect.width+this.rect.height)/2:(this.estimatedSize=this.child.calcEstimatedSize(),this.rect.width=this.estimatedSize,this.rect.height=this.estimatedSize,this.estimatedSize)},a.prototype.scatter=function(){var n,c,l=-i.INITIAL_WORLD_BOUNDARY,E=i.INITIAL_WORLD_BOUNDARY;n=i.WORLD_CENTER_X+o.nextDouble()*(E-l)+l;var T=-i.INITIAL_WORLD_BOUNDARY,m=i.INITIAL_WORLD_BOUNDARY;c=i.WORLD_CENTER_Y+o.nextDouble()*(m-T)+T,this.rect.x=n,this.rect.y=c},a.prototype.updateBounds=function(){if(this.getChild()==null)throw"assert failed";if(this.getChild().getNodes().length!=0){var n=this.getChild();if(n.updateBounds(!0),this.rect.x=n.getLeft(),this.rect.y=n.getTop(),this.setWidth(n.getRight()-n.getLeft()),this.setHeight(n.getBottom()-n.getTop()),i.NODE_DIMENSIONS_INCLUDE_LABELS){var c=n.getRight()-n.getLeft(),l=n.getBottom()-n.getTop();this.labelWidth>c&&(this.rect.x-=(this.labelWidth-c)/2,this.setWidth(this.labelWidth)),this.labelHeight>l&&(this.labelPos=="center"?this.rect.y-=(this.labelHeight-l)/2:this.labelPos=="top"&&(this.rect.y-=this.labelHeight-l),this.setHeight(this.labelHeight))}}},a.prototype.getInclusionTreeDepth=function(){if(this.inclusionTreeDepth==t.MAX_VALUE)throw"assert failed";return this.inclusionTreeDepth},a.prototype.transform=function(n){var c=this.rect.x;c>i.WORLD_BOUNDARY?c=i.WORLD_BOUNDARY:c<-i.WORLD_BOUNDARY&&(c=-i.WORLD_BOUNDARY);var l=this.rect.y;l>i.WORLD_BOUNDARY?l=i.WORLD_BOUNDARY:l<-i.WORLD_BOUNDARY&&(l=-i.WORLD_BOUNDARY);var E=new g(c,l),T=n.inverseTransformPoint(E);this.setLocation(T.x,T.y)},a.prototype.getLeft=function(){return this.rect.x},a.prototype.getRight=function(){return this.rect.x+this.rect.width},a.prototype.getTop=function(){return this.rect.y},a.prototype.getBottom=function(){return this.rect.y+this.rect.height},a.prototype.getParent=function(){return this.owner==null?null:this.owner.getParent()},f.exports=a},function(f,D,y){function r(t,e){t==null&&e==null?(this.x=0,this.y=0):(this.x=t,this.y=e)}r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.setX=function(t){this.x=t},r.prototype.setY=function(t){this.y=t},r.prototype.getDifference=function(t){return new DimensionD(this.x-t.x,this.y-t.y)},r.prototype.getCopy=function(){return new r(this.x,this.y)},r.prototype.translate=function(t){return this.x+=t.width,this.y+=t.height,this},f.exports=r},function(f,D,y){var r=y(2),t=y(10),e=y(0),i=y(6),o=y(3),g=y(1),a=y(13),v=y(12),n=y(11);function c(E,T,m){r.call(this,m),this.estimatedSize=t.MIN_VALUE,this.margin=e.DEFAULT_GRAPH_MARGIN,this.edges=[],this.nodes=[],this.isConnected=!1,this.parent=E,T!=null&&T instanceof i?this.graphManager=T:T!=null&&T instanceof Layout&&(this.graphManager=T.graphManager)}c.prototype=Object.create(r.prototype);for(var l in r)c[l]=r[l];c.prototype.getNodes=function(){return this.nodes},c.prototype.getEdges=function(){return this.edges},c.prototype.getGraphManager=function(){return this.graphManager},c.prototype.getParent=function(){return this.parent},c.prototype.getLeft=function(){return this.left},c.prototype.getRight=function(){return this.right},c.prototype.getTop=function(){return this.top},c.prototype.getBottom=function(){return this.bottom},c.prototype.isConnected=function(){return this.isConnected},c.prototype.add=function(E,T,m){if(T==null&&m==null){var L=E;if(this.graphManager==null)throw"Graph has no graph mgr!";if(this.getNodes().indexOf(L)>-1)throw"Node already in graph!";return L.owner=this,this.getNodes().push(L),L}else{var O=E;if(!(this.getNodes().indexOf(T)>-1&&this.getNodes().indexOf(m)>-1))throw"Source or target not in graph!";if(!(T.owner==m.owner&&T.owner==this))throw"Both owners must be this graph!";return T.owner!=m.owner?null:(O.source=T,O.target=m,O.isInterGraph=!1,this.getEdges().push(O),T.edges.push(O),m!=T&&m.edges.push(O),O)}},c.prototype.remove=function(E){var T=E;if(E instanceof o){if(T==null)throw"Node is null!";if(!(T.owner!=null&&T.owner==this))throw"Owner graph is invalid!";if(this.graphManager==null)throw"Owner graph manager is invalid!";for(var m=T.edges.slice(),L,O=m.length,d=0;d<O;d++)L=m[d],L.isInterGraph?this.graphManager.remove(L):L.source.owner.remove(L);var N=this.nodes.indexOf(T);if(N==-1)throw"Node not in owner node list!";this.nodes.splice(N,1)}else if(E instanceof g){var L=E;if(L==null)throw"Edge is null!";if(!(L.source!=null&&L.target!=null))throw"Source and/or target is null!";if(!(L.source.owner!=null&&L.target.owner!=null&&L.source.owner==this&&L.target.owner==this))throw"Source and/or target owner is invalid!";var s=L.source.edges.indexOf(L),h=L.target.edges.indexOf(L);if(!(s>-1&&h>-1))throw"Source and/or target doesn't know this edge!";L.source.edges.splice(s,1),L.target!=L.source&&L.target.edges.splice(h,1);var N=L.source.owner.getEdges().indexOf(L);if(N==-1)throw"Not in owner's edge list!";L.source.owner.getEdges().splice(N,1)}},c.prototype.updateLeftTop=function(){for(var E=t.MAX_VALUE,T=t.MAX_VALUE,m,L,O,d=this.getNodes(),N=d.length,s=0;s<N;s++){var h=d[s];m=h.getTop(),L=h.getLeft(),E>m&&(E=m),T>L&&(T=L)}return E==t.MAX_VALUE?null:(d[0].getParent().paddingLeft!=null?O=d[0].getParent().paddingLeft:O=this.margin,this.left=T-O,this.top=E-O,new v(this.left,this.top))},c.prototype.updateBounds=function(E){for(var T=t.MAX_VALUE,m=-t.MAX_VALUE,L=t.MAX_VALUE,O=-t.MAX_VALUE,d,N,s,h,u,p=this.nodes,A=p.length,C=0;C<A;C++){var R=p[C];E&&R.child!=null&&R.updateBounds(),d=R.getLeft(),N=R.getRight(),s=R.getTop(),h=R.getBottom(),T>d&&(T=d),m<N&&(m=N),L>s&&(L=s),O<h&&(O=h)}var x=new a(T,L,m-T,O-L);T==t.MAX_VALUE&&(this.left=this.parent.getLeft(),this.right=this.parent.getRight(),this.top=this.parent.getTop(),this.bottom=this.parent.getBottom()),p[0].getParent().paddingLeft!=null?u=p[0].getParent().paddingLeft:u=this.margin,this.left=x.x-u,this.right=x.x+x.width+u,this.top=x.y-u,this.bottom=x.y+x.height+u},c.calculateBounds=function(E){for(var T=t.MAX_VALUE,m=-t.MAX_VALUE,L=t.MAX_VALUE,O=-t.MAX_VALUE,d,N,s,h,u=E.length,p=0;p<u;p++){var A=E[p];d=A.getLeft(),N=A.getRight(),s=A.getTop(),h=A.getBottom(),T>d&&(T=d),m<N&&(m=N),L>s&&(L=s),O<h&&(O=h)}var C=new a(T,L,m-T,O-L);return C},c.prototype.getInclusionTreeDepth=function(){return this==this.graphManager.getRoot()?1:this.parent.getInclusionTreeDepth()},c.prototype.getEstimatedSize=function(){if(this.estimatedSize==t.MIN_VALUE)throw"assert failed";return this.estimatedSize},c.prototype.calcEstimatedSize=function(){for(var E=0,T=this.nodes,m=T.length,L=0;L<m;L++){var O=T[L];E+=O.calcEstimatedSize()}return E==0?this.estimatedSize=e.EMPTY_COMPOUND_NODE_SIZE:this.estimatedSize=E/Math.sqrt(this.nodes.length),this.estimatedSize},c.prototype.updateConnected=function(){var E=this;if(this.nodes.length==0){this.isConnected=!0;return}var T=new n,m=new Set,L=this.nodes[0],O,d,N=L.withChildren();for(N.forEach(function(C){T.push(C),m.add(C)});T.length!==0;){L=T.shift(),O=L.getEdges();for(var s=O.length,h=0;h<s;h++){var u=O[h];if(d=u.getOtherEndInGraph(L,this),d!=null&&!m.has(d)){var p=d.withChildren();p.forEach(function(C){T.push(C),m.add(C)})}}}if(this.isConnected=!1,m.size>=this.nodes.length){var A=0;m.forEach(function(C){C.owner==E&&A++}),A==this.nodes.length&&(this.isConnected=!0)}},f.exports=c},function(f,D,y){var r,t=y(1);function e(i){r=y(5),this.layout=i,this.graphs=[],this.edges=[]}e.prototype.addRoot=function(){var i=this.layout.newGraph(),o=this.layout.newNode(null),g=this.add(i,o);return this.setRootGraph(g),this.rootGraph},e.prototype.add=function(i,o,g,a,v){if(g==null&&a==null&&v==null){if(i==null)throw"Graph is null!";if(o==null)throw"Parent node is null!";if(this.graphs.indexOf(i)>-1)throw"Graph already in this graph mgr!";if(this.graphs.push(i),i.parent!=null)throw"Already has a parent!";if(o.child!=null)throw"Already has a child!";return i.parent=o,o.child=i,i}else{v=g,a=o,g=i;var n=a.getOwner(),c=v.getOwner();if(!(n!=null&&n.getGraphManager()==this))throw"Source not in this graph mgr!";if(!(c!=null&&c.getGraphManager()==this))throw"Target not in this graph mgr!";if(n==c)return g.isInterGraph=!1,n.add(g,a,v);if(g.isInterGraph=!0,g.source=a,g.target=v,this.edges.indexOf(g)>-1)throw"Edge already in inter-graph edge list!";if(this.edges.push(g),!(g.source!=null&&g.target!=null))throw"Edge source and/or target is null!";if(!(g.source.edges.indexOf(g)==-1&&g.target.edges.indexOf(g)==-1))throw"Edge already in source and/or target incidency list!";return g.source.edges.push(g),g.target.edges.push(g),g}},e.prototype.remove=function(i){if(i instanceof r){var o=i;if(o.getGraphManager()!=this)throw"Graph not in this graph mgr";if(!(o==this.rootGraph||o.parent!=null&&o.parent.graphManager==this))throw"Invalid parent node!";var g=[];g=g.concat(o.getEdges());for(var a,v=g.length,n=0;n<v;n++)a=g[n],o.remove(a);var c=[];c=c.concat(o.getNodes());var l;v=c.length;for(var n=0;n<v;n++)l=c[n],o.remove(l);o==this.rootGraph&&this.setRootGraph(null);var E=this.graphs.indexOf(o);this.graphs.splice(E,1),o.parent=null}else if(i instanceof t){if(a=i,a==null)throw"Edge is null!";if(!a.isInterGraph)throw"Not an inter-graph edge!";if(!(a.source!=null&&a.target!=null))throw"Source and/or target is null!";if(!(a.source.edges.indexOf(a)!=-1&&a.target.edges.indexOf(a)!=-1))throw"Source and/or target doesn't know this edge!";var E=a.source.edges.indexOf(a);if(a.source.edges.splice(E,1),E=a.target.edges.indexOf(a),a.target.edges.splice(E,1),!(a.source.owner!=null&&a.source.owner.getGraphManager()!=null))throw"Edge owner graph or owner graph manager is null!";if(a.source.owner.getGraphManager().edges.indexOf(a)==-1)throw"Not in owner graph manager's edge list!";var E=a.source.owner.getGraphManager().edges.indexOf(a);a.source.owner.getGraphManager().edges.splice(E,1)}},e.prototype.updateBounds=function(){this.rootGraph.updateBounds(!0)},e.prototype.getGraphs=function(){return this.graphs},e.prototype.getAllNodes=function(){if(this.allNodes==null){for(var i=[],o=this.getGraphs(),g=o.length,a=0;a<g;a++)i=i.concat(o[a].getNodes());this.allNodes=i}return this.allNodes},e.prototype.resetAllNodes=function(){this.allNodes=null},e.prototype.resetAllEdges=function(){this.allEdges=null},e.prototype.resetAllNodesToApplyGravitation=function(){this.allNodesToApplyGravitation=null},e.prototype.getAllEdges=function(){if(this.allEdges==null){var i=[],o=this.getGraphs();o.length;for(var g=0;g<o.length;g++)i=i.concat(o[g].getEdges());i=i.concat(this.edges),this.allEdges=i}return this.allEdges},e.prototype.getAllNodesToApplyGravitation=function(){return this.allNodesToApplyGravitation},e.prototype.setAllNodesToApplyGravitation=function(i){if(this.allNodesToApplyGravitation!=null)throw"assert failed";this.allNodesToApplyGravitation=i},e.prototype.getRoot=function(){return this.rootGraph},e.prototype.setRootGraph=function(i){if(i.getGraphManager()!=this)throw"Root not in this graph mgr!";this.rootGraph=i,i.parent==null&&(i.parent=this.layout.newNode("Root node"))},e.prototype.getLayout=function(){return this.layout},e.prototype.isOneAncestorOfOther=function(i,o){if(!(i!=null&&o!=null))throw"assert failed";if(i==o)return!0;var g=i.getOwner(),a;do{if(a=g.getParent(),a==null)break;if(a==o)return!0;if(g=a.getOwner(),g==null)break}while(!0);g=o.getOwner();do{if(a=g.getParent(),a==null)break;if(a==i)return!0;if(g=a.getOwner(),g==null)break}while(!0);return!1},e.prototype.calcLowestCommonAncestors=function(){for(var i,o,g,a,v,n=this.getAllEdges(),c=n.length,l=0;l<c;l++){if(i=n[l],o=i.source,g=i.target,i.lca=null,i.sourceInLca=o,i.targetInLca=g,o==g){i.lca=o.getOwner();continue}for(a=o.getOwner();i.lca==null;){for(i.targetInLca=g,v=g.getOwner();i.lca==null;){if(v==a){i.lca=v;break}if(v==this.rootGraph)break;if(i.lca!=null)throw"assert failed";i.targetInLca=v.getParent(),v=i.targetInLca.getOwner()}if(a==this.rootGraph)break;i.lca==null&&(i.sourceInLca=a.getParent(),a=i.sourceInLca.getOwner())}if(i.lca==null)throw"assert failed"}},e.prototype.calcLowestCommonAncestor=function(i,o){if(i==o)return i.getOwner();var g=i.getOwner();do{if(g==null)break;var a=o.getOwner();do{if(a==null)break;if(a==g)return a;a=a.getParent().getOwner()}while(!0);g=g.getParent().getOwner()}while(!0);return g},e.prototype.calcInclusionTreeDepths=function(i,o){i==null&&o==null&&(i=this.rootGraph,o=1);for(var g,a=i.getNodes(),v=a.length,n=0;n<v;n++)g=a[n],g.inclusionTreeDepth=o,g.child!=null&&this.calcInclusionTreeDepths(g.child,o+1)},e.prototype.includesInvalidEdge=function(){for(var i,o=this.edges.length,g=0;g<o;g++)if(i=this.edges[g],this.isOneAncestorOfOther(i.source,i.target))return!0;return!1},f.exports=e},function(f,D,y){var r=y(0);function t(){}for(var e in r)t[e]=r[e];t.MAX_ITERATIONS=2500,t.DEFAULT_EDGE_LENGTH=50,t.DEFAULT_SPRING_STRENGTH=.45,t.DEFAULT_REPULSION_STRENGTH=4500,t.DEFAULT_GRAVITY_STRENGTH=.4,t.DEFAULT_COMPOUND_GRAVITY_STRENGTH=1,t.DEFAULT_GRAVITY_RANGE_FACTOR=3.8,t.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=1.5,t.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION=!0,t.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION=!0,t.DEFAULT_COOLING_FACTOR_INCREMENTAL=.3,t.COOLING_ADAPTATION_FACTOR=.33,t.ADAPTATION_LOWER_NODE_LIMIT=1e3,t.ADAPTATION_UPPER_NODE_LIMIT=5e3,t.MAX_NODE_DISPLACEMENT_INCREMENTAL=100,t.MAX_NODE_DISPLACEMENT=t.MAX_NODE_DISPLACEMENT_INCREMENTAL*3,t.MIN_REPULSION_DIST=t.DEFAULT_EDGE_LENGTH/10,t.CONVERGENCE_CHECK_PERIOD=100,t.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=.1,t.MIN_EDGE_LENGTH=1,t.GRID_CALCULATION_CHECK_PERIOD=10,f.exports=t},function(f,D,y){var r=y(12);function t(){}t.calcSeparationAmount=function(e,i,o,g){if(!e.intersects(i))throw"assert failed";var a=new Array(2);this.decideDirectionsForOverlappingNodes(e,i,a),o[0]=Math.min(e.getRight(),i.getRight())-Math.max(e.x,i.x),o[1]=Math.min(e.getBottom(),i.getBottom())-Math.max(e.y,i.y),e.getX()<=i.getX()&&e.getRight()>=i.getRight()?o[0]+=Math.min(i.getX()-e.getX(),e.getRight()-i.getRight()):i.getX()<=e.getX()&&i.getRight()>=e.getRight()&&(o[0]+=Math.min(e.getX()-i.getX(),i.getRight()-e.getRight())),e.getY()<=i.getY()&&e.getBottom()>=i.getBottom()?o[1]+=Math.min(i.getY()-e.getY(),e.getBottom()-i.getBottom()):i.getY()<=e.getY()&&i.getBottom()>=e.getBottom()&&(o[1]+=Math.min(e.getY()-i.getY(),i.getBottom()-e.getBottom()));var v=Math.abs((i.getCenterY()-e.getCenterY())/(i.getCenterX()-e.getCenterX()));i.getCenterY()===e.getCenterY()&&i.getCenterX()===e.getCenterX()&&(v=1);var n=v*o[0],c=o[1]/v;o[0]<c?c=o[0]:n=o[1],o[0]=-1*a[0]*(c/2+g),o[1]=-1*a[1]*(n/2+g)},t.decideDirectionsForOverlappingNodes=function(e,i,o){e.getCenterX()<i.getCenterX()?o[0]=-1:o[0]=1,e.getCenterY()<i.getCenterY()?o[1]=-1:o[1]=1},t.getIntersection2=function(e,i,o){var g=e.getCenterX(),a=e.getCenterY(),v=i.getCenterX(),n=i.getCenterY();if(e.intersects(i))return o[0]=g,o[1]=a,o[2]=v,o[3]=n,!0;var c=e.getX(),l=e.getY(),E=e.getRight(),T=e.getX(),m=e.getBottom(),L=e.getRight(),O=e.getWidthHalf(),d=e.getHeightHalf(),N=i.getX(),s=i.getY(),h=i.getRight(),u=i.getX(),p=i.getBottom(),A=i.getRight(),C=i.getWidthHalf(),R=i.getHeightHalf(),x=!1,_=!1;if(g===v){if(a>n)return o[0]=g,o[1]=l,o[2]=v,o[3]=p,!1;if(a<n)return o[0]=g,o[1]=m,o[2]=v,o[3]=s,!1}else if(a===n){if(g>v)return o[0]=c,o[1]=a,o[2]=h,o[3]=n,!1;if(g<v)return o[0]=E,o[1]=a,o[2]=N,o[3]=n,!1}else{var U=e.height/e.width,X=i.height/i.width,M=(n-a)/(v-g),G=void 0,F=void 0,b=void 0,Y=void 0,k=void 0,H=void 0;if(-U===M?g>v?(o[0]=T,o[1]=m,x=!0):(o[0]=E,o[1]=l,x=!0):U===M&&(g>v?(o[0]=c,o[1]=l,x=!0):(o[0]=L,o[1]=m,x=!0)),-X===M?v>g?(o[2]=u,o[3]=p,_=!0):(o[2]=h,o[3]=s,_=!0):X===M&&(v>g?(o[2]=N,o[3]=s,_=!0):(o[2]=A,o[3]=p,_=!0)),x&&_)return!1;if(g>v?a>n?(G=this.getCardinalDirection(U,M,4),F=this.getCardinalDirection(X,M,2)):(G=this.getCardinalDirection(-U,M,3),F=this.getCardinalDirection(-X,M,1)):a>n?(G=this.getCardinalDirection(-U,M,1),F=this.getCardinalDirection(-X,M,3)):(G=this.getCardinalDirection(U,M,2),F=this.getCardinalDirection(X,M,4)),!x)switch(G){case 1:Y=l,b=g+-d/M,o[0]=b,o[1]=Y;break;case 2:b=L,Y=a+O*M,o[0]=b,o[1]=Y;break;case 3:Y=m,b=g+d/M,o[0]=b,o[1]=Y;break;case 4:b=T,Y=a+-O*M,o[0]=b,o[1]=Y;break}if(!_)switch(F){case 1:H=s,k=v+-R/M,o[2]=k,o[3]=H;break;case 2:k=A,H=n+C*M,o[2]=k,o[3]=H;break;case 3:H=p,k=v+R/M,o[2]=k,o[3]=H;break;case 4:k=u,H=n+-C*M,o[2]=k,o[3]=H;break}}return!1},t.getCardinalDirection=function(e,i,o){return e>i?o:1+o%4},t.getIntersection=function(e,i,o,g){if(g==null)return this.getIntersection2(e,i,o);var a=e.x,v=e.y,n=i.x,c=i.y,l=o.x,E=o.y,T=g.x,m=g.y,L=void 0,O=void 0,d=void 0,N=void 0,s=void 0,h=void 0,u=void 0,p=void 0,A=void 0;return d=c-v,s=a-n,u=n*v-a*c,N=m-E,h=l-T,p=T*E-l*m,A=d*h-N*s,A===0?null:(L=(s*p-h*u)/A,O=(N*u-d*p)/A,new r(L,O))},t.angleOfVector=function(e,i,o,g){var a=void 0;return e!==o?(a=Math.atan((g-i)/(o-e)),o<e?a+=Math.PI:g<i&&(a+=this.TWO_PI)):g<i?a=this.ONE_AND_HALF_PI:a=this.HALF_PI,a},t.doIntersect=function(e,i,o,g){var a=e.x,v=e.y,n=i.x,c=i.y,l=o.x,E=o.y,T=g.x,m=g.y,L=(n-a)*(m-E)-(T-l)*(c-v);if(L===0)return!1;var O=((m-E)*(T-a)+(l-T)*(m-v))/L,d=((v-c)*(T-a)+(n-a)*(m-v))/L;return 0<O&&O<1&&0<d&&d<1},t.HALF_PI=.5*Math.PI,t.ONE_AND_HALF_PI=1.5*Math.PI,t.TWO_PI=2*Math.PI,t.THREE_PI=3*Math.PI,f.exports=t},function(f,D,y){function r(){}r.sign=function(t){return t>0?1:t<0?-1:0},r.floor=function(t){return t<0?Math.ceil(t):Math.floor(t)},r.ceil=function(t){return t<0?Math.floor(t):Math.ceil(t)},f.exports=r},function(f,D,y){function r(){}r.MAX_VALUE=2147483647,r.MIN_VALUE=-2147483648,f.exports=r},function(f,D,y){var r=function(){function a(v,n){for(var c=0;c<n.length;c++){var l=n[c];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(v,l.key,l)}}return function(v,n,c){return n&&a(v.prototype,n),c&&a(v,c),v}}();function t(a,v){if(!(a instanceof v))throw new TypeError("Cannot call a class as a function")}var e=function(v){return{value:v,next:null,prev:null}},i=function(v,n,c,l){return v!==null?v.next=n:l.head=n,c!==null?c.prev=n:l.tail=n,n.prev=v,n.next=c,l.length++,n},o=function(v,n){var c=v.prev,l=v.next;return c!==null?c.next=l:n.head=l,l!==null?l.prev=c:n.tail=c,v.prev=v.next=null,n.length--,v},g=function(){function a(v){var n=this;t(this,a),this.length=0,this.head=null,this.tail=null,v!=null&&v.forEach(function(c){return n.push(c)})}return r(a,[{key:"size",value:function(){return this.length}},{key:"insertBefore",value:function(n,c){return i(c.prev,e(n),c,this)}},{key:"insertAfter",value:function(n,c){return i(c,e(n),c.next,this)}},{key:"insertNodeBefore",value:function(n,c){return i(c.prev,n,c,this)}},{key:"insertNodeAfter",value:function(n,c){return i(c,n,c.next,this)}},{key:"push",value:function(n){return i(this.tail,e(n),null,this)}},{key:"unshift",value:function(n){return i(null,e(n),this.head,this)}},{key:"remove",value:function(n){return o(n,this)}},{key:"pop",value:function(){return o(this.tail,this).value}},{key:"popNode",value:function(){return o(this.tail,this)}},{key:"shift",value:function(){return o(this.head,this).value}},{key:"shiftNode",value:function(){return o(this.head,this)}},{key:"get_object_at",value:function(n){if(n<=this.length()){for(var c=1,l=this.head;c<n;)l=l.next,c++;return l.value}}},{key:"set_object_at",value:function(n,c){if(n<=this.length()){for(var l=1,E=this.head;l<n;)E=E.next,l++;E.value=c}}}]),a}();f.exports=g},function(f,D,y){function r(t,e,i){this.x=null,this.y=null,t==null&&e==null&&i==null?(this.x=0,this.y=0):typeof t=="number"&&typeof e=="number"&&i==null?(this.x=t,this.y=e):t.constructor.name=="Point"&&e==null&&i==null&&(i=t,this.x=i.x,this.y=i.y)}r.prototype.getX=function(){return this.x},r.prototype.getY=function(){return this.y},r.prototype.getLocation=function(){return new r(this.x,this.y)},r.prototype.setLocation=function(t,e,i){t.constructor.name=="Point"&&e==null&&i==null?(i=t,this.setLocation(i.x,i.y)):typeof t=="number"&&typeof e=="number"&&i==null&&(parseInt(t)==t&&parseInt(e)==e?this.move(t,e):(this.x=Math.floor(t+.5),this.y=Math.floor(e+.5)))},r.prototype.move=function(t,e){this.x=t,this.y=e},r.prototype.translate=function(t,e){this.x+=t,this.y+=e},r.prototype.equals=function(t){if(t.constructor.name=="Point"){var e=t;return this.x==e.x&&this.y==e.y}return this==t},r.prototype.toString=function(){return new r().constructor.name+"[x="+this.x+",y="+this.y+"]"},f.exports=r},function(f,D,y){function r(t,e,i,o){this.x=0,this.y=0,this.width=0,this.height=0,t!=null&&e!=null&&i!=null&&o!=null&&(this.x=t,this.y=e,this.width=i,this.height=o)}r.prototype.getX=function(){return this.x},r.prototype.setX=function(t){this.x=t},r.prototype.getY=function(){return this.y},r.prototype.setY=function(t){this.y=t},r.prototype.getWidth=function(){return this.width},r.prototype.setWidth=function(t){this.width=t},r.prototype.getHeight=function(){return this.height},r.prototype.setHeight=function(t){this.height=t},r.prototype.getRight=function(){return this.x+this.width},r.prototype.getBottom=function(){return this.y+this.height},r.prototype.intersects=function(t){return!(this.getRight()<t.x||this.getBottom()<t.y||t.getRight()<this.x||t.getBottom()<this.y)},r.prototype.getCenterX=function(){return this.x+this.width/2},r.prototype.getMinX=function(){return this.getX()},r.prototype.getMaxX=function(){return this.getX()+this.width},r.prototype.getCenterY=function(){return this.y+this.height/2},r.prototype.getMinY=function(){return this.getY()},r.prototype.getMaxY=function(){return this.getY()+this.height},r.prototype.getWidthHalf=function(){return this.width/2},r.prototype.getHeightHalf=function(){return this.height/2},f.exports=r},function(f,D,y){var r=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function t(){}t.lastID=0,t.createID=function(e){return t.isPrimitive(e)?e:(e.uniqueID!=null||(e.uniqueID=t.getString(),t.lastID++),e.uniqueID)},t.getString=function(e){return e==null&&(e=t.lastID),"Object#"+e},t.isPrimitive=function(e){var i=typeof e>"u"?"undefined":r(e);return e==null||i!="object"&&i!="function"},f.exports=t},function(f,D,y){function r(l){if(Array.isArray(l)){for(var E=0,T=Array(l.length);E<l.length;E++)T[E]=l[E];return T}else return Array.from(l)}var t=y(0),e=y(6),i=y(3),o=y(1),g=y(5),a=y(4),v=y(17),n=y(27);function c(l){n.call(this),this.layoutQuality=t.QUALITY,this.createBendsAsNeeded=t.DEFAULT_CREATE_BENDS_AS_NEEDED,this.incremental=t.DEFAULT_INCREMENTAL,this.animationOnLayout=t.DEFAULT_ANIMATION_ON_LAYOUT,this.animationDuringLayout=t.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=t.DEFAULT_ANIMATION_PERIOD,this.uniformLeafNodeSizes=t.DEFAULT_UNIFORM_LEAF_NODE_SIZES,this.edgeToDummyNodes=new Map,this.graphManager=new e(this),this.isLayoutFinished=!1,this.isSubLayout=!1,this.isRemoteUse=!1,l!=null&&(this.isRemoteUse=l)}c.RANDOM_SEED=1,c.prototype=Object.create(n.prototype),c.prototype.getGraphManager=function(){return this.graphManager},c.prototype.getAllNodes=function(){return this.graphManager.getAllNodes()},c.prototype.getAllEdges=function(){return this.graphManager.getAllEdges()},c.prototype.getAllNodesToApplyGravitation=function(){return this.graphManager.getAllNodesToApplyGravitation()},c.prototype.newGraphManager=function(){var l=new e(this);return this.graphManager=l,l},c.prototype.newGraph=function(l){return new g(null,this.graphManager,l)},c.prototype.newNode=function(l){return new i(this.graphManager,l)},c.prototype.newEdge=function(l){return new o(null,null,l)},c.prototype.checkLayoutSuccess=function(){return this.graphManager.getRoot()==null||this.graphManager.getRoot().getNodes().length==0||this.graphManager.includesInvalidEdge()},c.prototype.runLayout=function(){this.isLayoutFinished=!1,this.tilingPreLayout&&this.tilingPreLayout(),this.initParameters();var l;return this.checkLayoutSuccess()?l=!1:l=this.layout(),t.ANIMATE==="during"?!1:(l&&(this.isSubLayout||this.doPostLayout()),this.tilingPostLayout&&this.tilingPostLayout(),this.isLayoutFinished=!0,l)},c.prototype.doPostLayout=function(){this.incremental||this.transform(),this.update()},c.prototype.update2=function(){if(this.createBendsAsNeeded&&(this.createBendpointsFromDummyNodes(),this.graphManager.resetAllEdges()),!this.isRemoteUse){for(var l=this.graphManager.getAllEdges(),E=0;E<l.length;E++)l[E];for(var T=this.graphManager.getRoot().getNodes(),E=0;E<T.length;E++)T[E];this.update(this.graphManager.getRoot())}},c.prototype.update=function(l){if(l==null)this.update2();else if(l instanceof i){var E=l;if(E.getChild()!=null)for(var T=E.getChild().getNodes(),m=0;m<T.length;m++)update(T[m]);if(E.vGraphObject!=null){var L=E.vGraphObject;L.update(E)}}else if(l instanceof o){var O=l;if(O.vGraphObject!=null){var d=O.vGraphObject;d.update(O)}}else if(l instanceof g){var N=l;if(N.vGraphObject!=null){var s=N.vGraphObject;s.update(N)}}},c.prototype.initParameters=function(){this.isSubLayout||(this.layoutQuality=t.QUALITY,this.animationDuringLayout=t.DEFAULT_ANIMATION_DURING_LAYOUT,this.animationPeriod=t.DEFAULT_ANIMATION_PERIOD,this.animationOnLayout=t.DEFAULT_ANIMATION_ON_LAYOUT,this.incremental=t.DEFAULT_INCREMENTAL,this.createBendsAsNeeded=t.DEFAULT_CREATE_BENDS_AS_NEEDED,this.uniformLeafNodeSizes=t.DEFAULT_UNIFORM_LEAF_NODE_SIZES),this.animationDuringLayout&&(this.animationOnLayout=!1)},c.prototype.transform=function(l){if(l==null)this.transform(new a(0,0));else{var E=new v,T=this.graphManager.getRoot().updateLeftTop();if(T!=null){E.setWorldOrgX(l.x),E.setWorldOrgY(l.y),E.setDeviceOrgX(T.x),E.setDeviceOrgY(T.y);for(var m=this.getAllNodes(),L,O=0;O<m.length;O++)L=m[O],L.transform(E)}}},c.prototype.positionNodesRandomly=function(l){if(l==null)this.positionNodesRandomly(this.getGraphManager().getRoot()),this.getGraphManager().getRoot().updateBounds(!0);else for(var E,T,m=l.getNodes(),L=0;L<m.length;L++)E=m[L],T=E.getChild(),T==null||T.getNodes().length==0?E.scatter():(this.positionNodesRandomly(T),E.updateBounds())},c.prototype.getFlatForest=function(){for(var l=[],E=!0,T=this.graphManager.getRoot().getNodes(),m=!0,L=0;L<T.length;L++)T[L].getChild()!=null&&(m=!1);if(!m)return l;var O=new Set,d=[],N=new Map,s=[];for(s=s.concat(T);s.length>0&&E;){for(d.push(s[0]);d.length>0&&E;){var h=d[0];d.splice(0,1),O.add(h);for(var u=h.getEdges(),L=0;L<u.length;L++){var p=u[L].getOtherEnd(h);if(N.get(h)!=p)if(!O.has(p))d.push(p),N.set(p,h);else{E=!1;break}}}if(!E)l=[];else{var A=[].concat(r(O));l.push(A);for(var L=0;L<A.length;L++){var C=A[L],R=s.indexOf(C);R>-1&&s.splice(R,1)}O=new Set,N=new Map}}return l},c.prototype.createDummyNodesForBendpoints=function(l){for(var E=[],T=l.source,m=this.graphManager.calcLowestCommonAncestor(l.source,l.target),L=0;L<l.bendpoints.length;L++){var O=this.newNode(null);O.setRect(new Point(0,0),new Dimension(1,1)),m.add(O);var d=this.newEdge(null);this.graphManager.add(d,T,O),E.add(O),T=O}var d=this.newEdge(null);return this.graphManager.add(d,T,l.target),this.edgeToDummyNodes.set(l,E),l.isInterGraph()?this.graphManager.remove(l):m.remove(l),E},c.prototype.createBendpointsFromDummyNodes=function(){var l=[];l=l.concat(this.graphManager.getAllEdges()),l=[].concat(r(this.edgeToDummyNodes.keys())).concat(l);for(var E=0;E<l.length;E++){var T=l[E];if(T.bendpoints.length>0){for(var m=this.edgeToDummyNodes.get(T),L=0;L<m.length;L++){var O=m[L],d=new a(O.getCenterX(),O.getCenterY()),N=T.bendpoints.get(L);N.x=d.x,N.y=d.y,O.getOwner().remove(O)}this.graphManager.add(T,T.source,T.target)}}},c.transform=function(l,E,T,m){if(T!=null&&m!=null){var L=E;if(l<=50){var O=E/T;L-=(E-O)/50*(50-l)}else{var d=E*m;L+=(d-E)/50*(l-50)}return L}else{var N,s;return l<=50?(N=9*E/500,s=E/10):(N=9*E/50,s=-8*E),N*l+s}},c.findCenterOfTree=function(l){var E=[];E=E.concat(l);var T=[],m=new Map,L=!1,O=null;(E.length==1||E.length==2)&&(L=!0,O=E[0]);for(var d=0;d<E.length;d++){var N=E[d],s=N.getNeighborsList().size;m.set(N,N.getNeighborsList().size),s==1&&T.push(N)}var h=[];for(h=h.concat(T);!L;){var u=[];u=u.concat(h),h=[];for(var d=0;d<E.length;d++){var N=E[d],p=E.indexOf(N);p>=0&&E.splice(p,1);var A=N.getNeighborsList();A.forEach(function(x){if(T.indexOf(x)<0){var _=m.get(x),U=_-1;U==1&&h.push(x),m.set(x,U)}})}T=T.concat(h),(E.length==1||E.length==2)&&(L=!0,O=E[0])}return O},c.prototype.setGraphManager=function(l){this.graphManager=l},f.exports=c},function(f,D,y){function r(){}r.seed=1,r.x=0,r.nextDouble=function(){return r.x=Math.sin(r.seed++)*1e4,r.x-Math.floor(r.x)},f.exports=r},function(f,D,y){var r=y(4);function t(e,i){this.lworldOrgX=0,this.lworldOrgY=0,this.ldeviceOrgX=0,this.ldeviceOrgY=0,this.lworldExtX=1,this.lworldExtY=1,this.ldeviceExtX=1,this.ldeviceExtY=1}t.prototype.getWorldOrgX=function(){return this.lworldOrgX},t.prototype.setWorldOrgX=function(e){this.lworldOrgX=e},t.prototype.getWorldOrgY=function(){return this.lworldOrgY},t.prototype.setWorldOrgY=function(e){this.lworldOrgY=e},t.prototype.getWorldExtX=function(){return this.lworldExtX},t.prototype.setWorldExtX=function(e){this.lworldExtX=e},t.prototype.getWorldExtY=function(){return this.lworldExtY},t.prototype.setWorldExtY=function(e){this.lworldExtY=e},t.prototype.getDeviceOrgX=function(){return this.ldeviceOrgX},t.prototype.setDeviceOrgX=function(e){this.ldeviceOrgX=e},t.prototype.getDeviceOrgY=function(){return this.ldeviceOrgY},t.prototype.setDeviceOrgY=function(e){this.ldeviceOrgY=e},t.prototype.getDeviceExtX=function(){return this.ldeviceExtX},t.prototype.setDeviceExtX=function(e){this.ldeviceExtX=e},t.prototype.getDeviceExtY=function(){return this.ldeviceExtY},t.prototype.setDeviceExtY=function(e){this.ldeviceExtY=e},t.prototype.transformX=function(e){var i=0,o=this.lworldExtX;return o!=0&&(i=this.ldeviceOrgX+(e-this.lworldOrgX)*this.ldeviceExtX/o),i},t.prototype.transformY=function(e){var i=0,o=this.lworldExtY;return o!=0&&(i=this.ldeviceOrgY+(e-this.lworldOrgY)*this.ldeviceExtY/o),i},t.prototype.inverseTransformX=function(e){var i=0,o=this.ldeviceExtX;return o!=0&&(i=this.lworldOrgX+(e-this.ldeviceOrgX)*this.lworldExtX/o),i},t.prototype.inverseTransformY=function(e){var i=0,o=this.ldeviceExtY;return o!=0&&(i=this.lworldOrgY+(e-this.ldeviceOrgY)*this.lworldExtY/o),i},t.prototype.inverseTransformPoint=function(e){var i=new r(this.inverseTransformX(e.x),this.inverseTransformY(e.y));return i},f.exports=t},function(f,D,y){function r(n){if(Array.isArray(n)){for(var c=0,l=Array(n.length);c<n.length;c++)l[c]=n[c];return l}else return Array.from(n)}var t=y(15),e=y(7),i=y(0),o=y(8),g=y(9);function a(){t.call(this),this.useSmartIdealEdgeLengthCalculation=e.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.idealEdgeLength=e.DEFAULT_EDGE_LENGTH,this.springConstant=e.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=e.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=e.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=e.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=e.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=e.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.displacementThresholdPerNode=3*e.DEFAULT_EDGE_LENGTH/100,this.coolingFactor=e.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.initialCoolingFactor=e.DEFAULT_COOLING_FACTOR_INCREMENTAL,this.totalDisplacement=0,this.oldTotalDisplacement=0,this.maxIterations=e.MAX_ITERATIONS}a.prototype=Object.create(t.prototype);for(var v in t)a[v]=t[v];a.prototype.initParameters=function(){t.prototype.initParameters.call(this,arguments),this.totalIterations=0,this.notAnimatedIterations=0,this.useFRGridVariant=e.DEFAULT_USE_SMART_REPULSION_RANGE_CALCULATION,this.grid=[]},a.prototype.calcIdealEdgeLengths=function(){for(var n,c,l,E,T,m,L=this.getGraphManager().getAllEdges(),O=0;O<L.length;O++)n=L[O],n.idealLength=this.idealEdgeLength,n.isInterGraph&&(l=n.getSource(),E=n.getTarget(),T=n.getSourceInLca().getEstimatedSize(),m=n.getTargetInLca().getEstimatedSize(),this.useSmartIdealEdgeLengthCalculation&&(n.idealLength+=T+m-2*i.SIMPLE_NODE_SIZE),c=n.getLca().getInclusionTreeDepth(),n.idealLength+=e.DEFAULT_EDGE_LENGTH*e.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR*(l.getInclusionTreeDepth()+E.getInclusionTreeDepth()-2*c))},a.prototype.initSpringEmbedder=function(){var n=this.getAllNodes().length;this.incremental?(n>e.ADAPTATION_LOWER_NODE_LIMIT&&(this.coolingFactor=Math.max(this.coolingFactor*e.COOLING_ADAPTATION_FACTOR,this.coolingFactor-(n-e.ADAPTATION_LOWER_NODE_LIMIT)/(e.ADAPTATION_UPPER_NODE_LIMIT-e.ADAPTATION_LOWER_NODE_LIMIT)*this.coolingFactor*(1-e.COOLING_ADAPTATION_FACTOR))),this.maxNodeDisplacement=e.MAX_NODE_DISPLACEMENT_INCREMENTAL):(n>e.ADAPTATION_LOWER_NODE_LIMIT?this.coolingFactor=Math.max(e.COOLING_ADAPTATION_FACTOR,1-(n-e.ADAPTATION_LOWER_NODE_LIMIT)/(e.ADAPTATION_UPPER_NODE_LIMIT-e.ADAPTATION_LOWER_NODE_LIMIT)*(1-e.COOLING_ADAPTATION_FACTOR)):this.coolingFactor=1,this.initialCoolingFactor=this.coolingFactor,this.maxNodeDisplacement=e.MAX_NODE_DISPLACEMENT),this.maxIterations=Math.max(this.getAllNodes().length*5,this.maxIterations),this.totalDisplacementThreshold=this.displacementThresholdPerNode*this.getAllNodes().length,this.repulsionRange=this.calcRepulsionRange()},a.prototype.calcSpringForces=function(){for(var n=this.getAllEdges(),c,l=0;l<n.length;l++)c=n[l],this.calcSpringForce(c,c.idealLength)},a.prototype.calcRepulsionForces=function(){var n=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l,E,T,m,L=this.getAllNodes(),O;if(this.useFRGridVariant)for(this.totalIterations%e.GRID_CALCULATION_CHECK_PERIOD==1&&n&&this.updateGrid(),O=new Set,l=0;l<L.length;l++)T=L[l],this.calculateRepulsionForceOfANode(T,O,n,c),O.add(T);else for(l=0;l<L.length;l++)for(T=L[l],E=l+1;E<L.length;E++)m=L[E],T.getOwner()==m.getOwner()&&this.calcRepulsionForce(T,m)},a.prototype.calcGravitationalForces=function(){for(var n,c=this.getAllNodesToApplyGravitation(),l=0;l<c.length;l++)n=c[l],this.calcGravitationalForce(n)},a.prototype.moveNodes=function(){for(var n=this.getAllNodes(),c,l=0;l<n.length;l++)c=n[l],c.move()},a.prototype.calcSpringForce=function(n,c){var l=n.getSource(),E=n.getTarget(),T,m,L,O;if(this.uniformLeafNodeSizes&&l.getChild()==null&&E.getChild()==null)n.updateLengthSimple();else if(n.updateLength(),n.isOverlapingSourceAndTarget)return;T=n.getLength(),T!=0&&(m=this.springConstant*(T-c),L=m*(n.lengthX/T),O=m*(n.lengthY/T),l.springForceX+=L,l.springForceY+=O,E.springForceX-=L,E.springForceY-=O)},a.prototype.calcRepulsionForce=function(n,c){var l=n.getRect(),E=c.getRect(),T=new Array(2),m=new Array(4),L,O,d,N,s,h,u;if(l.intersects(E)){o.calcSeparationAmount(l,E,T,e.DEFAULT_EDGE_LENGTH/2),h=2*T[0],u=2*T[1];var p=n.noOfChildren*c.noOfChildren/(n.noOfChildren+c.noOfChildren);n.repulsionForceX-=p*h,n.repulsionForceY-=p*u,c.repulsionForceX+=p*h,c.repulsionForceY+=p*u}else this.uniformLeafNodeSizes&&n.getChild()==null&&c.getChild()==null?(L=E.getCenterX()-l.getCenterX(),O=E.getCenterY()-l.getCenterY()):(o.getIntersection(l,E,m),L=m[2]-m[0],O=m[3]-m[1]),Math.abs(L)<e.MIN_REPULSION_DIST&&(L=g.sign(L)*e.MIN_REPULSION_DIST),Math.abs(O)<e.MIN_REPULSION_DIST&&(O=g.sign(O)*e.MIN_REPULSION_DIST),d=L*L+O*O,N=Math.sqrt(d),s=this.repulsionConstant*n.noOfChildren*c.noOfChildren/d,h=s*L/N,u=s*O/N,n.repulsionForceX-=h,n.repulsionForceY-=u,c.repulsionForceX+=h,c.repulsionForceY+=u},a.prototype.calcGravitationalForce=function(n){var c,l,E,T,m,L,O,d;c=n.getOwner(),l=(c.getRight()+c.getLeft())/2,E=(c.getTop()+c.getBottom())/2,T=n.getCenterX()-l,m=n.getCenterY()-E,L=Math.abs(T)+n.getWidth()/2,O=Math.abs(m)+n.getHeight()/2,n.getOwner()==this.graphManager.getRoot()?(d=c.getEstimatedSize()*this.gravityRangeFactor,(L>d||O>d)&&(n.gravitationForceX=-this.gravityConstant*T,n.gravitationForceY=-this.gravityConstant*m)):(d=c.getEstimatedSize()*this.compoundGravityRangeFactor,(L>d||O>d)&&(n.gravitationForceX=-this.gravityConstant*T*this.compoundGravityConstant,n.gravitationForceY=-this.gravityConstant*m*this.compoundGravityConstant))},a.prototype.isConverged=function(){var n,c=!1;return this.totalIterations>this.maxIterations/3&&(c=Math.abs(this.totalDisplacement-this.oldTotalDisplacement)<2),n=this.totalDisplacement<this.totalDisplacementThreshold,this.oldTotalDisplacement=this.totalDisplacement,n||c},a.prototype.animate=function(){this.animationDuringLayout&&!this.isSubLayout&&(this.notAnimatedIterations==this.animationPeriod?(this.update(),this.notAnimatedIterations=0):this.notAnimatedIterations++)},a.prototype.calcNoOfChildrenForAllNodes=function(){for(var n,c=this.graphManager.getAllNodes(),l=0;l<c.length;l++)n=c[l],n.noOfChildren=n.getNoOfChildren()},a.prototype.calcGrid=function(n){var c=0,l=0;c=parseInt(Math.ceil((n.getRight()-n.getLeft())/this.repulsionRange)),l=parseInt(Math.ceil((n.getBottom()-n.getTop())/this.repulsionRange));for(var E=new Array(c),T=0;T<c;T++)E[T]=new Array(l);for(var T=0;T<c;T++)for(var m=0;m<l;m++)E[T][m]=new Array;return E},a.prototype.addNodeToGrid=function(n,c,l){var E=0,T=0,m=0,L=0;E=parseInt(Math.floor((n.getRect().x-c)/this.repulsionRange)),T=parseInt(Math.floor((n.getRect().width+n.getRect().x-c)/this.repulsionRange)),m=parseInt(Math.floor((n.getRect().y-l)/this.repulsionRange)),L=parseInt(Math.floor((n.getRect().height+n.getRect().y-l)/this.repulsionRange));for(var O=E;O<=T;O++)for(var d=m;d<=L;d++)this.grid[O][d].push(n),n.setGridCoordinates(E,T,m,L)},a.prototype.updateGrid=function(){var n,c,l=this.getAllNodes();for(this.grid=this.calcGrid(this.graphManager.getRoot()),n=0;n<l.length;n++)c=l[n],this.addNodeToGrid(c,this.graphManager.getRoot().getLeft(),this.graphManager.getRoot().getTop())},a.prototype.calculateRepulsionForceOfANode=function(n,c,l,E){if(this.totalIterations%e.GRID_CALCULATION_CHECK_PERIOD==1&&l||E){var T=new Set;n.surrounding=new Array;for(var m,L=this.grid,O=n.startX-1;O<n.finishX+2;O++)for(var d=n.startY-1;d<n.finishY+2;d++)if(!(O<0||d<0||O>=L.length||d>=L[0].length)){for(var N=0;N<L[O][d].length;N++)if(m=L[O][d][N],!(n.getOwner()!=m.getOwner()||n==m)&&!c.has(m)&&!T.has(m)){var s=Math.abs(n.getCenterX()-m.getCenterX())-(n.getWidth()/2+m.getWidth()/2),h=Math.abs(n.getCenterY()-m.getCenterY())-(n.getHeight()/2+m.getHeight()/2);s<=this.repulsionRange&&h<=this.repulsionRange&&T.add(m)}}n.surrounding=[].concat(r(T))}for(O=0;O<n.surrounding.length;O++)this.calcRepulsionForce(n,n.surrounding[O])},a.prototype.calcRepulsionRange=function(){return 0},f.exports=a},function(f,D,y){var r=y(1),t=y(7);function e(o,g,a){r.call(this,o,g,a),this.idealLength=t.DEFAULT_EDGE_LENGTH}e.prototype=Object.create(r.prototype);for(var i in r)e[i]=r[i];f.exports=e},function(f,D,y){var r=y(3);function t(i,o,g,a){r.call(this,i,o,g,a),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0,this.startX=0,this.finishX=0,this.startY=0,this.finishY=0,this.surrounding=[]}t.prototype=Object.create(r.prototype);for(var e in r)t[e]=r[e];t.prototype.setGridCoordinates=function(i,o,g,a){this.startX=i,this.finishX=o,this.startY=g,this.finishY=a},f.exports=t},function(f,D,y){function r(t,e){this.width=0,this.height=0,t!==null&&e!==null&&(this.height=e,this.width=t)}r.prototype.getWidth=function(){return this.width},r.prototype.setWidth=function(t){this.width=t},r.prototype.getHeight=function(){return this.height},r.prototype.setHeight=function(t){this.height=t},f.exports=r},function(f,D,y){var r=y(14);function t(){this.map={},this.keys=[]}t.prototype.put=function(e,i){var o=r.createID(e);this.contains(o)||(this.map[o]=i,this.keys.push(e))},t.prototype.contains=function(e){return r.createID(e),this.map[e]!=null},t.prototype.get=function(e){var i=r.createID(e);return this.map[i]},t.prototype.keySet=function(){return this.keys},f.exports=t},function(f,D,y){var r=y(14);function t(){this.set={}}t.prototype.add=function(e){var i=r.createID(e);this.contains(i)||(this.set[i]=e)},t.prototype.remove=function(e){delete this.set[r.createID(e)]},t.prototype.clear=function(){this.set={}},t.prototype.contains=function(e){return this.set[r.createID(e)]==e},t.prototype.isEmpty=function(){return this.size()===0},t.prototype.size=function(){return Object.keys(this.set).length},t.prototype.addAllTo=function(e){for(var i=Object.keys(this.set),o=i.length,g=0;g<o;g++)e.push(this.set[i[g]])},t.prototype.size=function(){return Object.keys(this.set).length},t.prototype.addAll=function(e){for(var i=e.length,o=0;o<i;o++){var g=e[o];this.add(g)}},f.exports=t},function(f,D,y){var r=function(){function o(g,a){for(var v=0;v<a.length;v++){var n=a[v];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(g,n.key,n)}}return function(g,a,v){return a&&o(g.prototype,a),v&&o(g,v),g}}();function t(o,g){if(!(o instanceof g))throw new TypeError("Cannot call a class as a function")}var e=y(11),i=function(){function o(g,a){t(this,o),(a!==null||a!==void 0)&&(this.compareFunction=this._defaultCompareFunction);var v=void 0;g instanceof e?v=g.size():v=g.length,this._quicksort(g,0,v-1)}return r(o,[{key:"_quicksort",value:function(a,v,n){if(v<n){var c=this._partition(a,v,n);this._quicksort(a,v,c),this._quicksort(a,c+1,n)}}},{key:"_partition",value:function(a,v,n){for(var c=this._get(a,v),l=v,E=n;;){for(;this.compareFunction(c,this._get(a,E));)E--;for(;this.compareFunction(this._get(a,l),c);)l++;if(l<E)this._swap(a,l,E),l++,E--;else return E}}},{key:"_get",value:function(a,v){return a instanceof e?a.get_object_at(v):a[v]}},{key:"_set",value:function(a,v,n){a instanceof e?a.set_object_at(v,n):a[v]=n}},{key:"_swap",value:function(a,v,n){var c=this._get(a,v);this._set(a,v,this._get(a,n)),this._set(a,n,c)}},{key:"_defaultCompareFunction",value:function(a,v){return v>a}}]),o}();f.exports=i},function(f,D,y){var r=function(){function i(o,g){for(var a=0;a<g.length;a++){var v=g[a];v.enumerable=v.enumerable||!1,v.configurable=!0,"value"in v&&(v.writable=!0),Object.defineProperty(o,v.key,v)}}return function(o,g,a){return g&&i(o.prototype,g),a&&i(o,a),o}}();function t(i,o){if(!(i instanceof o))throw new TypeError("Cannot call a class as a function")}var e=function(){function i(o,g){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1,v=arguments.length>3&&arguments[3]!==void 0?arguments[3]:-1,n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:-1;t(this,i),this.sequence1=o,this.sequence2=g,this.match_score=a,this.mismatch_penalty=v,this.gap_penalty=n,this.iMax=o.length+1,this.jMax=g.length+1,this.grid=new Array(this.iMax);for(var c=0;c<this.iMax;c++){this.grid[c]=new Array(this.jMax);for(var l=0;l<this.jMax;l++)this.grid[c][l]=0}this.tracebackGrid=new Array(this.iMax);for(var E=0;E<this.iMax;E++){this.tracebackGrid[E]=new Array(this.jMax);for(var T=0;T<this.jMax;T++)this.tracebackGrid[E][T]=[null,null,null]}this.alignments=[],this.score=-1,this.computeGrids()}return r(i,[{key:"getScore",value:function(){return this.score}},{key:"getAlignments",value:function(){return this.alignments}},{key:"computeGrids",value:function(){for(var g=1;g<this.jMax;g++)this.grid[0][g]=this.grid[0][g-1]+this.gap_penalty,this.tracebackGrid[0][g]=[!1,!1,!0];for(var a=1;a<this.iMax;a++)this.grid[a][0]=this.grid[a-1][0]+this.gap_penalty,this.tracebackGrid[a][0]=[!1,!0,!1];for(var v=1;v<this.iMax;v++)for(var n=1;n<this.jMax;n++){var c=void 0;this.sequence1[v-1]===this.sequence2[n-1]?c=this.grid[v-1][n-1]+this.match_score:c=this.grid[v-1][n-1]+this.mismatch_penalty;var l=this.grid[v-1][n]+this.gap_penalty,E=this.grid[v][n-1]+this.gap_penalty,T=[c,l,E],m=this.arrayAllMaxIndexes(T);this.grid[v][n]=T[m[0]],this.tracebackGrid[v][n]=[m.includes(0),m.includes(1),m.includes(2)]}this.score=this.grid[this.iMax-1][this.jMax-1]}},{key:"alignmentTraceback",value:function(){var g=[];for(g.push({pos:[this.sequence1.length,this.sequence2.length],seq1:"",seq2:""});g[0];){var a=g[0],v=this.tracebackGrid[a.pos[0]][a.pos[1]];v[0]&&g.push({pos:[a.pos[0]-1,a.pos[1]-1],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),v[1]&&g.push({pos:[a.pos[0]-1,a.pos[1]],seq1:this.sequence1[a.pos[0]-1]+a.seq1,seq2:"-"+a.seq2}),v[2]&&g.push({pos:[a.pos[0],a.pos[1]-1],seq1:"-"+a.seq1,seq2:this.sequence2[a.pos[1]-1]+a.seq2}),a.pos[0]===0&&a.pos[1]===0&&this.alignments.push({sequence1:a.seq1,sequence2:a.seq2}),g.shift()}return this.alignments}},{key:"getAllIndexes",value:function(g,a){for(var v=[],n=-1;(n=g.indexOf(a,n+1))!==-1;)v.push(n);return v}},{key:"arrayAllMaxIndexes",value:function(g){return this.getAllIndexes(g,Math.max.apply(null,g))}}]),i}();f.exports=e},function(f,D,y){var r=function(){};r.FDLayout=y(18),r.FDLayoutConstants=y(7),r.FDLayoutEdge=y(19),r.FDLayoutNode=y(20),r.DimensionD=y(21),r.HashMap=y(22),r.HashSet=y(23),r.IGeometry=y(8),r.IMath=y(9),r.Integer=y(10),r.Point=y(12),r.PointD=y(4),r.RandomSeed=y(16),r.RectangleD=y(13),r.Transform=y(17),r.UniqueIDGeneretor=y(14),r.Quicksort=y(24),r.LinkedList=y(11),r.LGraphObject=y(2),r.LGraph=y(5),r.LEdge=y(1),r.LGraphManager=y(6),r.LNode=y(3),r.Layout=y(15),r.LayoutConstants=y(0),r.NeedlemanWunsch=y(25),f.exports=r},function(f,D,y){function r(){this.listeners=[]}var t=r.prototype;t.addListener=function(e,i){this.listeners.push({event:e,callback:i})},t.removeListener=function(e,i){for(var o=this.listeners.length;o>=0;o--){var g=this.listeners[o];g.event===e&&g.callback===i&&this.listeners.splice(o,1)}},t.emit=function(e,i){for(var o=0;o<this.listeners.length;o++){var g=this.listeners[o];e===g.event&&g.callback(i)}},f.exports=r}])})}(nt)),nt.exports}var ft;function wt(){return ft||(ft=1,function(I,w){(function(D,y){I.exports=y(Mt())})(ot,function(f){return function(D){var y={};function r(t){if(y[t])return y[t].exports;var e=y[t]={i:t,l:!1,exports:{}};return D[t].call(e.exports,e,e.exports,r),e.l=!0,e.exports}return r.m=D,r.c=y,r.i=function(t){return t},r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:i})},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=7)}([function(D,y){D.exports=f},function(D,y,r){var t=r(0).FDLayoutConstants;function e(){}for(var i in t)e[i]=t[i];e.DEFAULT_USE_MULTI_LEVEL_SCALING=!1,e.DEFAULT_RADIAL_SEPARATION=t.DEFAULT_EDGE_LENGTH,e.DEFAULT_COMPONENT_SEPERATION=60,e.TILE=!0,e.TILING_PADDING_VERTICAL=10,e.TILING_PADDING_HORIZONTAL=10,e.TREE_REDUCTION_ON_INCREMENTAL=!1,D.exports=e},function(D,y,r){var t=r(0).FDLayoutEdge;function e(o,g,a){t.call(this,o,g,a)}e.prototype=Object.create(t.prototype);for(var i in t)e[i]=t[i];D.exports=e},function(D,y,r){var t=r(0).LGraph;function e(o,g,a){t.call(this,o,g,a)}e.prototype=Object.create(t.prototype);for(var i in t)e[i]=t[i];D.exports=e},function(D,y,r){var t=r(0).LGraphManager;function e(o){t.call(this,o)}e.prototype=Object.create(t.prototype);for(var i in t)e[i]=t[i];D.exports=e},function(D,y,r){var t=r(0).FDLayoutNode,e=r(0).IMath;function i(g,a,v,n){t.call(this,g,a,v,n)}i.prototype=Object.create(t.prototype);for(var o in t)i[o]=t[o];i.prototype.move=function(){var g=this.graphManager.getLayout();this.displacementX=g.coolingFactor*(this.springForceX+this.repulsionForceX+this.gravitationForceX)/this.noOfChildren,this.displacementY=g.coolingFactor*(this.springForceY+this.repulsionForceY+this.gravitationForceY)/this.noOfChildren,Math.abs(this.displacementX)>g.coolingFactor*g.maxNodeDisplacement&&(this.displacementX=g.coolingFactor*g.maxNodeDisplacement*e.sign(this.displacementX)),Math.abs(this.displacementY)>g.coolingFactor*g.maxNodeDisplacement&&(this.displacementY=g.coolingFactor*g.maxNodeDisplacement*e.sign(this.displacementY)),this.child==null?this.moveBy(this.displacementX,this.displacementY):this.child.getNodes().length==0?this.moveBy(this.displacementX,this.displacementY):this.propogateDisplacementToChildren(this.displacementX,this.displacementY),g.totalDisplacement+=Math.abs(this.displacementX)+Math.abs(this.displacementY),this.springForceX=0,this.springForceY=0,this.repulsionForceX=0,this.repulsionForceY=0,this.gravitationForceX=0,this.gravitationForceY=0,this.displacementX=0,this.displacementY=0},i.prototype.propogateDisplacementToChildren=function(g,a){for(var v=this.getChild().getNodes(),n,c=0;c<v.length;c++)n=v[c],n.getChild()==null?(n.moveBy(g,a),n.displacementX+=g,n.displacementY+=a):n.propogateDisplacementToChildren(g,a)},i.prototype.setPred1=function(g){this.pred1=g},i.prototype.getPred1=function(){return pred1},i.prototype.getPred2=function(){return pred2},i.prototype.setNext=function(g){this.next=g},i.prototype.getNext=function(){return next},i.prototype.setProcessed=function(g){this.processed=g},i.prototype.isProcessed=function(){return processed},D.exports=i},function(D,y,r){var t=r(0).FDLayout,e=r(4),i=r(3),o=r(5),g=r(2),a=r(1),v=r(0).FDLayoutConstants,n=r(0).LayoutConstants,c=r(0).Point,l=r(0).PointD,E=r(0).Layout,T=r(0).Integer,m=r(0).IGeometry,L=r(0).LGraph,O=r(0).Transform;function d(){t.call(this),this.toBeTiled={}}d.prototype=Object.create(t.prototype);for(var N in t)d[N]=t[N];d.prototype.newGraphManager=function(){var s=new e(this);return this.graphManager=s,s},d.prototype.newGraph=function(s){return new i(null,this.graphManager,s)},d.prototype.newNode=function(s){return new o(this.graphManager,s)},d.prototype.newEdge=function(s){return new g(null,null,s)},d.prototype.initParameters=function(){t.prototype.initParameters.call(this,arguments),this.isSubLayout||(a.DEFAULT_EDGE_LENGTH<10?this.idealEdgeLength=10:this.idealEdgeLength=a.DEFAULT_EDGE_LENGTH,this.useSmartIdealEdgeLengthCalculation=a.DEFAULT_USE_SMART_IDEAL_EDGE_LENGTH_CALCULATION,this.springConstant=v.DEFAULT_SPRING_STRENGTH,this.repulsionConstant=v.DEFAULT_REPULSION_STRENGTH,this.gravityConstant=v.DEFAULT_GRAVITY_STRENGTH,this.compoundGravityConstant=v.DEFAULT_COMPOUND_GRAVITY_STRENGTH,this.gravityRangeFactor=v.DEFAULT_GRAVITY_RANGE_FACTOR,this.compoundGravityRangeFactor=v.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR,this.prunedNodesAll=[],this.growTreeIterations=0,this.afterGrowthIterations=0,this.isTreeGrowing=!1,this.isGrowthFinished=!1,this.coolingCycle=0,this.maxCoolingCycle=this.maxIterations/v.CONVERGENCE_CHECK_PERIOD,this.finalTemperature=v.CONVERGENCE_CHECK_PERIOD/this.maxIterations,this.coolingAdjuster=1)},d.prototype.layout=function(){var s=n.DEFAULT_CREATE_BENDS_AS_NEEDED;return s&&(this.createBendpoints(),this.graphManager.resetAllEdges()),this.level=0,this.classicLayout()},d.prototype.classicLayout=function(){if(this.nodesWithGravity=this.calculateNodesToApplyGravitationTo(),this.graphManager.setAllNodesToApplyGravitation(this.nodesWithGravity),this.calcNoOfChildrenForAllNodes(),this.graphManager.calcLowestCommonAncestors(),this.graphManager.calcInclusionTreeDepths(),this.graphManager.getRoot().calcEstimatedSize(),this.calcIdealEdgeLengths(),this.incremental){if(a.TREE_REDUCTION_ON_INCREMENTAL){this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var h=new Set(this.getAllNodes()),u=this.nodesWithGravity.filter(function(C){return h.has(C)});this.graphManager.setAllNodesToApplyGravitation(u)}}else{var s=this.getFlatForest();if(s.length>0)this.positionNodesRadially(s);else{this.reduceTrees(),this.graphManager.resetAllNodesToApplyGravitation();var h=new Set(this.getAllNodes()),u=this.nodesWithGravity.filter(function(p){return h.has(p)});this.graphManager.setAllNodesToApplyGravitation(u),this.positionNodesRandomly()}}return this.initSpringEmbedder(),this.runSpringEmbedder(),!0},d.prototype.tick=function(){if(this.totalIterations++,this.totalIterations===this.maxIterations&&!this.isTreeGrowing&&!this.isGrowthFinished)if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;if(this.totalIterations%v.CONVERGENCE_CHECK_PERIOD==0&&!this.isTreeGrowing&&!this.isGrowthFinished){if(this.isConverged())if(this.prunedNodesAll.length>0)this.isTreeGrowing=!0;else return!0;this.coolingCycle++,this.layoutQuality==0?this.coolingAdjuster=this.coolingCycle:this.layoutQuality==1&&(this.coolingAdjuster=this.coolingCycle/3),this.coolingFactor=Math.max(this.initialCoolingFactor-Math.pow(this.coolingCycle,Math.log(100*(this.initialCoolingFactor-this.finalTemperature))/Math.log(this.maxCoolingCycle))/100*this.coolingAdjuster,this.finalTemperature),this.animationPeriod=Math.ceil(this.initialAnimationPeriod*Math.sqrt(this.coolingFactor))}if(this.isTreeGrowing){if(this.growTreeIterations%10==0)if(this.prunedNodesAll.length>0){this.graphManager.updateBounds(),this.updateGrid(),this.growTree(this.prunedNodesAll),this.graphManager.resetAllNodesToApplyGravitation();var s=new Set(this.getAllNodes()),h=this.nodesWithGravity.filter(function(A){return s.has(A)});this.graphManager.setAllNodesToApplyGravitation(h),this.graphManager.updateBounds(),this.updateGrid(),this.coolingFactor=v.DEFAULT_COOLING_FACTOR_INCREMENTAL}else this.isTreeGrowing=!1,this.isGrowthFinished=!0;this.growTreeIterations++}if(this.isGrowthFinished){if(this.isConverged())return!0;this.afterGrowthIterations%10==0&&(this.graphManager.updateBounds(),this.updateGrid()),this.coolingFactor=v.DEFAULT_COOLING_FACTOR_INCREMENTAL*((100-this.afterGrowthIterations)/100),this.afterGrowthIterations++}var u=!this.isTreeGrowing&&!this.isGrowthFinished,p=this.growTreeIterations%10==1&&this.isTreeGrowing||this.afterGrowthIterations%10==1&&this.isGrowthFinished;return this.totalDisplacement=0,this.graphManager.updateBounds(),this.calcSpringForces(),this.calcRepulsionForces(u,p),this.calcGravitationalForces(),this.moveNodes(),this.animate(),!1},d.prototype.getPositionsData=function(){for(var s=this.graphManager.getAllNodes(),h={},u=0;u<s.length;u++){var p=s[u].rect,A=s[u].id;h[A]={id:A,x:p.getCenterX(),y:p.getCenterY(),w:p.width,h:p.height}}return h},d.prototype.runSpringEmbedder=function(){this.initialAnimationPeriod=25,this.animationPeriod=this.initialAnimationPeriod;var s=!1;if(v.ANIMATE==="during")this.emit("layoutstarted");else{for(;!s;)s=this.tick();this.graphManager.updateBounds()}},d.prototype.calculateNodesToApplyGravitationTo=function(){var s=[],h,u=this.graphManager.getGraphs(),p=u.length,A;for(A=0;A<p;A++)h=u[A],h.updateConnected(),h.isConnected||(s=s.concat(h.getNodes()));return s},d.prototype.createBendpoints=function(){var s=[];s=s.concat(this.graphManager.getAllEdges());var h=new Set,u;for(u=0;u<s.length;u++){var p=s[u];if(!h.has(p)){var A=p.getSource(),C=p.getTarget();if(A==C)p.getBendpoints().push(new l),p.getBendpoints().push(new l),this.createDummyNodesForBendpoints(p),h.add(p);else{var R=[];if(R=R.concat(A.getEdgeListToNode(C)),R=R.concat(C.getEdgeListToNode(A)),!h.has(R[0])){if(R.length>1){var x;for(x=0;x<R.length;x++){var _=R[x];_.getBendpoints().push(new l),this.createDummyNodesForBendpoints(_)}}R.forEach(function(U){h.add(U)})}}}if(h.size==s.length)break}},d.prototype.positionNodesRadially=function(s){for(var h=new c(0,0),u=Math.ceil(Math.sqrt(s.length)),p=0,A=0,C=0,R=new l(0,0),x=0;x<s.length;x++){x%u==0&&(C=0,A=p,x!=0&&(A+=a.DEFAULT_COMPONENT_SEPERATION),p=0);var _=s[x],U=E.findCenterOfTree(_);h.x=C,h.y=A,R=d.radialLayout(_,U,h),R.y>p&&(p=Math.floor(R.y)),C=Math.floor(R.x+a.DEFAULT_COMPONENT_SEPERATION)}this.transform(new l(n.WORLD_CENTER_X-R.x/2,n.WORLD_CENTER_Y-R.y/2))},d.radialLayout=function(s,h,u){var p=Math.max(this.maxDiagonalInTree(s),a.DEFAULT_RADIAL_SEPARATION);d.branchRadialLayout(h,null,0,359,0,p);var A=L.calculateBounds(s),C=new O;C.setDeviceOrgX(A.getMinX()),C.setDeviceOrgY(A.getMinY()),C.setWorldOrgX(u.x),C.setWorldOrgY(u.y);for(var R=0;R<s.length;R++){var x=s[R];x.transform(C)}var _=new l(A.getMaxX(),A.getMaxY());return C.inverseTransformPoint(_)},d.branchRadialLayout=function(s,h,u,p,A,C){var R=(p-u+1)/2;R<0&&(R+=180);var x=(R+u)%360,_=x*m.TWO_PI/360,U=A*Math.cos(_),X=A*Math.sin(_);s.setCenter(U,X);var M=[];M=M.concat(s.getEdges());var G=M.length;h!=null&&G--;for(var F=0,b=M.length,Y,k=s.getEdgesBetween(h);k.length>1;){var H=k[0];k.splice(0,1);var P=M.indexOf(H);P>=0&&M.splice(P,1),b--,G--}h!=null?Y=(M.indexOf(k[0])+1)%b:Y=0;for(var B=Math.abs(p-u)/G,$=Y;F!=G;$=++$%b){var K=M[$].getOtherEnd(s);if(K!=h){var Q=(u+F*B)%360,J=(Q+B)%360;d.branchRadialLayout(K,s,Q,J,A+C,C),F++}}},d.maxDiagonalInTree=function(s){for(var h=T.MIN_VALUE,u=0;u<s.length;u++){var p=s[u],A=p.getDiagonal();A>h&&(h=A)}return h},d.prototype.calcRepulsionRange=function(){return 2*(this.level+1)*this.idealEdgeLength},d.prototype.groupZeroDegreeMembers=function(){var s=this,h={};this.memberGroups={},this.idToDummyNode={};for(var u=[],p=this.graphManager.getAllNodes(),A=0;A<p.length;A++){var C=p[A],R=C.getParent();this.getNodeDegreeWithChildren(C)===0&&(R.id==null||!this.getToBeTiled(R))&&u.push(C)}for(var A=0;A<u.length;A++){var C=u[A],x=C.getParent().id;typeof h[x]>"u"&&(h[x]=[]),h[x]=h[x].concat(C)}Object.keys(h).forEach(function(_){if(h[_].length>1){var U="DummyCompound_"+_;s.memberGroups[U]=h[_];var X=h[_][0].getParent(),M=new o(s.graphManager);M.id=U,M.paddingLeft=X.paddingLeft||0,M.paddingRight=X.paddingRight||0,M.paddingBottom=X.paddingBottom||0,M.paddingTop=X.paddingTop||0,s.idToDummyNode[U]=M;var G=s.getGraphManager().add(s.newGraph(),M),F=X.getChild();F.add(M);for(var b=0;b<h[_].length;b++){var Y=h[_][b];F.remove(Y),G.add(Y)}}})},d.prototype.clearCompounds=function(){var s={},h={};this.performDFSOnCompounds();for(var u=0;u<this.compoundOrder.length;u++)h[this.compoundOrder[u].id]=this.compoundOrder[u],s[this.compoundOrder[u].id]=[].concat(this.compoundOrder[u].getChild().getNodes()),this.graphManager.remove(this.compoundOrder[u].getChild()),this.compoundOrder[u].child=null;this.graphManager.resetAllNodes(),this.tileCompoundMembers(s,h)},d.prototype.clearZeroDegreeMembers=function(){var s=this,h=this.tiledZeroDegreePack=[];Object.keys(this.memberGroups).forEach(function(u){var p=s.idToDummyNode[u];h[u]=s.tileNodes(s.memberGroups[u],p.paddingLeft+p.paddingRight),p.rect.width=h[u].width,p.rect.height=h[u].height})},d.prototype.repopulateCompounds=function(){for(var s=this.compoundOrder.length-1;s>=0;s--){var h=this.compoundOrder[s],u=h.id,p=h.paddingLeft,A=h.paddingTop;this.adjustLocations(this.tiledMemberPack[u],h.rect.x,h.rect.y,p,A)}},d.prototype.repopulateZeroDegreeMembers=function(){var s=this,h=this.tiledZeroDegreePack;Object.keys(h).forEach(function(u){var p=s.idToDummyNode[u],A=p.paddingLeft,C=p.paddingTop;s.adjustLocations(h[u],p.rect.x,p.rect.y,A,C)})},d.prototype.getToBeTiled=function(s){var h=s.id;if(this.toBeTiled[h]!=null)return this.toBeTiled[h];var u=s.getChild();if(u==null)return this.toBeTiled[h]=!1,!1;for(var p=u.getNodes(),A=0;A<p.length;A++){var C=p[A];if(this.getNodeDegree(C)>0)return this.toBeTiled[h]=!1,!1;if(C.getChild()==null){this.toBeTiled[C.id]=!1;continue}if(!this.getToBeTiled(C))return this.toBeTiled[h]=!1,!1}return this.toBeTiled[h]=!0,!0},d.prototype.getNodeDegree=function(s){s.id;for(var h=s.getEdges(),u=0,p=0;p<h.length;p++){var A=h[p];A.getSource().id!==A.getTarget().id&&(u=u+1)}return u},d.prototype.getNodeDegreeWithChildren=function(s){var h=this.getNodeDegree(s);if(s.getChild()==null)return h;for(var u=s.getChild().getNodes(),p=0;p<u.length;p++){var A=u[p];h+=this.getNodeDegreeWithChildren(A)}return h},d.prototype.performDFSOnCompounds=function(){this.compoundOrder=[],this.fillCompexOrderByDFS(this.graphManager.getRoot().getNodes())},d.prototype.fillCompexOrderByDFS=function(s){for(var h=0;h<s.length;h++){var u=s[h];u.getChild()!=null&&this.fillCompexOrderByDFS(u.getChild().getNodes()),this.getToBeTiled(u)&&this.compoundOrder.push(u)}},d.prototype.adjustLocations=function(s,h,u,p,A){h+=p,u+=A;for(var C=h,R=0;R<s.rows.length;R++){var x=s.rows[R];h=C;for(var _=0,U=0;U<x.length;U++){var X=x[U];X.rect.x=h,X.rect.y=u,h+=X.rect.width+s.horizontalPadding,X.rect.height>_&&(_=X.rect.height)}u+=_+s.verticalPadding}},d.prototype.tileCompoundMembers=function(s,h){var u=this;this.tiledMemberPack=[],Object.keys(s).forEach(function(p){var A=h[p];u.tiledMemberPack[p]=u.tileNodes(s[p],A.paddingLeft+A.paddingRight),A.rect.width=u.tiledMemberPack[p].width,A.rect.height=u.tiledMemberPack[p].height})},d.prototype.tileNodes=function(s,h){var u=a.TILING_PADDING_VERTICAL,p=a.TILING_PADDING_HORIZONTAL,A={rows:[],rowWidth:[],rowHeight:[],width:0,height:h,verticalPadding:u,horizontalPadding:p};s.sort(function(x,_){return x.rect.width*x.rect.height>_.rect.width*_.rect.height?-1:x.rect.width*x.rect.height<_.rect.width*_.rect.height?1:0});for(var C=0;C<s.length;C++){var R=s[C];A.rows.length==0?this.insertNodeToRow(A,R,0,h):this.canAddHorizontal(A,R.rect.width,R.rect.height)?this.insertNodeToRow(A,R,this.getShortestRowIndex(A),h):this.insertNodeToRow(A,R,A.rows.length,h),this.shiftToLastRow(A)}return A},d.prototype.insertNodeToRow=function(s,h,u,p){var A=p;if(u==s.rows.length){var C=[];s.rows.push(C),s.rowWidth.push(A),s.rowHeight.push(0)}var R=s.rowWidth[u]+h.rect.width;s.rows[u].length>0&&(R+=s.horizontalPadding),s.rowWidth[u]=R,s.width<R&&(s.width=R);var x=h.rect.height;u>0&&(x+=s.verticalPadding);var _=0;x>s.rowHeight[u]&&(_=s.rowHeight[u],s.rowHeight[u]=x,_=s.rowHeight[u]-_),s.height+=_,s.rows[u].push(h)},d.prototype.getShortestRowIndex=function(s){for(var h=-1,u=Number.MAX_VALUE,p=0;p<s.rows.length;p++)s.rowWidth[p]<u&&(h=p,u=s.rowWidth[p]);return h},d.prototype.getLongestRowIndex=function(s){for(var h=-1,u=Number.MIN_VALUE,p=0;p<s.rows.length;p++)s.rowWidth[p]>u&&(h=p,u=s.rowWidth[p]);return h},d.prototype.canAddHorizontal=function(s,h,u){var p=this.getShortestRowIndex(s);if(p<0)return!0;var A=s.rowWidth[p];if(A+s.horizontalPadding+h<=s.width)return!0;var C=0;s.rowHeight[p]<u&&p>0&&(C=u+s.verticalPadding-s.rowHeight[p]);var R;s.width-A>=h+s.horizontalPadding?R=(s.height+C)/(A+h+s.horizontalPadding):R=(s.height+C)/s.width,C=u+s.verticalPadding;var x;return s.width<h?x=(s.height+C)/h:x=(s.height+C)/s.width,x<1&&(x=1/x),R<1&&(R=1/R),R<x},d.prototype.shiftToLastRow=function(s){var h=this.getLongestRowIndex(s),u=s.rowWidth.length-1,p=s.rows[h],A=p[p.length-1],C=A.width+s.horizontalPadding;if(s.width-s.rowWidth[u]>C&&h!=u){p.splice(-1,1),s.rows[u].push(A),s.rowWidth[h]=s.rowWidth[h]-C,s.rowWidth[u]=s.rowWidth[u]+C,s.width=s.rowWidth[instance.getLongestRowIndex(s)];for(var R=Number.MIN_VALUE,x=0;x<p.length;x++)p[x].height>R&&(R=p[x].height);h>0&&(R+=s.verticalPadding);var _=s.rowHeight[h]+s.rowHeight[u];s.rowHeight[h]=R,s.rowHeight[u]<A.height+s.verticalPadding&&(s.rowHeight[u]=A.height+s.verticalPadding);var U=s.rowHeight[h]+s.rowHeight[u];s.height+=U-_,this.shiftToLastRow(s)}},d.prototype.tilingPreLayout=function(){a.TILE&&(this.groupZeroDegreeMembers(),this.clearCompounds(),this.clearZeroDegreeMembers())},d.prototype.tilingPostLayout=function(){a.TILE&&(this.repopulateZeroDegreeMembers(),this.repopulateCompounds())},d.prototype.reduceTrees=function(){for(var s=[],h=!0,u;h;){var p=this.graphManager.getAllNodes(),A=[];h=!1;for(var C=0;C<p.length;C++)u=p[C],u.getEdges().length==1&&!u.getEdges()[0].isInterGraph&&u.getChild()==null&&(A.push([u,u.getEdges()[0],u.getOwner()]),h=!0);if(h==!0){for(var R=[],x=0;x<A.length;x++)A[x][0].getEdges().length==1&&(R.push(A[x]),A[x][0].getOwner().remove(A[x][0]));s.push(R),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()}}this.prunedNodesAll=s},d.prototype.growTree=function(s){for(var h=s.length,u=s[h-1],p,A=0;A<u.length;A++)p=u[A],this.findPlaceforPrunedNode(p),p[2].add(p[0]),p[2].add(p[1],p[1].source,p[1].target);s.splice(s.length-1,1),this.graphManager.resetAllNodes(),this.graphManager.resetAllEdges()},d.prototype.findPlaceforPrunedNode=function(s){var h,u,p=s[0];p==s[1].source?u=s[1].target:u=s[1].source;var A=u.startX,C=u.finishX,R=u.startY,x=u.finishY,_=0,U=0,X=0,M=0,G=[_,X,U,M];if(R>0)for(var F=A;F<=C;F++)G[0]+=this.grid[F][R-1].length+this.grid[F][R].length-1;if(C<this.grid.length-1)for(var F=R;F<=x;F++)G[1]+=this.grid[C+1][F].length+this.grid[C][F].length-1;if(x<this.grid[0].length-1)for(var F=A;F<=C;F++)G[2]+=this.grid[F][x+1].length+this.grid[F][x].length-1;if(A>0)for(var F=R;F<=x;F++)G[3]+=this.grid[A-1][F].length+this.grid[A][F].length-1;for(var b=T.MAX_VALUE,Y,k,H=0;H<G.length;H++)G[H]<b?(b=G[H],Y=1,k=H):G[H]==b&&Y++;if(Y==3&&b==0)G[0]==0&&G[1]==0&&G[2]==0?h=1:G[0]==0&&G[1]==0&&G[3]==0?h=0:G[0]==0&&G[2]==0&&G[3]==0?h=3:G[1]==0&&G[2]==0&&G[3]==0&&(h=2);else if(Y==2&&b==0){var P=Math.floor(Math.random()*2);G[0]==0&&G[1]==0?P==0?h=0:h=1:G[0]==0&&G[2]==0?P==0?h=0:h=2:G[0]==0&&G[3]==0?P==0?h=0:h=3:G[1]==0&&G[2]==0?P==0?h=1:h=2:G[1]==0&&G[3]==0?P==0?h=1:h=3:P==0?h=2:h=3}else if(Y==4&&b==0){var P=Math.floor(Math.random()*4);h=P}else h=k;h==0?p.setCenter(u.getCenterX(),u.getCenterY()-u.getHeight()/2-v.DEFAULT_EDGE_LENGTH-p.getHeight()/2):h==1?p.setCenter(u.getCenterX()+u.getWidth()/2+v.DEFAULT_EDGE_LENGTH+p.getWidth()/2,u.getCenterY()):h==2?p.setCenter(u.getCenterX(),u.getCenterY()+u.getHeight()/2+v.DEFAULT_EDGE_LENGTH+p.getHeight()/2):p.setCenter(u.getCenterX()-u.getWidth()/2-v.DEFAULT_EDGE_LENGTH-p.getWidth()/2,u.getCenterY())},D.exports=d},function(D,y,r){var t={};t.layoutBase=r(0),t.CoSEConstants=r(1),t.CoSEEdge=r(2),t.CoSEGraph=r(3),t.CoSEGraphManager=r(4),t.CoSELayout=r(6),t.CoSENode=r(5),D.exports=t}])})}(it)),it.exports}(function(I,w){(function(D,y){I.exports=y(wt())})(ot,function(f){return function(D){var y={};function r(t){if(y[t])return y[t].exports;var e=y[t]={i:t,l:!1,exports:{}};return D[t].call(e.exports,e,e.exports,r),e.l=!0,e.exports}return r.m=D,r.c=y,r.i=function(t){return t},r.d=function(t,e,i){r.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:i})},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1)}([function(D,y){D.exports=f},function(D,y,r){var t=r(0).layoutBase.LayoutConstants,e=r(0).layoutBase.FDLayoutConstants,i=r(0).CoSEConstants,o=r(0).CoSELayout,g=r(0).CoSENode,a=r(0).layoutBase.PointD,v=r(0).layoutBase.DimensionD,n={ready:function(){},stop:function(){},quality:"default",nodeDimensionsIncludeLabels:!1,refresh:30,fit:!0,padding:10,randomize:!0,nodeRepulsion:4500,idealEdgeLength:50,edgeElasticity:.45,nestingFactor:.1,gravity:.25,numIter:2500,tile:!0,animate:"end",animationDuration:500,tilingPaddingVertical:10,tilingPaddingHorizontal:10,gravityRangeCompound:1.5,gravityCompound:1,gravityRange:3.8,initialEnergyOnIncremental:.5};function c(m,L){var O={};for(var d in m)O[d]=m[d];for(var d in L)O[d]=L[d];return O}function l(m){this.options=c(n,m),E(this.options)}var E=function(L){L.nodeRepulsion!=null&&(i.DEFAULT_REPULSION_STRENGTH=e.DEFAULT_REPULSION_STRENGTH=L.nodeRepulsion),L.idealEdgeLength!=null&&(i.DEFAULT_EDGE_LENGTH=e.DEFAULT_EDGE_LENGTH=L.idealEdgeLength),L.edgeElasticity!=null&&(i.DEFAULT_SPRING_STRENGTH=e.DEFAULT_SPRING_STRENGTH=L.edgeElasticity),L.nestingFactor!=null&&(i.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=e.PER_LEVEL_IDEAL_EDGE_LENGTH_FACTOR=L.nestingFactor),L.gravity!=null&&(i.DEFAULT_GRAVITY_STRENGTH=e.DEFAULT_GRAVITY_STRENGTH=L.gravity),L.numIter!=null&&(i.MAX_ITERATIONS=e.MAX_ITERATIONS=L.numIter),L.gravityRange!=null&&(i.DEFAULT_GRAVITY_RANGE_FACTOR=e.DEFAULT_GRAVITY_RANGE_FACTOR=L.gravityRange),L.gravityCompound!=null&&(i.DEFAULT_COMPOUND_GRAVITY_STRENGTH=e.DEFAULT_COMPOUND_GRAVITY_STRENGTH=L.gravityCompound),L.gravityRangeCompound!=null&&(i.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=e.DEFAULT_COMPOUND_GRAVITY_RANGE_FACTOR=L.gravityRangeCompound),L.initialEnergyOnIncremental!=null&&(i.DEFAULT_COOLING_FACTOR_INCREMENTAL=e.DEFAULT_COOLING_FACTOR_INCREMENTAL=L.initialEnergyOnIncremental),L.quality=="draft"?t.QUALITY=0:L.quality=="proof"?t.QUALITY=2:t.QUALITY=1,i.NODE_DIMENSIONS_INCLUDE_LABELS=e.NODE_DIMENSIONS_INCLUDE_LABELS=t.NODE_DIMENSIONS_INCLUDE_LABELS=L.nodeDimensionsIncludeLabels,i.DEFAULT_INCREMENTAL=e.DEFAULT_INCREMENTAL=t.DEFAULT_INCREMENTAL=!L.randomize,i.ANIMATE=e.ANIMATE=t.ANIMATE=L.animate,i.TILE=L.tile,i.TILING_PADDING_VERTICAL=typeof L.tilingPaddingVertical=="function"?L.tilingPaddingVertical.call():L.tilingPaddingVertical,i.TILING_PADDING_HORIZONTAL=typeof L.tilingPaddingHorizontal=="function"?L.tilingPaddingHorizontal.call():L.tilingPaddingHorizontal};l.prototype.run=function(){var m,L,O=this.options;this.idToLNode={};var d=this.layout=new o,N=this;N.stopped=!1,this.cy=this.options.cy,this.cy.trigger({type:"layoutstart",layout:this});var s=d.newGraphManager();this.gm=s;var h=this.options.eles.nodes(),u=this.options.eles.edges();this.root=s.addRoot(),this.processChildrenList(this.root,this.getTopMostNodes(h),d);for(var p=0;p<u.length;p++){var A=u[p],C=this.idToLNode[A.data("source")],R=this.idToLNode[A.data("target")];if(C!==R&&C.getEdgesBetween(R).length==0){var x=s.add(d.newEdge(),C,R);x.id=A.id()}}var _=function(M,G){typeof M=="number"&&(M=G);var F=M.data("id"),b=N.idToLNode[F];return{x:b.getRect().getCenterX(),y:b.getRect().getCenterY()}},U=function X(){for(var M=function(){O.fit&&O.cy.fit(O.eles,O.padding),m||(m=!0,N.cy.one("layoutready",O.ready),N.cy.trigger({type:"layoutready",layout:N}))},G=N.options.refresh,F,b=0;b<G&&!F;b++)F=N.stopped||N.layout.tick();if(F){d.checkLayoutSuccess()&&!d.isSubLayout&&d.doPostLayout(),d.tilingPostLayout&&d.tilingPostLayout(),d.isLayoutFinished=!0,N.options.eles.nodes().positions(_),M(),N.cy.one("layoutstop",N.options.stop),N.cy.trigger({type:"layoutstop",layout:N}),L&&cancelAnimationFrame(L),m=!1;return}var Y=N.layout.getPositionsData();O.eles.nodes().positions(function(k,H){if(typeof k=="number"&&(k=H),!k.isParent()){for(var P=k.id(),B=Y[P],$=k;B==null&&(B=Y[$.data("parent")]||Y["DummyCompound_"+$.data("parent")],Y[P]=B,$=$.parent()[0],$!=null););return B!=null?{x:B.x,y:B.y}:{x:k.position("x"),y:k.position("y")}}}),M(),L=requestAnimationFrame(X)};return d.addListener("layoutstarted",function(){N.options.animate==="during"&&(L=requestAnimationFrame(U))}),d.runLayout(),this.options.animate!=="during"&&(N.options.eles.nodes().not(":parent").layoutPositions(N,N.options,_),m=!1),this},l.prototype.getTopMostNodes=function(m){for(var L={},O=0;O<m.length;O++)L[m[O].id()]=!0;var d=m.filter(function(N,s){typeof N=="number"&&(N=s);for(var h=N.parent()[0];h!=null;){if(L[h.id()])return!1;h=h.parent()[0]}return!0});return d},l.prototype.processChildrenList=function(m,L,O){for(var d=L.length,N=0;N<d;N++){var s=L[N],h=s.children(),u,p=s.layoutDimensions({nodeDimensionsIncludeLabels:this.options.nodeDimensionsIncludeLabels});if(s.outerWidth()!=null&&s.outerHeight()!=null?u=m.add(new g(O.graphManager,new a(s.position("x")-p.w/2,s.position("y")-p.h/2),new v(parseFloat(p.w),parseFloat(p.h)))):u=m.add(new g(this.graphManager)),u.id=s.data("id"),u.paddingLeft=parseInt(s.css("padding")),u.paddingTop=parseInt(s.css("padding")),u.paddingRight=parseInt(s.css("padding")),u.paddingBottom=parseInt(s.css("padding")),this.options.nodeDimensionsIncludeLabels&&s.isParent()){var A=s.boundingBox({includeLabels:!0,includeNodes:!1}).w,C=s.boundingBox({includeLabels:!0,includeNodes:!1}).h,R=s.css("text-halign");u.labelWidth=A,u.labelHeight=C,u.labelPos=R}if(this.idToLNode[s.data("id")]=u,isNaN(u.rect.x)&&(u.rect.x=0),isNaN(u.rect.y)&&(u.rect.y=0),h!=null&&h.length>0){var x;x=O.getGraphManager().add(O.newGraph(),u),this.processChildrenList(x,h,O)}}},l.prototype.stop=function(){return this.stopped=!0,this};var T=function(L){L("layout","cose-bilkent",l)};typeof cytoscape<"u"&&T(cytoscape),D.exports=T}])})})(dt);var St=dt.exports;const Gt=xt(St);var st=function(){var I=S(function(O,d,N,s){for(N=N||{},s=O.length;s--;N[O[s]]=d);return N},"o"),w=[1,4],f=[1,13],D=[1,12],y=[1,15],r=[1,16],t=[1,20],e=[1,19],i=[6,7,8],o=[1,26],g=[1,24],a=[1,25],v=[6,7,11],n=[1,6,13,15,16,19,22],c=[1,33],l=[1,34],E=[1,6,7,11,13,15,16,19,22],T={trace:S(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,MINDMAP:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,ICON:15,CLASS:16,nodeWithId:17,nodeWithoutId:18,NODE_DSTART:19,NODE_DESCR:20,NODE_DEND:21,NODE_ID:22,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"MINDMAP",11:"EOF",13:"SPACELIST",15:"ICON",16:"CLASS",19:"NODE_DSTART",20:"NODE_DESCR",21:"NODE_DEND",22:"NODE_ID"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,2],[12,2],[12,2],[12,1],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[18,3],[17,1],[17,4]],performAction:S(function(d,N,s,h,u,p,A){var C=p.length-1;switch(u){case 6:case 7:return h;case 8:h.getLogger().trace("Stop NL ");break;case 9:h.getLogger().trace("Stop EOF ");break;case 11:h.getLogger().trace("Stop NL2 ");break;case 12:h.getLogger().trace("Stop EOF2 ");break;case 15:h.getLogger().info("Node: ",p[C].id),h.addNode(p[C-1].length,p[C].id,p[C].descr,p[C].type);break;case 16:h.getLogger().trace("Icon: ",p[C]),h.decorateNode({icon:p[C]});break;case 17:case 21:h.decorateNode({class:p[C]});break;case 18:h.getLogger().trace("SPACELIST");break;case 19:h.getLogger().trace("Node: ",p[C].id),h.addNode(0,p[C].id,p[C].descr,p[C].type);break;case 20:h.decorateNode({icon:p[C]});break;case 25:h.getLogger().trace("node found ..",p[C-2]),this.$={id:p[C-1],descr:p[C-1],type:h.getType(p[C-2],p[C])};break;case 26:this.$={id:p[C],descr:p[C],type:h.nodeType.DEFAULT};break;case 27:h.getLogger().trace("node found ..",p[C-3]),this.$={id:p[C-3],descr:p[C-1],type:h.getType(p[C-2],p[C])};break}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:w},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:w},{6:f,7:[1,10],9:9,12:11,13:D,14:14,15:y,16:r,17:17,18:18,19:t,22:e},I(i,[2,3]),{1:[2,2]},I(i,[2,4]),I(i,[2,5]),{1:[2,6],6:f,12:21,13:D,14:14,15:y,16:r,17:17,18:18,19:t,22:e},{6:f,9:22,12:11,13:D,14:14,15:y,16:r,17:17,18:18,19:t,22:e},{6:o,7:g,10:23,11:a},I(v,[2,22],{17:17,18:18,14:27,15:[1,28],16:[1,29],19:t,22:e}),I(v,[2,18]),I(v,[2,19]),I(v,[2,20]),I(v,[2,21]),I(v,[2,23]),I(v,[2,24]),I(v,[2,26],{19:[1,30]}),{20:[1,31]},{6:o,7:g,10:32,11:a},{1:[2,7],6:f,12:21,13:D,14:14,15:y,16:r,17:17,18:18,19:t,22:e},I(n,[2,14],{7:c,11:l}),I(E,[2,8]),I(E,[2,9]),I(E,[2,10]),I(v,[2,15]),I(v,[2,16]),I(v,[2,17]),{20:[1,35]},{21:[1,36]},I(n,[2,13],{7:c,11:l}),I(E,[2,11]),I(E,[2,12]),{21:[1,37]},I(v,[2,25]),I(v,[2,27])],defaultActions:{2:[2,1],6:[2,2]},parseError:S(function(d,N){if(N.recoverable)this.trace(d);else{var s=new Error(d);throw s.hash=N,s}},"parseError"),parse:S(function(d){var N=this,s=[0],h=[],u=[null],p=[],A=this.table,C="",R=0,x=0,_=2,U=1,X=p.slice.call(arguments,1),M=Object.create(this.lexer),G={yy:{}};for(var F in this.yy)Object.prototype.hasOwnProperty.call(this.yy,F)&&(G.yy[F]=this.yy[F]);M.setInput(d,G.yy),G.yy.lexer=M,G.yy.parser=this,typeof M.yylloc>"u"&&(M.yylloc={});var b=M.yylloc;p.push(b);var Y=M.options&&M.options.ranges;typeof G.yy.parseError=="function"?this.parseError=G.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function k(V){s.length=s.length-2*V,u.length=u.length-V,p.length=p.length-V}S(k,"popStack");function H(){var V;return V=h.pop()||M.lex()||U,typeof V!="number"&&(V instanceof Array&&(h=V,V=h.pop()),V=N.symbols_[V]||V),V}S(H,"lex");for(var P,B,$,K,Q={},J,j,gt,q;;){if(B=s[s.length-1],this.defaultActions[B]?$=this.defaultActions[B]:((P===null||typeof P>"u")&&(P=H()),$=A[B]&&A[B][P]),typeof $>"u"||!$.length||!$[0]){var rt="";q=[];for(J in A[B])this.terminals_[J]&&J>_&&q.push("'"+this.terminals_[J]+"'");M.showPosition?rt="Parse error on line "+(R+1)+`:
`+M.showPosition()+`
Expecting `+q.join(", ")+", got '"+(this.terminals_[P]||P)+"'":rt="Parse error on line "+(R+1)+": Unexpected "+(P==U?"end of input":"'"+(this.terminals_[P]||P)+"'"),this.parseError(rt,{text:M.match,token:this.terminals_[P]||P,line:M.yylineno,loc:b,expected:q})}if($[0]instanceof Array&&$.length>1)throw new Error("Parse Error: multiple actions possible at state: "+B+", token: "+P);switch($[0]){case 1:s.push(P),u.push(M.yytext),p.push(M.yylloc),s.push($[1]),P=null,x=M.yyleng,C=M.yytext,R=M.yylineno,b=M.yylloc;break;case 2:if(j=this.productions_[$[1]][1],Q.$=u[u.length-j],Q._$={first_line:p[p.length-(j||1)].first_line,last_line:p[p.length-1].last_line,first_column:p[p.length-(j||1)].first_column,last_column:p[p.length-1].last_column},Y&&(Q._$.range=[p[p.length-(j||1)].range[0],p[p.length-1].range[1]]),K=this.performAction.apply(Q,[C,x,R,G.yy,$[1],u,p].concat(X)),typeof K<"u")return K;j&&(s=s.slice(0,-1*j*2),u=u.slice(0,-1*j),p=p.slice(0,-1*j)),s.push(this.productions_[$[1]][0]),u.push(Q.$),p.push(Q._$),gt=A[s[s.length-2]][s[s.length-1]],s.push(gt);break;case 3:return!0}}return!0},"parse")},m=function(){var O={EOF:1,parseError:S(function(N,s){if(this.yy.parser)this.yy.parser.parseError(N,s);else throw new Error(N)},"parseError"),setInput:S(function(d,N){return this.yy=N||this.yy||{},this._input=d,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:S(function(){var d=this._input[0];this.yytext+=d,this.yyleng++,this.offset++,this.match+=d,this.matched+=d;var N=d.match(/(?:\r\n?|\n).*/g);return N?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),d},"input"),unput:S(function(d){var N=d.length,s=d.split(/(?:\r\n?|\n)/g);this._input=d+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-N),this.offset-=N;var h=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var u=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===h.length?this.yylloc.first_column:0)+h[h.length-s.length].length-s[0].length:this.yylloc.first_column-N},this.options.ranges&&(this.yylloc.range=[u[0],u[0]+this.yyleng-N]),this.yyleng=this.yytext.length,this},"unput"),more:S(function(){return this._more=!0,this},"more"),reject:S(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:S(function(d){this.unput(this.match.slice(d))},"less"),pastInput:S(function(){var d=this.matched.substr(0,this.matched.length-this.match.length);return(d.length>20?"...":"")+d.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:S(function(){var d=this.match;return d.length<20&&(d+=this._input.substr(0,20-d.length)),(d.substr(0,20)+(d.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:S(function(){var d=this.pastInput(),N=new Array(d.length+1).join("-");return d+this.upcomingInput()+`
`+N+"^"},"showPosition"),test_match:S(function(d,N){var s,h,u;if(this.options.backtrack_lexer&&(u={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(u.yylloc.range=this.yylloc.range.slice(0))),h=d[0].match(/(?:\r\n?|\n).*/g),h&&(this.yylineno+=h.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:h?h[h.length-1].length-h[h.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+d[0].length},this.yytext+=d[0],this.match+=d[0],this.matches=d,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(d[0].length),this.matched+=d[0],s=this.performAction.call(this,this.yy,this,N,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),s)return s;if(this._backtrack){for(var p in u)this[p]=u[p];return!1}return!1},"test_match"),next:S(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var d,N,s,h;this._more||(this.yytext="",this.match="");for(var u=this._currentRules(),p=0;p<u.length;p++)if(s=this._input.match(this.rules[u[p]]),s&&(!N||s[0].length>N[0].length)){if(N=s,h=p,this.options.backtrack_lexer){if(d=this.test_match(s,u[p]),d!==!1)return d;if(this._backtrack){N=!1;continue}else return!1}else if(!this.options.flex)break}return N?(d=this.test_match(N,u[h]),d!==!1?d:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:S(function(){var N=this.next();return N||this.lex()},"lex"),begin:S(function(N){this.conditionStack.push(N)},"begin"),popState:S(function(){var N=this.conditionStack.length-1;return N>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:S(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:S(function(N){return N=this.conditionStack.length-1-Math.abs(N||0),N>=0?this.conditionStack[N]:"INITIAL"},"topState"),pushState:S(function(N){this.begin(N)},"pushState"),stateStackSize:S(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:S(function(N,s,h,u){switch(h){case 0:return N.getLogger().trace("Found comment",s.yytext),6;case 1:return 8;case 2:this.begin("CLASS");break;case 3:return this.popState(),16;case 4:this.popState();break;case 5:N.getLogger().trace("Begin icon"),this.begin("ICON");break;case 6:return N.getLogger().trace("SPACELINE"),6;case 7:return 7;case 8:return 15;case 9:N.getLogger().trace("end icon"),this.popState();break;case 10:return N.getLogger().trace("Exploding node"),this.begin("NODE"),19;case 11:return N.getLogger().trace("Cloud"),this.begin("NODE"),19;case 12:return N.getLogger().trace("Explosion Bang"),this.begin("NODE"),19;case 13:return N.getLogger().trace("Cloud Bang"),this.begin("NODE"),19;case 14:return this.begin("NODE"),19;case 15:return this.begin("NODE"),19;case 16:return this.begin("NODE"),19;case 17:return this.begin("NODE"),19;case 18:return 13;case 19:return 22;case 20:return 11;case 21:this.begin("NSTR2");break;case 22:return"NODE_DESCR";case 23:this.popState();break;case 24:N.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 25:return N.getLogger().trace("description:",s.yytext),"NODE_DESCR";case 26:this.popState();break;case 27:return this.popState(),N.getLogger().trace("node end ))"),"NODE_DEND";case 28:return this.popState(),N.getLogger().trace("node end )"),"NODE_DEND";case 29:return this.popState(),N.getLogger().trace("node end ...",s.yytext),"NODE_DEND";case 30:return this.popState(),N.getLogger().trace("node end (("),"NODE_DEND";case 31:return this.popState(),N.getLogger().trace("node end (-"),"NODE_DEND";case 32:return this.popState(),N.getLogger().trace("node end (-"),"NODE_DEND";case 33:return this.popState(),N.getLogger().trace("node end (("),"NODE_DEND";case 34:return this.popState(),N.getLogger().trace("node end (("),"NODE_DEND";case 35:return N.getLogger().trace("Long description:",s.yytext),20;case 36:return N.getLogger().trace("Long description:",s.yytext),20}},"anonymous"),rules:[/^(?:\s*%%.*)/i,/^(?:mindmap\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{CLASS:{rules:[3,4],inclusive:!1},ICON:{rules:[8,9],inclusive:!1},NSTR2:{rules:[22,23],inclusive:!1},NSTR:{rules:[25,26],inclusive:!1},NODE:{rules:[21,24,27,28,29,30,31,32,33,34,35,36],inclusive:!1},INITIAL:{rules:[0,1,2,5,6,7,10,11,12,13,14,15,16,17,18,19,20],inclusive:!0}}};return O}();T.lexer=m;function L(){this.yy={}}return S(L,"Parser"),L.prototype=T,T.Parser=L,new L}();st.parser=st;var _t=st,Z=[],vt=0,ht={},Ft=S(()=>{Z=[],vt=0,ht={}},"clear"),bt=S(function(I){for(let w=Z.length-1;w>=0;w--)if(Z[w].level<I)return Z[w];return null},"getParent"),Ut=S(()=>Z.length>0?Z[0]:null,"getMindmap"),Pt=S((I,w,f,D)=>{var i,o;z.info("addNode",I,w,f,D);const y=at();let r=((i=y.mindmap)==null?void 0:i.padding)??tt.mindmap.padding;switch(D){case W.ROUNDED_RECT:case W.RECT:case W.HEXAGON:r*=2}const t={id:vt++,nodeId:et(w,y),level:I,descr:et(f,y),type:D,children:[],width:((o=y.mindmap)==null?void 0:o.maxNodeWidth)??tt.mindmap.maxNodeWidth,padding:r},e=bt(I);if(e)e.children.push(t),Z.push(t);else if(Z.length===0)Z.push(t);else throw new Error('There can be only one root. No parent could be found for ("'+t.descr+'")')},"addNode"),W={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},Yt=S((I,w)=>{switch(z.debug("In get type",I,w),I){case"[":return W.RECT;case"(":return w===")"?W.ROUNDED_RECT:W.CLOUD;case"((":return W.CIRCLE;case")":return W.CLOUD;case"))":return W.BANG;case"{{":return W.HEXAGON;default:return W.DEFAULT}},"getType"),Xt=S((I,w)=>{ht[I]=w},"setElementForId"),kt=S(I=>{if(!I)return;const w=at(),f=Z[Z.length-1];I.icon&&(f.icon=et(I.icon,w)),I.class&&(f.class=et(I.class,w))},"decorateNode"),Ht=S(I=>{switch(I){case W.DEFAULT:return"no-border";case W.RECT:return"rect";case W.ROUNDED_RECT:return"rounded-rect";case W.CIRCLE:return"circle";case W.CLOUD:return"cloud";case W.BANG:return"bang";case W.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),$t=S(()=>z,"getLogger"),Wt=S(I=>ht[I],"getElementById"),Bt={clear:Ft,addNode:Pt,getMindmap:Ut,nodeType:W,getType:Yt,setElementForId:Xt,decorateNode:kt,type2Str:Ht,getLogger:$t,getElementById:Wt},Vt=Bt,Zt=12,Qt=S(function(I,w,f,D){w.append("path").attr("id","node-"+f.id).attr("class","node-bkg node-"+I.type2Str(f.type)).attr("d",`M0 ${f.height-5} v${-f.height+2*5} q0,-5 5,-5 h${f.width-2*5} q5,0 5,5 v${f.height-5} H0 Z`),w.append("line").attr("class","node-line-"+D).attr("x1",0).attr("y1",f.height).attr("x2",f.width).attr("y2",f.height)},"defaultBkg"),jt=S(function(I,w,f){w.append("rect").attr("id","node-"+f.id).attr("class","node-bkg node-"+I.type2Str(f.type)).attr("height",f.height).attr("width",f.width)},"rectBkg"),zt=S(function(I,w,f){const D=f.width,y=f.height,r=.15*D,t=.25*D,e=.35*D,i=.2*D;w.append("path").attr("id","node-"+f.id).attr("class","node-bkg node-"+I.type2Str(f.type)).attr("d",`M0 0 a${r},${r} 0 0,1 ${D*.25},${-1*D*.1}
      a${e},${e} 1 0,1 ${D*.4},${-1*D*.1}
      a${t},${t} 1 0,1 ${D*.35},${1*D*.2}

      a${r},${r} 1 0,1 ${D*.15},${1*y*.35}
      a${i},${i} 1 0,1 ${-1*D*.15},${1*y*.65}

      a${t},${r} 1 0,1 ${-1*D*.25},${D*.15}
      a${e},${e} 1 0,1 ${-1*D*.5},0
      a${r},${r} 1 0,1 ${-1*D*.25},${-1*D*.15}

      a${r},${r} 1 0,1 ${-1*D*.1},${-1*y*.35}
      a${i},${i} 1 0,1 ${D*.1},${-1*y*.65}

    H0 V0 Z`)},"cloudBkg"),Kt=S(function(I,w,f){const D=f.width,y=f.height,r=.15*D;w.append("path").attr("id","node-"+f.id).attr("class","node-bkg node-"+I.type2Str(f.type)).attr("d",`M0 0 a${r},${r} 1 0,0 ${D*.25},${-1*y*.1}
      a${r},${r} 1 0,0 ${D*.25},0
      a${r},${r} 1 0,0 ${D*.25},0
      a${r},${r} 1 0,0 ${D*.25},${1*y*.1}

      a${r},${r} 1 0,0 ${D*.15},${1*y*.33}
      a${r*.8},${r*.8} 1 0,0 0,${1*y*.34}
      a${r},${r} 1 0,0 ${-1*D*.15},${1*y*.33}

      a${r},${r} 1 0,0 ${-1*D*.25},${y*.15}
      a${r},${r} 1 0,0 ${-1*D*.25},0
      a${r},${r} 1 0,0 ${-1*D*.25},0
      a${r},${r} 1 0,0 ${-1*D*.25},${-1*y*.15}

      a${r},${r} 1 0,0 ${-1*D*.1},${-1*y*.33}
      a${r*.8},${r*.8} 1 0,0 0,${-1*y*.34}
      a${r},${r} 1 0,0 ${D*.1},${-1*y*.33}

    H0 V0 Z`)},"bangBkg"),Jt=S(function(I,w,f){w.append("circle").attr("id","node-"+f.id).attr("class","node-bkg node-"+I.type2Str(f.type)).attr("r",f.width/2)},"circleBkg");function yt(I,w,f,D,y){return I.insert("polygon",":first-child").attr("points",D.map(function(r){return r.x+","+r.y}).join(" ")).attr("transform","translate("+(y.width-w)/2+", "+f+")")}S(yt,"insertPolygonShape");var qt=S(function(I,w,f){const D=f.height,r=D/4,t=f.width-f.padding+2*r,e=[{x:r,y:0},{x:t-r,y:0},{x:t,y:-D/2},{x:t-r,y:-D},{x:r,y:-D},{x:0,y:-D/2}];yt(w,t,D,e,f)},"hexagonBkg"),te=S(function(I,w,f){w.append("rect").attr("id","node-"+f.id).attr("class","node-bkg node-"+I.type2Str(f.type)).attr("height",f.height).attr("rx",f.padding).attr("ry",f.padding).attr("width",f.width)},"roundedRectBkg"),ee=S(async function(I,w,f,D,y){const r=y.htmlLabels,t=D%(Zt-1),e=w.append("g");f.section=t;let i="section-"+t;t<0&&(i+=" section-root"),e.attr("class",(f.class?f.class+" ":"")+"mindmap-node "+i);const o=e.append("g"),g=e.append("g"),a=f.descr.replace(/(<br\/*>)/g,`
`);await Ct(g,a,{useHtmlLabels:r,width:f.width,classes:"mindmap-node-label"},y),r||g.attr("dy","1em").attr("alignment-baseline","middle").attr("dominant-baseline","middle").attr("text-anchor","middle");const v=g.node().getBBox(),[n]=Rt(y.fontSize);if(f.height=v.height+n*1.1*.5+f.padding,f.width=v.width+2*f.padding,f.icon)if(f.type===I.nodeType.CIRCLE)f.height+=50,f.width+=50,e.append("foreignObject").attr("height","50px").attr("width",f.width).attr("style","text-align: center;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+t+" "+f.icon),g.attr("transform","translate("+f.width/2+", "+(f.height/2-1.5*f.padding)+")");else{f.width+=50;const c=f.height;f.height=Math.max(c,60);const l=Math.abs(f.height-c);e.append("foreignObject").attr("width","60px").attr("height",f.height).attr("style","text-align: center;margin-top:"+l/2+"px;").append("div").attr("class","icon-container").append("i").attr("class","node-icon-"+t+" "+f.icon),g.attr("transform","translate("+(25+f.width/2)+", "+(l/2+f.padding/2)+")")}else if(r){const c=(f.width-v.width)/2,l=(f.height-v.height)/2;g.attr("transform","translate("+c+", "+l+")")}else{const c=f.width/2,l=f.padding/2;g.attr("transform","translate("+c+", "+l+")")}switch(f.type){case I.nodeType.DEFAULT:Qt(I,o,f,t);break;case I.nodeType.ROUNDED_RECT:te(I,o,f,t);break;case I.nodeType.RECT:jt(I,o,f,t);break;case I.nodeType.CIRCLE:o.attr("transform","translate("+f.width/2+", "+ +f.height/2+")"),Jt(I,o,f,t);break;case I.nodeType.CLOUD:zt(I,o,f,t);break;case I.nodeType.BANG:Kt(I,o,f,t);break;case I.nodeType.HEXAGON:qt(I,o,f,t);break}return I.setElementForId(f.id,e),f.height},"drawNode"),re=S(function(I,w){const f=I.getElementById(w.id),D=w.x||0,y=w.y||0;f.attr("transform","translate("+D+","+y+")")},"positionNode");pt.use(Gt);async function lt(I,w,f,D,y){await ee(I,w,f,D,y),f.children&&await Promise.all(f.children.map((r,t)=>lt(I,w,r,D<0?t:D,y)))}S(lt,"drawNodes");function Et(I,w){w.edges().map((f,D)=>{const y=f.data();if(f[0]._private.bodyBounds){const r=f[0]._private.rscratch;z.trace("Edge: ",D,y),I.insert("path").attr("d",`M ${r.startX},${r.startY} L ${r.midX},${r.midY} L${r.endX},${r.endY} `).attr("class","edge section-edge-"+y.section+" edge-depth-"+y.depth)}})}S(Et,"drawEdges");function ct(I,w,f,D){w.add({group:"nodes",data:{id:I.id.toString(),labelText:I.descr,height:I.height,width:I.width,level:D,nodeId:I.id,padding:I.padding,type:I.type},position:{x:I.x,y:I.y}}),I.children&&I.children.forEach(y=>{ct(y,w,f,D+1),w.add({group:"edges",data:{id:`${I.id}_${y.id}`,source:I.id,target:y.id,depth:D,section:y.section}})})}S(ct,"addNodes");function Lt(I,w){return new Promise(f=>{const D=It("body").append("div").attr("id","cy").attr("style","display:none"),y=pt({container:document.getElementById("cy"),style:[{selector:"edge",style:{"curve-style":"bezier"}}]});D.remove(),ct(I,y,w,0),y.nodes().forEach(function(r){r.layoutDimensions=()=>{const t=r.data();return{w:t.width,h:t.height}}}),y.layout({name:"cose-bilkent",quality:"proof",styleEnabled:!1,animate:!1}).run(),y.ready(r=>{z.info("Ready",r),f(y)})})}S(Lt,"layoutMindmap");function Tt(I,w){w.nodes().map((f,D)=>{const y=f.data();y.x=f.position().x,y.y=f.position().y,re(I,y);const r=I.getElementById(y.nodeId);z.info("Id:",D,"Position: (",f.position().x,", ",f.position().y,")",y),r.attr("transform",`translate(${f.position().x-y.width/2}, ${f.position().y-y.height/2})`),r.attr("attr",`apa-${D})`)})}S(Tt,"positionNodes");var ie=S(async(I,w,f,D)=>{var a,v;z.debug(`Rendering mindmap diagram
`+I);const y=D.db,r=y.getMindmap();if(!r)return;const t=at();t.htmlLabels=!1;const e=Nt(w),i=e.append("g");i.attr("class","mindmap-edges");const o=e.append("g");o.attr("class","mindmap-nodes"),await lt(y,o,r,-1,t);const g=await Lt(r,t);Et(i,g),Tt(y,g),mt(void 0,e,((a=t.mindmap)==null?void 0:a.padding)??tt.mindmap.padding,((v=t.mindmap)==null?void 0:v.useMaxWidth)??tt.mindmap.useMaxWidth)},"draw"),ne={draw:ie},se=S(I=>{let w="";for(let f=0;f<I.THEME_COLOR_LIMIT;f++)I["lineColor"+f]=I["lineColor"+f]||I["cScaleInv"+f],Dt(I["lineColor"+f])?I["lineColor"+f]=Ot(I["lineColor"+f],20):I["lineColor"+f]=At(I["lineColor"+f],20);for(let f=0;f<I.THEME_COLOR_LIMIT;f++){const D=""+(17-3*f);w+=`
    .section-${f-1} rect, .section-${f-1} path, .section-${f-1} circle, .section-${f-1} polygon, .section-${f-1} path  {
      fill: ${I["cScale"+f]};
    }
    .section-${f-1} text {
     fill: ${I["cScaleLabel"+f]};
    }
    .node-icon-${f-1} {
      font-size: 40px;
      color: ${I["cScaleLabel"+f]};
    }
    .section-edge-${f-1}{
      stroke: ${I["cScale"+f]};
    }
    .edge-depth-${f-1}{
      stroke-width: ${D};
    }
    .section-${f-1} line {
      stroke: ${I["cScaleInv"+f]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }
    `}return w},"genSections"),ae=S(I=>`
  .edge {
    stroke-width: 3;
  }
  ${se(I)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${I.git0};
  }
  .section-root text {
    fill: ${I.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .mindmap-node-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles"),oe=ae,ue={db:Vt,renderer:ne,parser:_t,styles:oe};export{ue as diagram};
