{"version": 3, "file": "FileDescriptorQueue.js", "sourceRoot": "", "sources": ["../../../src/FileSystem/FileDescriptorQueue.ts"], "names": [], "mappings": ";;;;;;AAAA,4CAAwC;AACxC,mCAA+B;AAU/B;;;;;;;;;;;GAWG;AACH,MAAa,mBAAmB;IAG/B,YAAoB,UAAkB;QAAlB,eAAU,GAAV,UAAU,CAAQ;QAF9B,gBAAW,GAAG,CAAC,CAAA;QACf,gBAAW,GAAG,IAAI,aAAK,EAAO,CAAA;IACI,CAAC;IAE3C,IAAI,CAAC,IAAY,EAAE,KAAoB,EAAE,QAA0B;QAClE,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;YACxB,IAAI,EAAE,IAAI;YACV,KAAK,EAAE,KAAK;YACZ,QAAQ,EAAE,QAAQ;SAClB,CAAC,CAAA;QACF,IAAI,CAAC,OAAO,EAAE,CAAA;IACf,CAAC;IAED,OAAO;QACN,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE;YAC3E,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,EAAS,CAAA;YAC7C,IAAI,CAAC,WAAW,EAAE,CAAA;YAClB,YAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAA;SAC1C;IACF,CAAC;IAED,KAAK,CAAC,EAAU,EAAE,QAAyB;QAC1C,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,YAAE,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;QACtB,IAAI,CAAC,OAAO,EAAE,CAAA;IACf,CAAC;IAED,WAAW,CAAC,IAAY,EAAE,KAAoB;QAC7C,OAAO,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE;gBAClC,IAAI,GAAG,EAAE;oBACR,MAAM,CAAC,GAAG,CAAC,CAAA;iBACX;qBAAM;oBACN,OAAO,CAAC,EAAE,CAAC,CAAA;iBACX;YACF,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC;IAED,YAAY,CAAC,EAAU;QACtB,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC5C,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;gBACtB,IAAI,GAAG,EAAE;oBACR,MAAM,CAAC,GAAG,CAAC,CAAA;iBACX;qBAAM;oBACN,OAAO,EAAE,CAAA;iBACT;YACF,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC;CACD;AAnDD,kDAmDC"}