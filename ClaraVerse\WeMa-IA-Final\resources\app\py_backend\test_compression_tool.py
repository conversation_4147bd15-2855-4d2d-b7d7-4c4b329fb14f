#!/usr/bin/env python3
"""
🧪 Outil de test pour la compression avec LM Studio
Test complet du nouveau système de compression
"""

import sys
import os
import json
import time
from typing import List, Dict, Any

# Ajouter le répertoire parent au path pour importer perfect_compressor
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from perfect_compressor import PerfectCompressor

def test_lmstudio_connection():
    """Test de connexion LM Studio"""
    print("🔍 Test de connexion LM Studio...")
    
    try:
        compressor = PerfectCompressor()
        print(f"✅ Compresseur initialisé avec: {compressor.lmstudio_url}")
        return compressor
    except Exception as e:
        print(f"❌ Erreur initialisation: {e}")
        return None

def test_compression_simple(compressor: PerfectCompressor):
    """Test de compression simple"""
    print("\n🧪 Test compression simple...")
    
    # Messages de test
    test_messages = [
        {
            "role": "user",
            "content": "Bonjour ! Peux-tu m'expliquer comment fonctionne l'intelligence artificielle ?"
        },
        {
            "role": "assistant",
            "content": "Bonjour ! L'intelligence artificielle est un domaine fascinant de l'informatique qui vise à créer des systèmes capables de réaliser des tâches qui nécessitent normalement l'intelligence humaine. Cela inclut l'apprentissage automatique, le raisonnement logique, la perception visuelle et auditive, et la prise de décision complexe. Les techniques principales incluent l'apprentissage automatique (machine learning), les réseaux de neurones artificiels, et l'apprentissage profond (deep learning). Ces technologies permettent aux machines d'apprendre à partir de données et d'améliorer leurs performances au fil du temps."
        },
        {
            "role": "user",
            "content": "Et comment fonctionne exactement l'apprentissage automatique ? Peux-tu me donner des exemples concrets ?"
        },
        {
            "role": "assistant",
            "content": "Excellente question ! L'apprentissage automatique est une sous-catégorie de l'IA où les algorithmes apprennent à partir de données sans être explicitement programmés pour chaque tâche spécifique. Il existe trois types principaux : 1) L'apprentissage supervisé (avec des exemples étiquetés, comme la classification d'emails en spam/non-spam), 2) L'apprentissage non supervisé (découverte de patterns cachés, comme le regroupement de clients par comportement d'achat), et 3) L'apprentissage par renforcement (apprentissage par essai-erreur avec récompenses, comme les jeux vidéo ou la conduite autonome). Des exemples concrets incluent la reconnaissance vocale de Siri, les recommandations Netflix, la détection de fraude bancaire, et la traduction automatique de Google."
        },
        {
            "role": "user", 
            "content": "Merci pour ces explications ! Peux-tu maintenant me parler des réseaux de neurones ?"
        },
        {
            "role": "assistant",
            "content": "Avec plaisir ! Les réseaux de neurones artificiels s'inspirent du fonctionnement du cerveau humain. Ils sont composés de neurones artificiels (ou nœuds) organisés en couches : une couche d'entrée, une ou plusieurs couches cachées, et une couche de sortie. Chaque neurone reçoit des signaux, les traite avec une fonction d'activation, et transmet le résultat. L'apprentissage se fait par ajustement des poids des connexions entre neurones grâce à la rétropropagation. Les réseaux profonds (deep learning) utilisent de nombreuses couches cachées et excellent dans la reconnaissance d'images, le traitement du langage naturel, et la génération de contenu. Des architectures spécialisées existent : CNN pour les images, RNN pour les séquences, Transformers pour le langage."
        }
    ]
    
    try:
        # Configuration de test
        config = {
            "compression_threshold": 1000,  # Seuil bas pour forcer la compression
            "max_tokens": 35000,
            "target_tokens": 15000,
            "compression_model": "qwen3-14b-optimized"
        }
        
        print(f"📊 Messages originaux: {len(test_messages)}")
        original_chars = sum(len(msg["content"]) for msg in test_messages)
        print(f"📊 Caractères originaux: {original_chars}")
        
        start_time = time.time()
        compressed_messages, compression_info = compressor.compress_conversation(test_messages)
        end_time = time.time()

        compressed_chars = sum(len(msg["content"]) for msg in compressed_messages)
        ratio = original_chars / compressed_chars if compressed_chars > 0 else 0
        
        print(f"✅ Compression réussie!")
        print(f"📊 Messages compressés: {len(compressed_messages)}")
        print(f"📊 Caractères compressés: {compressed_chars}")
        print(f"📊 Ratio de compression: {ratio:.2f}x")
        print(f"⏱️ Temps de traitement: {end_time - start_time:.2f}s")
        
        # Afficher un aperçu
        if compressed_messages:
            print(f"\n🔍 Aperçu du premier message compressé:")
            print(f"   {compressed_messages[0]['content'][:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur compression: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compression_avec_documents(compressor: PerfectCompressor):
    """Test avec documents simulés"""
    print("\n🧪 Test compression avec documents...")
    
    # Simuler une conversation avec documents
    test_messages = [
        {
            "role": "user",
            "content": "J'ai uploadé un document sur les finances. Peux-tu l'analyser ?"
        },
        {
            "role": "assistant",
            "content": "Je vais analyser votre document financier. D'après le contenu, il s'agit d'un rapport trimestriel montrant une croissance de 15% du chiffre d'affaires, avec des dépenses maîtrisées et une marge bénéficiaire en amélioration. Les indicateurs clés sont positifs."
        },
        {
            "role": "user",
            "content": "Quelles sont les recommandations principales ?"
        },
        {
            "role": "assistant",
            "content": "Basé sur l'analyse du document, voici mes recommandations : 1) Maintenir la stratégie actuelle qui génère cette croissance, 2) Investir dans les secteurs les plus performants identifiés, 3) Optimiser davantage les coûts opérationnels, 4) Prévoir une réserve de trésorerie pour les opportunités futures, 5) Surveiller les indicateurs de risque mentionnés dans le rapport."
        }
    ]
    
    # Ajouter un contexte RAG simulé
    rag_context = """
    DOCUMENT FINANCIER - RAPPORT Q3 2024
    
    Chiffre d'affaires: 2.5M€ (+15% vs Q3 2023)
    Charges d'exploitation: 1.8M€ (+8% vs Q3 2023)
    Résultat net: 0.7M€ (+35% vs Q3 2023)
    
    Secteurs performants:
    - E-commerce: +25%
    - Services digitaux: +18%
    - Consulting: +12%
    
    Risques identifiés:
    - Inflation des coûts matières premières
    - Concurrence accrue sur le marché digital
    - Dépendance à 3 clients majeurs (60% du CA)
    
    Recommandations:
    - Diversification client
    - Investissement R&D
    - Optimisation supply chain
    """
    
    try:
        config = {
            "compression_threshold": 500,  # Seuil très bas
            "max_tokens": 35000,
            "target_tokens": 15000,
            "compression_model": "qwen3-14b-optimized"
        }
        
        print(f"📊 Messages + contexte RAG")
        original_chars = sum(len(msg["content"]) for msg in test_messages) + len(rag_context)
        print(f"📊 Caractères originaux: {original_chars}")
        
        start_time = time.time()
        compressed_messages, compression_info = compressor.compress_conversation(test_messages)
        end_time = time.time()
        
        compressed_chars = sum(len(msg["content"]) for msg in compressed_messages)
        ratio = original_chars / compressed_chars if compressed_chars > 0 else 0
        
        print(f"✅ Compression avec RAG réussie!")
        print(f"📊 Messages compressés: {len(compressed_messages)}")
        print(f"📊 Caractères compressés: {compressed_chars}")
        print(f"📊 Ratio de compression: {ratio:.2f}x")
        print(f"⏱️ Temps de traitement: {end_time - start_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur compression avec RAG: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🧪 Test Complet - Perfect Compressor LM Studio")
    print("=" * 60)
    
    # 1. Test de connexion
    compressor = test_lmstudio_connection()
    if not compressor:
        print("❌ Impossible de continuer sans connexion LM Studio")
        return
    
    # 2. Test compression simple
    success1 = test_compression_simple(compressor)
    
    # 3. Test compression avec documents
    success2 = test_compression_avec_documents(compressor)
    
    # 4. Résumé
    print("\n" + "=" * 60)
    print("🏁 Résumé des tests:")
    print(f"   Compression simple: {'✅' if success1 else '❌'}")
    print(f"   Compression avec RAG: {'✅' if success2 else '❌'}")
    
    if success1 and success2:
        print("🎉 Tous les tests sont passés ! La compression LM Studio fonctionne.")
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez la configuration.")

if __name__ == "__main__":
    main()
