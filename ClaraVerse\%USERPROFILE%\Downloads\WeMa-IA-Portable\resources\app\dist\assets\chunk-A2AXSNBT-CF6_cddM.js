import{g as et,s as tt}from"./chunk-RZ5BOZE2-DjixKW2-.js";import{_ as f,l as Oe,c as F,q as st,r as it,u as we,d as $,b as at,a as nt,s as rt,g as ut,v as lt,x as ct,k as v,B as ot,A as ht,i as dt,a9 as R}from"./index-Bcuwyvq3.js";var Ve=function(){var s=f(function(I,c,h,p){for(h=h||{},p=I.length;p--;h[I[p]]=c);return h},"o"),i=[1,18],a=[1,19],u=[1,20],l=[1,41],r=[1,42],o=[1,26],A=[1,24],g=[1,25],k=[1,32],L=[1,33],Ae=[1,34],m=[1,45],fe=[1,35],ge=[1,36],Ce=[1,37],me=[1,38],be=[1,27],Ee=[1,28],ye=[1,29],Te=[1,30],ke=[1,31],b=[1,44],E=[1,46],y=[1,43],D=[1,47],De=[1,9],d=[1,8,9],ee=[1,58],te=[1,59],se=[1,60],ie=[1,61],ae=[1,62],Fe=[1,63],Be=[1,64],ne=[1,8,9,41],Pe=[1,76],P=[1,8,9,12,13,22,39,41,44,66,67,68,69,70,71,72,77,79],re=[1,8,9,12,13,17,20,22,39,41,44,48,58,66,67,68,69,70,71,72,77,79,84,99,101,102],ue=[13,58,84,99,101,102],z=[13,58,71,72,84,99,101,102],Me=[13,58,66,67,68,69,70,84,99,101,102],_e=[1,98],K=[1,115],Y=[1,107],Q=[1,113],W=[1,108],j=[1,109],X=[1,110],q=[1,111],H=[1,112],J=[1,114],Re=[22,58,59,80,84,85,86,87,88,89],Se=[1,8,9,39,41,44],le=[1,8,9,22],Ge=[1,143],Ue=[1,8,9,59],N=[1,8,9,22,58,59,80,84,85,86,87,88,89],Ne={trace:f(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mermaidDoc:4,statements:5,graphConfig:6,CLASS_DIAGRAM:7,NEWLINE:8,EOF:9,statement:10,classLabel:11,SQS:12,STR:13,SQE:14,namespaceName:15,alphaNumToken:16,DOT:17,className:18,classLiteralName:19,GENERICTYPE:20,relationStatement:21,LABEL:22,namespaceStatement:23,classStatement:24,memberStatement:25,annotationStatement:26,clickStatement:27,styleStatement:28,cssClassStatement:29,noteStatement:30,classDefStatement:31,direction:32,acc_title:33,acc_title_value:34,acc_descr:35,acc_descr_value:36,acc_descr_multiline_value:37,namespaceIdentifier:38,STRUCT_START:39,classStatements:40,STRUCT_STOP:41,NAMESPACE:42,classIdentifier:43,STYLE_SEPARATOR:44,members:45,CLASS:46,ANNOTATION_START:47,ANNOTATION_END:48,MEMBER:49,SEPARATOR:50,relation:51,NOTE_FOR:52,noteText:53,NOTE:54,CLASSDEF:55,classList:56,stylesOpt:57,ALPHA:58,COMMA:59,direction_tb:60,direction_bt:61,direction_rl:62,direction_lr:63,relationType:64,lineType:65,AGGREGATION:66,EXTENSION:67,COMPOSITION:68,DEPENDENCY:69,LOLLIPOP:70,LINE:71,DOTTED_LINE:72,CALLBACK:73,LINK:74,LINK_TARGET:75,CLICK:76,CALLBACK_NAME:77,CALLBACK_ARGS:78,HREF:79,STYLE:80,CSSCLASS:81,style:82,styleComponent:83,NUM:84,COLON:85,UNIT:86,SPACE:87,BRKT:88,PCT:89,commentToken:90,textToken:91,graphCodeTokens:92,textNoTagsToken:93,TAGSTART:94,TAGEND:95,"==":96,"--":97,DEFAULT:98,MINUS:99,keywords:100,UNICODE_TEXT:101,BQUOTE_STR:102,$accept:0,$end:1},terminals_:{2:"error",7:"CLASS_DIAGRAM",8:"NEWLINE",9:"EOF",12:"SQS",13:"STR",14:"SQE",17:"DOT",20:"GENERICTYPE",22:"LABEL",33:"acc_title",34:"acc_title_value",35:"acc_descr",36:"acc_descr_value",37:"acc_descr_multiline_value",39:"STRUCT_START",41:"STRUCT_STOP",42:"NAMESPACE",44:"STYLE_SEPARATOR",46:"CLASS",47:"ANNOTATION_START",48:"ANNOTATION_END",49:"MEMBER",50:"SEPARATOR",52:"NOTE_FOR",54:"NOTE",55:"CLASSDEF",58:"ALPHA",59:"COMMA",60:"direction_tb",61:"direction_bt",62:"direction_rl",63:"direction_lr",66:"AGGREGATION",67:"EXTENSION",68:"COMPOSITION",69:"DEPENDENCY",70:"LOLLIPOP",71:"LINE",72:"DOTTED_LINE",73:"CALLBACK",74:"LINK",75:"LINK_TARGET",76:"CLICK",77:"CALLBACK_NAME",78:"CALLBACK_ARGS",79:"HREF",80:"STYLE",81:"CSSCLASS",84:"NUM",85:"COLON",86:"UNIT",87:"SPACE",88:"BRKT",89:"PCT",92:"graphCodeTokens",94:"TAGSTART",95:"TAGEND",96:"==",97:"--",98:"DEFAULT",99:"MINUS",100:"keywords",101:"UNICODE_TEXT",102:"BQUOTE_STR"},productions_:[0,[3,1],[3,1],[4,1],[6,4],[5,1],[5,2],[5,3],[11,3],[15,1],[15,3],[15,2],[18,1],[18,3],[18,1],[18,2],[18,2],[18,2],[10,1],[10,2],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,1],[10,2],[10,2],[10,1],[23,4],[23,5],[38,2],[40,1],[40,2],[40,3],[24,1],[24,3],[24,4],[24,6],[43,2],[43,3],[26,4],[45,1],[45,2],[25,1],[25,2],[25,1],[25,1],[21,3],[21,4],[21,4],[21,5],[30,3],[30,2],[31,3],[56,1],[56,3],[32,1],[32,1],[32,1],[32,1],[51,3],[51,2],[51,2],[51,1],[64,1],[64,1],[64,1],[64,1],[64,1],[65,1],[65,1],[27,3],[27,4],[27,3],[27,4],[27,4],[27,5],[27,3],[27,4],[27,4],[27,5],[27,4],[27,5],[27,5],[27,6],[28,3],[29,3],[57,1],[57,3],[82,1],[82,2],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[83,1],[90,1],[90,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[91,1],[93,1],[93,1],[93,1],[93,1],[16,1],[16,1],[16,1],[16,1],[19,1],[53,1]],performAction:f(function(c,h,p,n,C,e,Z){var t=e.length-1;switch(C){case 8:this.$=e[t-1];break;case 9:case 12:case 14:this.$=e[t];break;case 10:case 13:this.$=e[t-2]+"."+e[t];break;case 11:case 15:this.$=e[t-1]+e[t];break;case 16:case 17:this.$=e[t-1]+"~"+e[t]+"~";break;case 18:n.addRelation(e[t]);break;case 19:e[t-1].title=n.cleanupLabel(e[t]),n.addRelation(e[t-1]);break;case 30:this.$=e[t].trim(),n.setAccTitle(this.$);break;case 31:case 32:this.$=e[t].trim(),n.setAccDescription(this.$);break;case 33:n.addClassesToNamespace(e[t-3],e[t-1]);break;case 34:n.addClassesToNamespace(e[t-4],e[t-1]);break;case 35:this.$=e[t],n.addNamespace(e[t]);break;case 36:this.$=[e[t]];break;case 37:this.$=[e[t-1]];break;case 38:e[t].unshift(e[t-2]),this.$=e[t];break;case 40:n.setCssClass(e[t-2],e[t]);break;case 41:n.addMembers(e[t-3],e[t-1]);break;case 42:n.setCssClass(e[t-5],e[t-3]),n.addMembers(e[t-5],e[t-1]);break;case 43:this.$=e[t],n.addClass(e[t]);break;case 44:this.$=e[t-1],n.addClass(e[t-1]),n.setClassLabel(e[t-1],e[t]);break;case 45:n.addAnnotation(e[t],e[t-2]);break;case 46:case 59:this.$=[e[t]];break;case 47:e[t].push(e[t-1]),this.$=e[t];break;case 48:break;case 49:n.addMember(e[t-1],n.cleanupLabel(e[t]));break;case 50:break;case 51:break;case 52:this.$={id1:e[t-2],id2:e[t],relation:e[t-1],relationTitle1:"none",relationTitle2:"none"};break;case 53:this.$={id1:e[t-3],id2:e[t],relation:e[t-1],relationTitle1:e[t-2],relationTitle2:"none"};break;case 54:this.$={id1:e[t-3],id2:e[t],relation:e[t-2],relationTitle1:"none",relationTitle2:e[t-1]};break;case 55:this.$={id1:e[t-4],id2:e[t],relation:e[t-2],relationTitle1:e[t-3],relationTitle2:e[t-1]};break;case 56:n.addNote(e[t],e[t-1]);break;case 57:n.addNote(e[t]);break;case 58:this.$=e[t-2],n.defineClass(e[t-1],e[t]);break;case 60:this.$=e[t-2].concat([e[t]]);break;case 61:n.setDirection("TB");break;case 62:n.setDirection("BT");break;case 63:n.setDirection("RL");break;case 64:n.setDirection("LR");break;case 65:this.$={type1:e[t-2],type2:e[t],lineType:e[t-1]};break;case 66:this.$={type1:"none",type2:e[t],lineType:e[t-1]};break;case 67:this.$={type1:e[t-1],type2:"none",lineType:e[t]};break;case 68:this.$={type1:"none",type2:"none",lineType:e[t]};break;case 69:this.$=n.relationType.AGGREGATION;break;case 70:this.$=n.relationType.EXTENSION;break;case 71:this.$=n.relationType.COMPOSITION;break;case 72:this.$=n.relationType.DEPENDENCY;break;case 73:this.$=n.relationType.LOLLIPOP;break;case 74:this.$=n.lineType.LINE;break;case 75:this.$=n.lineType.DOTTED_LINE;break;case 76:case 82:this.$=e[t-2],n.setClickEvent(e[t-1],e[t]);break;case 77:case 83:this.$=e[t-3],n.setClickEvent(e[t-2],e[t-1]),n.setTooltip(e[t-2],e[t]);break;case 78:this.$=e[t-2],n.setLink(e[t-1],e[t]);break;case 79:this.$=e[t-3],n.setLink(e[t-2],e[t-1],e[t]);break;case 80:this.$=e[t-3],n.setLink(e[t-2],e[t-1]),n.setTooltip(e[t-2],e[t]);break;case 81:this.$=e[t-4],n.setLink(e[t-3],e[t-2],e[t]),n.setTooltip(e[t-3],e[t-1]);break;case 84:this.$=e[t-3],n.setClickEvent(e[t-2],e[t-1],e[t]);break;case 85:this.$=e[t-4],n.setClickEvent(e[t-3],e[t-2],e[t-1]),n.setTooltip(e[t-3],e[t]);break;case 86:this.$=e[t-3],n.setLink(e[t-2],e[t]);break;case 87:this.$=e[t-4],n.setLink(e[t-3],e[t-1],e[t]);break;case 88:this.$=e[t-4],n.setLink(e[t-3],e[t-1]),n.setTooltip(e[t-3],e[t]);break;case 89:this.$=e[t-5],n.setLink(e[t-4],e[t-2],e[t]),n.setTooltip(e[t-4],e[t-1]);break;case 90:this.$=e[t-2],n.setCssStyle(e[t-1],e[t]);break;case 91:n.setCssClass(e[t-1],e[t]);break;case 92:this.$=[e[t]];break;case 93:e[t-2].push(e[t]),this.$=e[t-2];break;case 95:this.$=e[t-1]+e[t];break}},"anonymous"),table:[{3:1,4:2,5:3,6:4,7:[1,6],10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:i,35:a,37:u,38:22,42:l,43:23,46:r,47:o,49:A,50:g,52:k,54:L,55:Ae,58:m,60:fe,61:ge,62:Ce,63:me,73:be,74:Ee,76:ye,80:Te,81:ke,84:b,99:E,101:y,102:D},{1:[3]},{1:[2,1]},{1:[2,2]},{1:[2,3]},s(De,[2,5],{8:[1,48]}),{8:[1,49]},s(d,[2,18],{22:[1,50]}),s(d,[2,20]),s(d,[2,21]),s(d,[2,22]),s(d,[2,23]),s(d,[2,24]),s(d,[2,25]),s(d,[2,26]),s(d,[2,27]),s(d,[2,28]),s(d,[2,29]),{34:[1,51]},{36:[1,52]},s(d,[2,32]),s(d,[2,48],{51:53,64:56,65:57,13:[1,54],22:[1,55],66:ee,67:te,68:se,69:ie,70:ae,71:Fe,72:Be}),{39:[1,65]},s(ne,[2,39],{39:[1,67],44:[1,66]}),s(d,[2,50]),s(d,[2,51]),{16:68,58:m,84:b,99:E,101:y},{16:39,18:69,19:40,58:m,84:b,99:E,101:y,102:D},{16:39,18:70,19:40,58:m,84:b,99:E,101:y,102:D},{16:39,18:71,19:40,58:m,84:b,99:E,101:y,102:D},{58:[1,72]},{13:[1,73]},{16:39,18:74,19:40,58:m,84:b,99:E,101:y,102:D},{13:Pe,53:75},{56:77,58:[1,78]},s(d,[2,61]),s(d,[2,62]),s(d,[2,63]),s(d,[2,64]),s(P,[2,12],{16:39,19:40,18:80,17:[1,79],20:[1,81],58:m,84:b,99:E,101:y,102:D}),s(P,[2,14],{20:[1,82]}),{15:83,16:84,58:m,84:b,99:E,101:y},{16:39,18:85,19:40,58:m,84:b,99:E,101:y,102:D},s(re,[2,118]),s(re,[2,119]),s(re,[2,120]),s(re,[2,121]),s([1,8,9,12,13,20,22,39,41,44,66,67,68,69,70,71,72,77,79],[2,122]),s(De,[2,6],{10:5,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,18:21,38:22,43:23,16:39,19:40,5:86,33:i,35:a,37:u,42:l,46:r,47:o,49:A,50:g,52:k,54:L,55:Ae,58:m,60:fe,61:ge,62:Ce,63:me,73:be,74:Ee,76:ye,80:Te,81:ke,84:b,99:E,101:y,102:D}),{5:87,10:5,16:39,18:21,19:40,21:7,23:8,24:9,25:10,26:11,27:12,28:13,29:14,30:15,31:16,32:17,33:i,35:a,37:u,38:22,42:l,43:23,46:r,47:o,49:A,50:g,52:k,54:L,55:Ae,58:m,60:fe,61:ge,62:Ce,63:me,73:be,74:Ee,76:ye,80:Te,81:ke,84:b,99:E,101:y,102:D},s(d,[2,19]),s(d,[2,30]),s(d,[2,31]),{13:[1,89],16:39,18:88,19:40,58:m,84:b,99:E,101:y,102:D},{51:90,64:56,65:57,66:ee,67:te,68:se,69:ie,70:ae,71:Fe,72:Be},s(d,[2,49]),{65:91,71:Fe,72:Be},s(ue,[2,68],{64:92,66:ee,67:te,68:se,69:ie,70:ae}),s(z,[2,69]),s(z,[2,70]),s(z,[2,71]),s(z,[2,72]),s(z,[2,73]),s(Me,[2,74]),s(Me,[2,75]),{8:[1,94],24:95,40:93,43:23,46:r},{16:96,58:m,84:b,99:E,101:y},{45:97,49:_e},{48:[1,99]},{13:[1,100]},{13:[1,101]},{77:[1,102],79:[1,103]},{22:K,57:104,58:Y,80:Q,82:105,83:106,84:W,85:j,86:X,87:q,88:H,89:J},{58:[1,116]},{13:Pe,53:117},s(d,[2,57]),s(d,[2,123]),{22:K,57:118,58:Y,59:[1,119],80:Q,82:105,83:106,84:W,85:j,86:X,87:q,88:H,89:J},s(Re,[2,59]),{16:39,18:120,19:40,58:m,84:b,99:E,101:y,102:D},s(P,[2,15]),s(P,[2,16]),s(P,[2,17]),{39:[2,35]},{15:122,16:84,17:[1,121],39:[2,9],58:m,84:b,99:E,101:y},s(Se,[2,43],{11:123,12:[1,124]}),s(De,[2,7]),{9:[1,125]},s(le,[2,52]),{16:39,18:126,19:40,58:m,84:b,99:E,101:y,102:D},{13:[1,128],16:39,18:127,19:40,58:m,84:b,99:E,101:y,102:D},s(ue,[2,67],{64:129,66:ee,67:te,68:se,69:ie,70:ae}),s(ue,[2,66]),{41:[1,130]},{24:95,40:131,43:23,46:r},{8:[1,132],41:[2,36]},s(ne,[2,40],{39:[1,133]}),{41:[1,134]},{41:[2,46],45:135,49:_e},{16:39,18:136,19:40,58:m,84:b,99:E,101:y,102:D},s(d,[2,76],{13:[1,137]}),s(d,[2,78],{13:[1,139],75:[1,138]}),s(d,[2,82],{13:[1,140],78:[1,141]}),{13:[1,142]},s(d,[2,90],{59:Ge}),s(Ue,[2,92],{83:144,22:K,58:Y,80:Q,84:W,85:j,86:X,87:q,88:H,89:J}),s(N,[2,94]),s(N,[2,96]),s(N,[2,97]),s(N,[2,98]),s(N,[2,99]),s(N,[2,100]),s(N,[2,101]),s(N,[2,102]),s(N,[2,103]),s(N,[2,104]),s(d,[2,91]),s(d,[2,56]),s(d,[2,58],{59:Ge}),{58:[1,145]},s(P,[2,13]),{15:146,16:84,58:m,84:b,99:E,101:y},{39:[2,11]},s(Se,[2,44]),{13:[1,147]},{1:[2,4]},s(le,[2,54]),s(le,[2,53]),{16:39,18:148,19:40,58:m,84:b,99:E,101:y,102:D},s(ue,[2,65]),s(d,[2,33]),{41:[1,149]},{24:95,40:150,41:[2,37],43:23,46:r},{45:151,49:_e},s(ne,[2,41]),{41:[2,47]},s(d,[2,45]),s(d,[2,77]),s(d,[2,79]),s(d,[2,80],{75:[1,152]}),s(d,[2,83]),s(d,[2,84],{13:[1,153]}),s(d,[2,86],{13:[1,155],75:[1,154]}),{22:K,58:Y,80:Q,82:156,83:106,84:W,85:j,86:X,87:q,88:H,89:J},s(N,[2,95]),s(Re,[2,60]),{39:[2,10]},{14:[1,157]},s(le,[2,55]),s(d,[2,34]),{41:[2,38]},{41:[1,158]},s(d,[2,81]),s(d,[2,85]),s(d,[2,87]),s(d,[2,88],{75:[1,159]}),s(Ue,[2,93],{83:144,22:K,58:Y,80:Q,84:W,85:j,86:X,87:q,88:H,89:J}),s(Se,[2,8]),s(ne,[2,42]),s(d,[2,89])],defaultActions:{2:[2,1],3:[2,2],4:[2,3],83:[2,35],122:[2,11],125:[2,4],135:[2,47],146:[2,10],150:[2,38]},parseError:f(function(c,h){if(h.recoverable)this.trace(c);else{var p=new Error(c);throw p.hash=h,p}},"parseError"),parse:f(function(c){var h=this,p=[0],n=[],C=[null],e=[],Z=this.table,t="",oe=0,ze=0,He=2,Ke=1,Je=e.slice.call(arguments,1),T=Object.create(this.lexer),O={yy:{}};for(var Le in this.yy)Object.prototype.hasOwnProperty.call(this.yy,Le)&&(O.yy[Le]=this.yy[Le]);T.setInput(c,O.yy),O.yy.lexer=T,O.yy.parser=this,typeof T.yylloc>"u"&&(T.yylloc={});var xe=T.yylloc;e.push(xe);var Ze=T.options&&T.options.ranges;typeof O.yy.parseError=="function"?this.parseError=O.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;function $e(_){p.length=p.length-2*_,C.length=C.length-_,e.length=e.length-_}f($e,"popStack");function Ye(){var _;return _=n.pop()||T.lex()||Ke,typeof _!="number"&&(_ instanceof Array&&(n=_,_=n.pop()),_=h.symbols_[_]||_),_}f(Ye,"lex");for(var B,w,S,ve,M={},he,x,Qe,de;;){if(w=p[p.length-1],this.defaultActions[w]?S=this.defaultActions[w]:((B===null||typeof B>"u")&&(B=Ye()),S=Z[w]&&Z[w][B]),typeof S>"u"||!S.length||!S[0]){var Ie="";de=[];for(he in Z[w])this.terminals_[he]&&he>He&&de.push("'"+this.terminals_[he]+"'");T.showPosition?Ie="Parse error on line "+(oe+1)+`:
`+T.showPosition()+`
Expecting `+de.join(", ")+", got '"+(this.terminals_[B]||B)+"'":Ie="Parse error on line "+(oe+1)+": Unexpected "+(B==Ke?"end of input":"'"+(this.terminals_[B]||B)+"'"),this.parseError(Ie,{text:T.match,token:this.terminals_[B]||B,line:T.yylineno,loc:xe,expected:de})}if(S[0]instanceof Array&&S.length>1)throw new Error("Parse Error: multiple actions possible at state: "+w+", token: "+B);switch(S[0]){case 1:p.push(B),C.push(T.yytext),e.push(T.yylloc),p.push(S[1]),B=null,ze=T.yyleng,t=T.yytext,oe=T.yylineno,xe=T.yylloc;break;case 2:if(x=this.productions_[S[1]][1],M.$=C[C.length-x],M._$={first_line:e[e.length-(x||1)].first_line,last_line:e[e.length-1].last_line,first_column:e[e.length-(x||1)].first_column,last_column:e[e.length-1].last_column},Ze&&(M._$.range=[e[e.length-(x||1)].range[0],e[e.length-1].range[1]]),ve=this.performAction.apply(M,[t,ze,oe,O.yy,S[1],C,e].concat(Je)),typeof ve<"u")return ve;x&&(p=p.slice(0,-1*x*2),C=C.slice(0,-1*x),e=e.slice(0,-1*x)),p.push(this.productions_[S[1]][0]),C.push(M.$),e.push(M._$),Qe=Z[p[p.length-2]][p[p.length-1]],p.push(Qe);break;case 3:return!0}}return!0},"parse")},qe=function(){var I={EOF:1,parseError:f(function(h,p){if(this.yy.parser)this.yy.parser.parseError(h,p);else throw new Error(h)},"parseError"),setInput:f(function(c,h){return this.yy=h||this.yy||{},this._input=c,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:f(function(){var c=this._input[0];this.yytext+=c,this.yyleng++,this.offset++,this.match+=c,this.matched+=c;var h=c.match(/(?:\r\n?|\n).*/g);return h?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),c},"input"),unput:f(function(c){var h=c.length,p=c.split(/(?:\r\n?|\n)/g);this._input=c+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-h),this.offset-=h;var n=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),p.length-1&&(this.yylineno-=p.length-1);var C=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:p?(p.length===n.length?this.yylloc.first_column:0)+n[n.length-p.length].length-p[0].length:this.yylloc.first_column-h},this.options.ranges&&(this.yylloc.range=[C[0],C[0]+this.yyleng-h]),this.yyleng=this.yytext.length,this},"unput"),more:f(function(){return this._more=!0,this},"more"),reject:f(function(){if(this.options.backtrack_lexer)this._backtrack=!0;else return this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno});return this},"reject"),less:f(function(c){this.unput(this.match.slice(c))},"less"),pastInput:f(function(){var c=this.matched.substr(0,this.matched.length-this.match.length);return(c.length>20?"...":"")+c.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:f(function(){var c=this.match;return c.length<20&&(c+=this._input.substr(0,20-c.length)),(c.substr(0,20)+(c.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:f(function(){var c=this.pastInput(),h=new Array(c.length+1).join("-");return c+this.upcomingInput()+`
`+h+"^"},"showPosition"),test_match:f(function(c,h){var p,n,C;if(this.options.backtrack_lexer&&(C={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(C.yylloc.range=this.yylloc.range.slice(0))),n=c[0].match(/(?:\r\n?|\n).*/g),n&&(this.yylineno+=n.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:n?n[n.length-1].length-n[n.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+c[0].length},this.yytext+=c[0],this.match+=c[0],this.matches=c,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(c[0].length),this.matched+=c[0],p=this.performAction.call(this,this.yy,this,h,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),p)return p;if(this._backtrack){for(var e in C)this[e]=C[e];return!1}return!1},"test_match"),next:f(function(){if(this.done)return this.EOF;this._input||(this.done=!0);var c,h,p,n;this._more||(this.yytext="",this.match="");for(var C=this._currentRules(),e=0;e<C.length;e++)if(p=this._input.match(this.rules[C[e]]),p&&(!h||p[0].length>h[0].length)){if(h=p,n=e,this.options.backtrack_lexer){if(c=this.test_match(p,C[e]),c!==!1)return c;if(this._backtrack){h=!1;continue}else return!1}else if(!this.options.flex)break}return h?(c=this.test_match(h,C[n]),c!==!1?c:!1):this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:f(function(){var h=this.next();return h||this.lex()},"lex"),begin:f(function(h){this.conditionStack.push(h)},"begin"),popState:f(function(){var h=this.conditionStack.length-1;return h>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:f(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:f(function(h){return h=this.conditionStack.length-1-Math.abs(h||0),h>=0?this.conditionStack[h]:"INITIAL"},"topState"),pushState:f(function(h){this.begin(h)},"pushState"),stateStackSize:f(function(){return this.conditionStack.length},"stateStackSize"),options:{},performAction:f(function(h,p,n,C){switch(n){case 0:return 60;case 1:return 61;case 2:return 62;case 3:return 63;case 4:break;case 5:break;case 6:return this.begin("acc_title"),33;case 7:return this.popState(),"acc_title_value";case 8:return this.begin("acc_descr"),35;case 9:return this.popState(),"acc_descr_value";case 10:this.begin("acc_descr_multiline");break;case 11:this.popState();break;case 12:return"acc_descr_multiline_value";case 13:return 8;case 14:break;case 15:return 7;case 16:return 7;case 17:return"EDGE_STATE";case 18:this.begin("callback_name");break;case 19:this.popState();break;case 20:this.popState(),this.begin("callback_args");break;case 21:return 77;case 22:this.popState();break;case 23:return 78;case 24:this.popState();break;case 25:return"STR";case 26:this.begin("string");break;case 27:return 80;case 28:return 55;case 29:return this.begin("namespace"),42;case 30:return this.popState(),8;case 31:break;case 32:return this.begin("namespace-body"),39;case 33:return this.popState(),41;case 34:return"EOF_IN_STRUCT";case 35:return 8;case 36:break;case 37:return"EDGE_STATE";case 38:return this.begin("class"),46;case 39:return this.popState(),8;case 40:break;case 41:return this.popState(),this.popState(),41;case 42:return this.begin("class-body"),39;case 43:return this.popState(),41;case 44:return"EOF_IN_STRUCT";case 45:return"EDGE_STATE";case 46:return"OPEN_IN_STRUCT";case 47:break;case 48:return"MEMBER";case 49:return 81;case 50:return 73;case 51:return 74;case 52:return 76;case 53:return 52;case 54:return 54;case 55:return 47;case 56:return 48;case 57:return 79;case 58:this.popState();break;case 59:return"GENERICTYPE";case 60:this.begin("generic");break;case 61:this.popState();break;case 62:return"BQUOTE_STR";case 63:this.begin("bqstring");break;case 64:return 75;case 65:return 75;case 66:return 75;case 67:return 75;case 68:return 67;case 69:return 67;case 70:return 69;case 71:return 69;case 72:return 68;case 73:return 66;case 74:return 70;case 75:return 71;case 76:return 72;case 77:return 22;case 78:return 44;case 79:return 99;case 80:return 17;case 81:return"PLUS";case 82:return 85;case 83:return 59;case 84:return 88;case 85:return 88;case 86:return 89;case 87:return"EQUALS";case 88:return"EQUALS";case 89:return 58;case 90:return 12;case 91:return 14;case 92:return"PUNCTUATION";case 93:return 84;case 94:return 101;case 95:return 87;case 96:return 87;case 97:return 9}},"anonymous"),rules:[/^(?:.*direction\s+TB[^\n]*)/,/^(?:.*direction\s+BT[^\n]*)/,/^(?:.*direction\s+RL[^\n]*)/,/^(?:.*direction\s+LR[^\n]*)/,/^(?:%%(?!\{)*[^\n]*(\r?\n?)+)/,/^(?:%%[^\n]*(\r?\n)*)/,/^(?:accTitle\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*:\s*)/,/^(?:(?!\n||)*[^\n]*)/,/^(?:accDescr\s*\{\s*)/,/^(?:[\}])/,/^(?:[^\}]*)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:classDiagram-v2\b)/,/^(?:classDiagram\b)/,/^(?:\[\*\])/,/^(?:call[\s]+)/,/^(?:\([\s]*\))/,/^(?:\()/,/^(?:[^(]*)/,/^(?:\))/,/^(?:[^)]*)/,/^(?:["])/,/^(?:[^"]*)/,/^(?:["])/,/^(?:style\b)/,/^(?:classDef\b)/,/^(?:namespace\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:\[\*\])/,/^(?:class\b)/,/^(?:\s*(\r?\n)+)/,/^(?:\s+)/,/^(?:[}])/,/^(?:[{])/,/^(?:[}])/,/^(?:$)/,/^(?:\[\*\])/,/^(?:[{])/,/^(?:[\n])/,/^(?:[^{}\n]*)/,/^(?:cssClass\b)/,/^(?:callback\b)/,/^(?:link\b)/,/^(?:click\b)/,/^(?:note for\b)/,/^(?:note\b)/,/^(?:<<)/,/^(?:>>)/,/^(?:href\b)/,/^(?:[~])/,/^(?:[^~]*)/,/^(?:~)/,/^(?:[`])/,/^(?:[^`]+)/,/^(?:[`])/,/^(?:_self\b)/,/^(?:_blank\b)/,/^(?:_parent\b)/,/^(?:_top\b)/,/^(?:\s*<\|)/,/^(?:\s*\|>)/,/^(?:\s*>)/,/^(?:\s*<)/,/^(?:\s*\*)/,/^(?:\s*o\b)/,/^(?:\s*\(\))/,/^(?:--)/,/^(?:\.\.)/,/^(?::{1}[^:\n;]+)/,/^(?::{3})/,/^(?:-)/,/^(?:\.)/,/^(?:\+)/,/^(?::)/,/^(?:,)/,/^(?:#)/,/^(?:#)/,/^(?:%)/,/^(?:=)/,/^(?:=)/,/^(?:\w+)/,/^(?:\[)/,/^(?:\])/,/^(?:[!"#$%&'*+,-.`?\\/])/,/^(?:[0-9]+)/,/^(?:[\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6]|[\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377]|[\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5]|[\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA]|[\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE]|[\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA]|[\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0]|[\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977]|[\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2]|[\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A]|[\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39]|[\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8]|[\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C]|[\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C]|[\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99]|[\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0]|[\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D]|[\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3]|[\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10]|[\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1]|[\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81]|[\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3]|[\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6]|[\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A]|[\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081]|[\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D]|[\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0]|[\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310]|[\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C]|[\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711]|[\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7]|[\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C]|[\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16]|[\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF]|[\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC]|[\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D]|[\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D]|[\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3]|[\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F]|[\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128]|[\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184]|[\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3]|[\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6]|[\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE]|[\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C]|[\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D]|[\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC]|[\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B]|[\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788]|[\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805]|[\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB]|[\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28]|[\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5]|[\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4]|[\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|[\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D]|[\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36]|[\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D]|[\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC]|[\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF]|[\uFFD2-\uFFD7\uFFDA-\uFFDC])/,/^(?:\s)/,/^(?:\s)/,/^(?:$)/],conditions:{"namespace-body":{rules:[26,33,34,35,36,37,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},namespace:{rules:[26,29,30,31,32,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},"class-body":{rules:[26,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},class:{rules:[26,39,40,41,42,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr_multiline:{rules:[11,12,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_descr:{rules:[9,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},acc_title:{rules:[7,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_args:{rules:[22,23,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},callback_name:{rules:[19,20,21,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},href:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},struct:{rules:[26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},generic:{rules:[26,49,50,51,52,53,54,55,56,57,58,59,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},bqstring:{rules:[26,49,50,51,52,53,54,55,56,57,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},string:{rules:[24,25,26,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,86,87,88,89,90,91,92,93,94,95,97],inclusive:!1},INITIAL:{rules:[0,1,2,3,4,5,6,8,10,13,14,15,16,17,18,26,27,28,29,38,49,50,51,52,53,54,55,56,57,60,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97],inclusive:!0}}};return I}();Ne.lexer=qe;function ce(){this.yy={}}return f(ce,"Parser"),ce.prototype=Ne,Ne.Parser=ce,new ce}();Ve.parser=Ve;var bt=Ve,We=["#","+","~","-",""],G,je=(G=class{constructor(i,a){this.memberType=a,this.visibility="",this.classifier="",this.text="";const u=dt(i,F());this.parseMember(u)}getDisplayDetails(){let i=this.visibility+R(this.id);this.memberType==="method"&&(i+=`(${R(this.parameters.trim())})`,this.returnType&&(i+=" : "+R(this.returnType))),i=i.trim();const a=this.parseClassifier();return{displayText:i,cssStyle:a}}parseMember(i){let a="";if(this.memberType==="method"){const r=/([#+~-])?(.+)\((.*)\)([\s$*])?(.*)([$*])?/.exec(i);if(r){const o=r[1]?r[1].trim():"";if(We.includes(o)&&(this.visibility=o),this.id=r[2],this.parameters=r[3]?r[3].trim():"",a=r[4]?r[4].trim():"",this.returnType=r[5]?r[5].trim():"",a===""){const A=this.returnType.substring(this.returnType.length-1);/[$*]/.exec(A)&&(a=A,this.returnType=this.returnType.substring(0,this.returnType.length-1))}}}else{const l=i.length,r=i.substring(0,1),o=i.substring(l-1);We.includes(r)&&(this.visibility=r),/[$*]/.exec(o)&&(a=o),this.id=i.substring(this.visibility===""?0:1,a===""?l:l-1)}this.classifier=a,this.id=this.id.startsWith(" ")?" "+this.id.trim():this.id.trim();const u=`${this.visibility?"\\"+this.visibility:""}${R(this.id)}${this.memberType==="method"?`(${R(this.parameters)})${this.returnType?" : "+R(this.returnType):""}`:""}`;this.text=u.replaceAll("<","&lt;").replaceAll(">","&gt;"),this.text.startsWith("\\&lt;")&&(this.text=this.text.replace("\\&lt;","~"))}parseClassifier(){switch(this.classifier){case"*":return"font-style:italic;";case"$":return"text-decoration:underline;";default:return""}}},f(G,"ClassMember"),G),pe="classId-",Xe=0,V=f(s=>v.sanitizeText(s,F()),"sanitizeText"),U,Et=(U=class{constructor(){this.relations=[],this.classes=new Map,this.styleClasses=new Map,this.notes=[],this.interfaces=[],this.namespaces=new Map,this.namespaceCounter=0,this.functions=[],this.lineType={LINE:0,DOTTED_LINE:1},this.relationType={AGGREGATION:0,EXTENSION:1,COMPOSITION:2,DEPENDENCY:3,LOLLIPOP:4},this.setupToolTips=f(i=>{let a=$(".mermaidTooltip");(a._groups||a)[0][0]===null&&(a=$("body").append("div").attr("class","mermaidTooltip").style("opacity",0)),$(i).select("svg").selectAll("g.node").on("mouseover",r=>{const o=$(r.currentTarget);if(o.attr("title")===null)return;const g=this.getBoundingClientRect();a.transition().duration(200).style("opacity",".9"),a.text(o.attr("title")).style("left",window.scrollX+g.left+(g.right-g.left)/2+"px").style("top",window.scrollY+g.top-14+document.body.scrollTop+"px"),a.html(a.html().replace(/&lt;br\/&gt;/g,"<br/>")),o.classed("hover",!0)}).on("mouseout",r=>{a.transition().duration(500).style("opacity",0),$(r.currentTarget).classed("hover",!1)})},"setupToolTips"),this.direction="TB",this.setAccTitle=at,this.getAccTitle=nt,this.setAccDescription=rt,this.getAccDescription=ut,this.setDiagramTitle=lt,this.getDiagramTitle=ct,this.getConfig=f(()=>F().class,"getConfig"),this.functions.push(this.setupToolTips.bind(this)),this.clear(),this.addRelation=this.addRelation.bind(this),this.addClassesToNamespace=this.addClassesToNamespace.bind(this),this.addNamespace=this.addNamespace.bind(this),this.setCssClass=this.setCssClass.bind(this),this.addMembers=this.addMembers.bind(this),this.addClass=this.addClass.bind(this),this.setClassLabel=this.setClassLabel.bind(this),this.addAnnotation=this.addAnnotation.bind(this),this.addMember=this.addMember.bind(this),this.cleanupLabel=this.cleanupLabel.bind(this),this.addNote=this.addNote.bind(this),this.defineClass=this.defineClass.bind(this),this.setDirection=this.setDirection.bind(this),this.setLink=this.setLink.bind(this),this.bindFunctions=this.bindFunctions.bind(this),this.clear=this.clear.bind(this),this.setTooltip=this.setTooltip.bind(this),this.setClickEvent=this.setClickEvent.bind(this),this.setCssStyle=this.setCssStyle.bind(this)}splitClassNameAndType(i){const a=v.sanitizeText(i,F());let u="",l=a;if(a.indexOf("~")>0){const r=a.split("~");l=V(r[0]),u=V(r[1])}return{className:l,type:u}}setClassLabel(i,a){const u=v.sanitizeText(i,F());a&&(a=V(a));const{className:l}=this.splitClassNameAndType(u);this.classes.get(l).label=a,this.classes.get(l).text=`${a}${this.classes.get(l).type?`<${this.classes.get(l).type}>`:""}`}addClass(i){const a=v.sanitizeText(i,F()),{className:u,type:l}=this.splitClassNameAndType(a);if(this.classes.has(u))return;const r=v.sanitizeText(u,F());this.classes.set(r,{id:r,type:l,label:r,text:`${r}${l?`&lt;${l}&gt;`:""}`,shape:"classBox",cssClasses:"default",methods:[],members:[],annotations:[],styles:[],domId:pe+r+"-"+Xe}),Xe++}addInterface(i,a){const u={id:`interface${this.interfaces.length}`,label:i,classId:a};this.interfaces.push(u)}lookUpDomId(i){const a=v.sanitizeText(i,F());if(this.classes.has(a))return this.classes.get(a).domId;throw new Error("Class not found: "+a)}clear(){this.relations=[],this.classes=new Map,this.notes=[],this.interfaces=[],this.functions=[],this.functions.push(this.setupToolTips.bind(this)),this.namespaces=new Map,this.namespaceCounter=0,this.direction="TB",ot()}getClass(i){return this.classes.get(i)}getClasses(){return this.classes}getRelations(){return this.relations}getNotes(){return this.notes}addRelation(i){Oe.debug("Adding relation: "+JSON.stringify(i));const a=[this.relationType.LOLLIPOP,this.relationType.AGGREGATION,this.relationType.COMPOSITION,this.relationType.DEPENDENCY,this.relationType.EXTENSION];i.relation.type1===this.relationType.LOLLIPOP&&!a.includes(i.relation.type2)?(this.addClass(i.id2),this.addInterface(i.id1,i.id2),i.id1=`interface${this.interfaces.length-1}`):i.relation.type2===this.relationType.LOLLIPOP&&!a.includes(i.relation.type1)?(this.addClass(i.id1),this.addInterface(i.id2,i.id1),i.id2=`interface${this.interfaces.length-1}`):(this.addClass(i.id1),this.addClass(i.id2)),i.id1=this.splitClassNameAndType(i.id1).className,i.id2=this.splitClassNameAndType(i.id2).className,i.relationTitle1=v.sanitizeText(i.relationTitle1.trim(),F()),i.relationTitle2=v.sanitizeText(i.relationTitle2.trim(),F()),this.relations.push(i)}addAnnotation(i,a){const u=this.splitClassNameAndType(i).className;this.classes.get(u).annotations.push(a)}addMember(i,a){this.addClass(i);const u=this.splitClassNameAndType(i).className,l=this.classes.get(u);if(typeof a=="string"){const r=a.trim();r.startsWith("<<")&&r.endsWith(">>")?l.annotations.push(V(r.substring(2,r.length-2))):r.indexOf(")")>0?l.methods.push(new je(r,"method")):r&&l.members.push(new je(r,"attribute"))}}addMembers(i,a){Array.isArray(a)&&(a.reverse(),a.forEach(u=>this.addMember(i,u)))}addNote(i,a){const u={id:`note${this.notes.length}`,class:a,text:i};this.notes.push(u)}cleanupLabel(i){return i.startsWith(":")&&(i=i.substring(1)),V(i.trim())}setCssClass(i,a){i.split(",").forEach(u=>{let l=u;/\d/.exec(u[0])&&(l=pe+l);const r=this.classes.get(l);r&&(r.cssClasses+=" "+a)})}defineClass(i,a){for(const u of i){let l=this.styleClasses.get(u);l===void 0&&(l={id:u,styles:[],textStyles:[]},this.styleClasses.set(u,l)),a&&a.forEach(r=>{if(/color/.exec(r)){const o=r.replace("fill","bgFill");l.textStyles.push(o)}l.styles.push(r)}),this.classes.forEach(r=>{r.cssClasses.includes(u)&&r.styles.push(...a.flatMap(o=>o.split(",")))})}}setTooltip(i,a){i.split(",").forEach(u=>{a!==void 0&&(this.classes.get(u).tooltip=V(a))})}getTooltip(i,a){return a&&this.namespaces.has(a)?this.namespaces.get(a).classes.get(i).tooltip:this.classes.get(i).tooltip}setLink(i,a,u){const l=F();i.split(",").forEach(r=>{let o=r;/\d/.exec(r[0])&&(o=pe+o);const A=this.classes.get(o);A&&(A.link=we.formatUrl(a,l),l.securityLevel==="sandbox"?A.linkTarget="_top":typeof u=="string"?A.linkTarget=V(u):A.linkTarget="_blank")}),this.setCssClass(i,"clickable")}setClickEvent(i,a,u){i.split(",").forEach(l=>{this.setClickFunc(l,a,u),this.classes.get(l).haveCallback=!0}),this.setCssClass(i,"clickable")}setClickFunc(i,a,u){const l=v.sanitizeText(i,F());if(F().securityLevel!=="loose"||a===void 0)return;const o=l;if(this.classes.has(o)){const A=this.lookUpDomId(o);let g=[];if(typeof u=="string"){g=u.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);for(let k=0;k<g.length;k++){let L=g[k].trim();L.startsWith('"')&&L.endsWith('"')&&(L=L.substr(1,L.length-2)),g[k]=L}}g.length===0&&g.push(A),this.functions.push(()=>{const k=document.querySelector(`[id="${A}"]`);k!==null&&k.addEventListener("click",()=>{we.runFunc(a,...g)},!1)})}}bindFunctions(i){this.functions.forEach(a=>{a(i)})}getDirection(){return this.direction}setDirection(i){this.direction=i}addNamespace(i){this.namespaces.has(i)||(this.namespaces.set(i,{id:i,classes:new Map,children:{},domId:pe+i+"-"+this.namespaceCounter}),this.namespaceCounter++)}getNamespace(i){return this.namespaces.get(i)}getNamespaces(){return this.namespaces}addClassesToNamespace(i,a){if(this.namespaces.has(i))for(const u of a){const{className:l}=this.splitClassNameAndType(u);this.classes.get(l).parent=i,this.namespaces.get(i).classes.set(l,this.classes.get(l))}}setCssStyle(i,a){const u=this.classes.get(i);if(!(!a||!u))for(const l of a)l.includes(",")?u.styles.push(...l.split(",")):u.styles.push(l)}getArrowMarker(i){let a;switch(i){case 0:a="aggregation";break;case 1:a="extension";break;case 2:a="composition";break;case 3:a="dependency";break;case 4:a="lollipop";break;default:a="none"}return a}getData(){var r;const i=[],a=[],u=F();for(const o of this.namespaces.keys()){const A=this.namespaces.get(o);if(A){const g={id:A.id,label:A.id,isGroup:!0,padding:u.class.padding??16,shape:"rect",cssStyles:["fill: none","stroke: black"],look:u.look};i.push(g)}}for(const o of this.classes.keys()){const A=this.classes.get(o);if(A){const g=A;g.parentId=A.parent,g.look=u.look,i.push(g)}}let l=0;for(const o of this.notes){l++;const A={id:o.id,label:o.text,isGroup:!1,shape:"note",padding:u.class.padding??6,cssStyles:["text-align: left","white-space: nowrap",`fill: ${u.themeVariables.noteBkgColor}`,`stroke: ${u.themeVariables.noteBorderColor}`],look:u.look};i.push(A);const g=((r=this.classes.get(o.class))==null?void 0:r.id)??"";if(g){const k={id:`edgeNote${l}`,start:o.id,end:g,type:"normal",thickness:"normal",classes:"relation",arrowTypeStart:"none",arrowTypeEnd:"none",arrowheadStyle:"",labelStyle:[""],style:["fill: none"],pattern:"dotted",look:u.look};a.push(k)}}for(const o of this.interfaces){const A={id:o.id,label:o.label,isGroup:!1,shape:"rect",cssStyles:["opacity: 0;"],look:u.look};i.push(A)}l=0;for(const o of this.relations){l++;const A={id:ht(o.id1,o.id2,{prefix:"id",counter:l}),start:o.id1,end:o.id2,type:"normal",label:o.title,labelpos:"c",thickness:"normal",classes:"relation",arrowTypeStart:this.getArrowMarker(o.relation.type1),arrowTypeEnd:this.getArrowMarker(o.relation.type2),startLabelRight:o.relationTitle1==="none"?"":o.relationTitle1,endLabelLeft:o.relationTitle2==="none"?"":o.relationTitle2,arrowheadStyle:"",labelStyle:["display: inline-block"],style:o.style||"",pattern:o.relation.lineType==1?"dashed":"solid",look:u.look};a.push(A)}return{nodes:i,edges:a,other:{},config:u,direction:this.getDirection()}}},f(U,"ClassDB"),U),pt=f(s=>`g.classGroup text {
  fill: ${s.nodeBorder||s.classText};
  stroke: none;
  font-family: ${s.fontFamily};
  font-size: 10px;

  .title {
    font-weight: bolder;
  }

}

.nodeLabel, .edgeLabel {
  color: ${s.classText};
}
.edgeLabel .label rect {
  fill: ${s.mainBkg};
}
.label text {
  fill: ${s.classText};
}

.labelBkg {
  background: ${s.mainBkg};
}
.edgeLabel .label span {
  background: ${s.mainBkg};
}

.classTitle {
  font-weight: bolder;
}
.node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${s.mainBkg};
    stroke: ${s.nodeBorder};
    stroke-width: 1px;
  }


.divider {
  stroke: ${s.nodeBorder};
  stroke-width: 1;
}

g.clickable {
  cursor: pointer;
}

g.classGroup rect {
  fill: ${s.mainBkg};
  stroke: ${s.nodeBorder};
}

g.classGroup line {
  stroke: ${s.nodeBorder};
  stroke-width: 1;
}

.classLabel .box {
  stroke: none;
  stroke-width: 0;
  fill: ${s.mainBkg};
  opacity: 0.5;
}

.classLabel .label {
  fill: ${s.nodeBorder};
  font-size: 10px;
}

.relation {
  stroke: ${s.lineColor};
  stroke-width: 1;
  fill: none;
}

.dashed-line{
  stroke-dasharray: 3;
}

.dotted-line{
  stroke-dasharray: 1 2;
}

#compositionStart, .composition {
  fill: ${s.lineColor} !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#compositionEnd, .composition {
  fill: ${s.lineColor} !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${s.lineColor} !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#dependencyStart, .dependency {
  fill: ${s.lineColor} !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#extensionStart, .extension {
  fill: transparent !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#extensionEnd, .extension {
  fill: transparent !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#aggregationStart, .aggregation {
  fill: transparent !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#aggregationEnd, .aggregation {
  fill: transparent !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#lollipopStart, .lollipop {
  fill: ${s.mainBkg} !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

#lollipopEnd, .lollipop {
  fill: ${s.mainBkg} !important;
  stroke: ${s.lineColor} !important;
  stroke-width: 1;
}

.edgeTerminals {
  font-size: 11px;
  line-height: initial;
}

.classTitleText {
  text-anchor: middle;
  font-size: 18px;
  fill: ${s.textColor};
}
`,"getStyles"),yt=pt,At=f((s,i="TB")=>{if(!s.doc)return i;let a=i;for(const u of s.doc)u.stmt==="dir"&&(a=u.value);return a},"getDir"),ft=f(function(s,i){return i.db.getClasses()},"getClasses"),gt=f(async function(s,i,a,u){Oe.info("REF0:"),Oe.info("Drawing class diagram (v3)",i);const{securityLevel:l,state:r,layout:o}=F(),A=u.db.getData(),g=et(i,l);A.type=u.type,A.layoutAlgorithm=st(o),A.nodeSpacing=(r==null?void 0:r.nodeSpacing)||50,A.rankSpacing=(r==null?void 0:r.rankSpacing)||50,A.markers=["aggregation","extension","composition","dependency","lollipop"],A.diagramId=i,await it(A,g);const k=8;we.insertTitle(g,"classDiagramTitleText",(r==null?void 0:r.titleTopMargin)??25,u.db.getDiagramTitle()),tt(g,k,"classDiagram",(r==null?void 0:r.useMaxWidth)??!0)},"draw"),Tt={getClasses:ft,draw:gt,getDir:At};export{Et as C,bt as a,Tt as c,yt as s};
