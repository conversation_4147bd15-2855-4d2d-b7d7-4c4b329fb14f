import { useState, useEffect } from 'react';
import Sidebar from './components/Sidebar';
import Topbar from './components/Topbar';
import Dashboard from './components/Dashboard';
import Settings from './components/Settings';
import Debug from './components/Debug';
// 🚀 SUPPRIMÉ: import Onboarding - remplacé par UserRegistration
import ImageGen from './components/ImageGen';
import Gallery from './components/Gallery';
import Help from './components/Help';
import Servers from './components/Servers';
import Lumaui from './components/Lumaui';
import LumaUILite from './components/LumaUILite';

import DocumentManager from './components/DocumentManager';
import UserRegistration from './components/UserRegistration';
import UserMonitoring from './components/UserMonitoring';
import InferenceDashboard from './components/InferenceDashboard';
import UpdateManager from './components/UpdateManager';
import { db } from './db';
import { InterpreterProvider } from './contexts/InterpreterContext';
import { ProvidersProvider } from './contexts/ProvidersContext';
import ClaraAssistant from './components/ClaraAssistant';


interface UserInfo {
  firstName: string;
  lastName: string;
  pole: string;
  pcName: string;
  ipAddress: string;
  registeredAt: string;
}

function App() {
  const [activePage, setActivePage] = useState(() => localStorage.getItem('activePage') || 'dashboard');
  // 🚀 SUPPRIMÉ: showOnboarding et userInfo - remplacés par wemaUserInfo
  const [alphaFeaturesEnabled, setAlphaFeaturesEnabled] = useState(false);
  const [wemaUserInfo, setWemaUserInfo] = useState<UserInfo | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);
  const [showUserRegistration, setShowUserRegistration] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    // 🚀 SUPPRIMÉ: Ancien système Clara onboarding
    // Maintenant on utilise uniquement le système WeMa IA UserRegistration
    // setShowOnboarding(false); // SUPPRIMÉ - plus nécessaire

    // Vérifier l'enregistrement WeMa IA
    const checkWemaUserInfo = () => {
      const savedWemaUserInfo = localStorage.getItem('wema_user_info');

      // Vérifier si c'est l'interface admin
      const isAdminMode = window.location.hostname === '*************' ||
                         window.location.search.includes('admin=true') ||
                         localStorage.getItem('wema_admin_mode') === 'true';

      setIsAdmin(isAdminMode);

      if (savedWemaUserInfo && !isAdminMode) {
        try {
          const parsed = JSON.parse(savedWemaUserInfo);
          setWemaUserInfo(parsed);
        } catch (error) {
          console.error('Erreur parsing wema user info:', error);
          localStorage.removeItem('wema_user_info');
          setShowUserRegistration(true);
        }
      } else if (!isAdminMode) {
        setShowUserRegistration(true);
      }
    };

    checkWemaUserInfo();

    // Add db to window for debugging in development
    if (import.meta.env.DEV) {
      (window as typeof window & { db: typeof db }).db = db;
    }
  }, []);

  useEffect(() => {
    db.getAlphaFeaturesEnabled?.().then(val => setAlphaFeaturesEnabled(!!val));
  }, []);

  useEffect(() => {
    // Ouvrir les paramètres comme modal si la page est 'settings'
    if (activePage === 'settings') {
      setShowSettings(true);
      setActivePage('dashboard'); // Retourner au dashboard
    }
  }, [activePage]);

  // 🚀 SUPPRIMÉ: handleOnboardingComplete - plus nécessaire

  const handleWemaRegistrationComplete = (newUserInfo: UserInfo) => {
    setWemaUserInfo(newUserInfo);
    setShowUserRegistration(false);
  };

  const handleWemaLogout = () => {
    localStorage.removeItem('wema_user_info');
    setWemaUserInfo(null);
    setShowUserRegistration(true);
  };

  useEffect(() => {
    console.log('Storing activePage:', activePage);
    localStorage.setItem('activePage', activePage);

    // 🚀 PRÉCHARGEMENT AUTOMATIQUE QUAND ON NAVIGUE VERS CLARA
    if (activePage === 'clara') {
      console.log('🎯 Navigating to Clara - triggering model preload...');

      // Déclencher le préchargement via un événement personnalisé
      // que ClaraAssistant peut écouter
      const preloadEvent = new CustomEvent('clara-navigation-preload', {
        detail: { timestamp: Date.now() }
      });
      window.dispatchEvent(preloadEvent);
    }
  }, [activePage]);

  const renderContent = () => {
    if (activePage === 'assistant') {
      return <ClaraAssistant onPageChange={setActivePage} />;
    }
    
    // Clara is now always mounted but conditionally visible
    // This allows it to run in the background
    
    if (activePage === 'agents') {
      return <AgentStudio onPageChange={setActivePage} userName={wemaUserInfo?.firstName} />;
    }





    if (activePage === 'image-gen') {
      return <ImageGen onPageChange={setActivePage} />;
    }

    if (activePage === 'gallery') {
      return <Gallery onPageChange={setActivePage} />;
    }

    if (activePage === 'n8n') {
      return <N8N onPageChange={setActivePage} />;
    }
    
    if (activePage === 'servers') {
      return <Servers onPageChange={setActivePage} />;
    }

    return (
      <div className="flex h-screen">
        <Sidebar activePage={activePage} onPageChange={setActivePage} alphaFeaturesEnabled={alphaFeaturesEnabled} />
        
        <div className="flex-1 flex flex-col">
          {/* <Topbar userName={`${wemaUserInfo?.firstName} ${wemaUserInfo?.lastName}`} onPageChange={setActivePage} /> */}
          
          <main className="flex-1 p-6 overflow-auto">
            {(() => {
              switch (activePage) {
                case 'debug':
                  return <Debug />;

                case 'help':
                  return <Help />;
                case 'document-manager':
                  return <DocumentManager onPageChange={setActivePage} />;
                case 'lumaui':
                  return <Lumaui onPageChange={setActivePage} />;
                case 'lumaui-lite':
                  return <LumaUILite />;
                case 'user-monitoring':
                  return <UserMonitoring />;
                case 'inference-dashboard':
                  return <InferenceDashboard />;
                case 'update-manager':
                  return <UpdateManager />;
                case 'dashboard':
                default:
                  return <Dashboard onPageChange={setActivePage} />;
              }
            })()}
          </main>
        </div>

        {/* Modal des paramètres */}
        {showSettings && (
          <Settings
            alphaFeaturesEnabled={alphaFeaturesEnabled}
            setAlphaFeaturesEnabled={setAlphaFeaturesEnabled}
            onClose={() => setShowSettings(false)}
          />
        )}
      </div>
    );
  };

  // Interface admin complète
  if (isAdmin) {
    return (
      <ProvidersProvider>
        <InterpreterProvider onPageChange={setActivePage}>
          <div className="min-h-screen bg-gray-50">
            <div className="flex h-screen">
              <Sidebar activePage={activePage} onPageChange={setActivePage} alphaFeaturesEnabled={alphaFeaturesEnabled} />

              <div className="flex-1 flex flex-col">
                {/* <Topbar userName="Admin" onPageChange={setActivePage} /> */}

                <main className="flex-1 p-6 overflow-auto">
                  {(() => {
                    switch (activePage) {
                      case 'user-monitoring':
                        return <UserMonitoring />;
                      case 'inference-dashboard':
                        return <InferenceDashboard />;
                      case 'update-manager':
                        return <UpdateManager />;
                      case 'debug':
                        return <Debug />;
                      case 'help':
                        return <Help />;
                      case 'document-manager':
                        return <DocumentManager onPageChange={setActivePage} />;
                      case 'clara':
                        return <ClaraAssistant onPageChange={setActivePage} />;
                      case 'dashboard':
                      default:
                        return <Dashboard onPageChange={setActivePage} />;
                    }
                  })()}
                </main>
              </div>
            </div>
          </div>

          {/* Modal des paramètres */}
          {showSettings && (
            <Settings
              alphaFeaturesEnabled={alphaFeaturesEnabled}
              setAlphaFeaturesEnabled={setAlphaFeaturesEnabled}
              onClose={() => setShowSettings(false)}
            />
          )}
        </InterpreterProvider>
      </ProvidersProvider>
    );
  }

  // Enregistrement utilisateur requis
  if (showUserRegistration) {
    return (
      <ProvidersProvider>
        <InterpreterProvider onPageChange={setActivePage}>
          <UserRegistration onRegistrationComplete={handleWemaRegistrationComplete} />
        </InterpreterProvider>
      </ProvidersProvider>
    );
  }

  // Interface utilisateur normale
  return (
    <ProvidersProvider>
      <InterpreterProvider onPageChange={setActivePage}>
        <div className="min-h-screen bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
          {/* Always render WeMa IA in background - visible when activePage is 'clara' */}
          <div className={activePage === 'clara' ? 'block' : 'hidden'} data-clara-container>
            <ClaraAssistant
              onPageChange={setActivePage}
              userInfo={wemaUserInfo}
              onLogout={handleWemaLogout}
            />
          </div>

          {/* Render other content when not on WeMa IA page */}
          {activePage !== 'clara' && renderContent()}
        </div>
      </InterpreterProvider>
    </ProvidersProvider>
  );
}

export default App;


