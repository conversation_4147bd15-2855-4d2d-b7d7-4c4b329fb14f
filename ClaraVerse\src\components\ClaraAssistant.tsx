import React, { useState, useEffect, useCallback } from 'react';
import { logger, LogCategory } from '../utils/logger';
import { Database } from 'lucide-react';

import ClaraSidebar from './Clara_Components/ClaraSidebar';
import ClaraAssistantInput from './Clara_Components/clara_assistant_input';
import ClaraChatWindow from './Clara_Components/clara_assistant_chat_window';
import ContextViewer from './Clara_Components/ContextViewer';
import DraggableAdvancedOptions from './Clara_Components/DraggableAdvancedOptions';
import Sidebar from './Sidebar';
import { db } from '../db';
import { claraDB } from '../db/claraDatabase';

// Import Clara types and API service
import {
  ClaraMessage,
  ClaraFileAttachment,
  ClaraSessionConfig,
  ClaraChatSession,
  ClaraProvider,
  ClaraModel,
  ClaraAIConfig
} from '../types/clara_assistant_types';
// 🚀 CORRECTION : Import direct du nouveau service (éviter le proxy)
import { claraApiService } from '../services/api/ClaraApiService';
import { saveProviderConfig, loadProviderConfig, cleanInvalidProviderConfigs } from '../utils/providerConfigStorage';
import { claraMCPService } from '../services/claraMCPService';
import { addCompletionNotification, addBackgroundCompletionNotification, addErrorNotification, addInfoNotification, notificationService } from '../services/notificationService';
import { claraBackgroundService } from '../services/claraBackgroundService';
import { createOptimizedContentHandler } from '../utils/streamOptimizer';

// Import clear data utility
import '../utils/clearClaraData';
import { copyToClipboard } from '../utils/clipboard';

// Import TTS service
import { claraTTSService } from '../services/claraTTSService';

// Import Document Chat Integration
import DocumentChatIntegration from './DocumentChatIntegration';
import { useDocumentStore, useHasDocuments, useSelectedDocuments, useDocumentPanelVisible } from '../stores/documentStore';



// 🧠 Import Perfect Compressor pour la gestion du contexte
import { perfectCompressor } from '../services/api/context/PerfectCompressor';

// 🚀 Import Session RAG Mode Service pour l'isolation par session
import { SessionRagModeService, getRagModeForSession } from '../services/session/SessionRagModeService';

// 🚀 Import LM Studio Preloader pour le préchargement optimisé
import { lmStudioPreloader } from '../services/LMStudioPreloader';

// 🔄 Import Update Service pour les mises à jour automatiques
import { updateService } from '../services/UpdateService';

// Import clipboard test functions for development
if (process.env.NODE_ENV === 'development') {
  import('../utils/clipboard.test');
}

interface ClaraAssistantProps {
  onPageChange: (page: string) => void;
}

/**
 * Generate a unique ID for messages
 */
const generateId = () => `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

/**
 * Get default system prompt for a provider
 */
const getDefaultSystemPrompt = (provider: ClaraProvider): string => {
  const providerName = provider?.name || 'AI Assistant';
  
  switch (provider?.type) {
    case 'ollama':
      return `Tu es WeMa IA, un assistant intelligent alimenté par ${providerName}. Tu es compétent, amical et fournis des informations précises. Tu peux aider avec diverses tâches incluant l'analyse, le codage, la rédaction et les questions générales. Quand tu utilises des outils, sois minutieux et explique clairement tes actions. IMPORTANT: Réponds TOUJOURS en français sauf demande explicite contraire.`;

    case 'openai':
      return `Tu es WeMa IA, un assistant intelligent alimenté par OpenAI. Tu es utile, inoffensif et honnête. Tu excelles dans le raisonnement, l'analyse, les tâches créatives et la résolution de problèmes. Efforce-toi toujours de fournir des réponses précises et bien structurées, et utilise efficacement les outils disponibles quand nécessaire. IMPORTANT: Réponds TOUJOURS en français sauf demande explicite contraire.`;
      
    case 'openrouter':
      return `Tu es WeMa IA, un assistant polyvalent avec accès à divers modèles via OpenRouter. Tu adaptes ton style de communication selon la tâche et exploites les forces des différents modèles IA. Sois utile, précis et efficace. IMPORTANT: Réponds TOUJOURS en français sauf demande explicite contraire.`;

    case 'claras-pocket':
      return `Tu es WeMa IA, un assistant IA axé sur la confidentialité fonctionnant localement. Tu priorises la vie privée de l'utilisateur et fournis une assistance utile sans nécessiter de connectivité externe. Tu es efficace, compétent et respectes les préférences de confidentialité. IMPORTANT: Réponds TOUJOURS en français sauf demande explicite contraire.`;

    default:
      return `Tu es WeMa IA, un assistant IA utile et professionnel. Tu es compétent, amical et fournis des informations précises. Tu peux aider avec diverses tâches incluant l'analyse, le codage, la rédaction et les questions générales. IMPORTANT: Réponds TOUJOURS en français sauf demande explicite contraire.`;
  }
};



// Add a hook to detect if Clara is currently visible
const useIsVisible = () => {
  const [isVisible, setIsVisible] = useState(true);
  
  useEffect(() => {
    const checkVisibility = () => {
      // Check if the Clara container is visible
      const claraContainer = document.querySelector('[data-clara-container]');
      if (claraContainer) {
        const isCurrentlyVisible = !claraContainer.classList.contains('hidden');
        setIsVisible(isCurrentlyVisible);
      }
    };
    
    // Check initially
    checkVisibility();
    
    // Set up observer for visibility changes
    const observer = new MutationObserver(checkVisibility);
    const claraContainer = document.querySelector('[data-clara-container]');
    if (claraContainer) {
      observer.observe(claraContainer, { 
        attributes: true, 
        attributeFilter: ['class'] 
      });
    }
    
    return () => observer.disconnect();
  }, []);
  
  return isVisible;
};

const ClaraAssistant: React.FC<ClaraAssistantProps> = ({ onPageChange }) => {
  // Check if Clara is currently visible (for background operation)
  const isVisible = useIsVisible();
  
  // User and session state
  const [userName, setUserName] = useState<string>('');
  const [currentSession, setCurrentSession] = useState<ClaraChatSession | null>(null);
  const [messages, setMessages] = useState<ClaraMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  // Auto TTS state - track latest AI response for voice synthesis
  const [latestAIResponse, setLatestAIResponse] = useState<string>('');
  const [autoTTSTrigger, setAutoTTSTrigger] = useState<{text: string, timestamp: number} | null>(null);
  
  // Advanced options state
  const [showAdvancedOptions, setShowAdvancedOptions] = useState(false);

  // 🧠 Context Viewer state
  const [showContextViewer, setShowContextViewer] = useState(false);

  // 🔍 Internet Search state
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // 🎨 Callbacks pour l'animation de recherche
  const handleSearchStart = useCallback((query: string) => {
    // Éviter les déclenchements multiples
    if (isSearching && searchQuery === query) {
      console.log('🎨 Animation de recherche déjà active pour:', query);
      return;
    }

    setIsSearching(true);
    setSearchQuery(query);
    // 🚀 SUPPRIMÉ: setSearchProgressData inutile
    console.log('🎨 Animation de recherche démarrée:', query);
  }, [isSearching, searchQuery]);

  const handleSearchComplete = useCallback(() => {
    if (!isSearching) {
      console.log('🎨 Animation de recherche déjà terminée');
      return;
    }

    setIsSearching(false);
    setSearchQuery('');
    // 🚀 SUPPRIMÉ: setSearchProgressData inutile
    console.log('🎨 Animation de recherche terminée');
  }, [isSearching]);

  // 🚀 SUPPRIMÉ: handleSearchProgress - inutile sans setSearchProgressData

  // 🚀 État pour l'affichage des messages (complet vs compressé)
  const [hasCompressedVersion, setHasCompressedVersion] = useState(false);
  const [messagesForLLM, setMessagesForLLM] = useState<ClaraMessage[]>([]);
  const [ragContextForLLM, setRagContextForLLM] = useState<string>(''); // 🚀 CONTEXTE RAG RÉEL

  // Session management state
  const [sessions, setSessions] = useState<ClaraChatSession[]>([]);
  const [isLoadingSessions, setIsLoadingSessions] = useState(true);
  const [hasMoreSessions, setHasMoreSessions] = useState(true);
  const [sessionPage, setSessionPage] = useState(0);
  const [isLoadingMoreSessions, setIsLoadingMoreSessions] = useState(false);
  
  // Provider and model state
  const [providers, setProviders] = useState<ClaraProvider[]>([]);
  const [models, setModels] = useState<ClaraModel[]>([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(true);

  // No models modal state
  const [showNoModelsModal, setShowNoModelsModal] = useState(false);

  // Wallpaper state
  const [wallpaperUrl, setWallpaperUrl] = useState<string | null>(null);

  // Refresh state - track when we last refreshed to avoid too frequent calls
  const [lastRefreshTime, setLastRefreshTime] = useState<number>(0);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Provider health check caching to reduce latency
  const [providerHealthCache, setProviderHealthCache] = useState<Map<string, {isHealthy: boolean, timestamp: number}>>(new Map());
  const HEALTH_CHECK_CACHE_TIME = 30000; // 30 seconds cache

  // Document Chat Integration - Using stable hooks
  const documentStore = useDocumentStore();
  const selectedDocuments = useSelectedDocuments();
  const hasDocuments = useHasDocuments();
  const isDocumentPanelVisible = useDocumentPanelVisible();

  // 🧠 PERFECT COMPRESSOR - Utilise le singleton global (pas d'instance locale nécessaire)

  // 🚀 SUPPRIMÉ: Fonctions RAG mode - inutiles

  // 🎯 FONCTION UTILITAIRE POUR DÉTECTER LA LIMITE DE CONTEXTE - MODÈLES RÉELS
  const getModelContextLimit = useCallback((modelId: string): number => {
    const modelLimits: Record<string, number> = {
      // 🎯 QWEN 3 MODELS - 14B PARTOUT (rapidité + stabilité)
      'qwen3-32b': 60000,   // 60K tokens (même limite que 14B)
      'qwen3-14b': 60000,   // 60K tokens pour 14B (rapidité)
      'qwen3-4b': 35000,    // Peut aller un peu au-delà de 30K
      'qwen3-7b': 35000,    // Peut aller un peu au-delà de 30K
      'qwen3-8b': 35000,    // Peut aller un peu au-delà de 30K
      'qwen3': 35000,       // Générique Qwen 3

      // 🎯 GEMMA MODELS - Modèles utilisés
      'gemma-27b': 32000,   // 🆕 NOUVEAU - À ajouter plus tard
      'gemma-2-27b': 32000, // Variante possible
      'gemma': 8000,        // Versions plus petites

      // 🎯 AUTRES MODÈLES POTENTIELLEMENT UTILISÉS
      'qwen2.5': 32000,
      'qwen2': 32000,
      'llama3-8b': 8000,
      'llama3-70b': 8000,
      'mistral-7b': 8000,
      'codellama': 16000,
      'codeqwen': 35000     // Même limite que Qwen 3
    };

    // Recherche par nom partiel (insensible à la casse)
    const lowerModelId = modelId.toLowerCase();
    for (const [key, limit] of Object.entries(modelLimits)) {
      if (lowerModelId.includes(key.toLowerCase())) {
        return limit;
      }
    }

    // Heuristiques basées sur la taille du modèle
    if (lowerModelId.includes('32b') || lowerModelId.includes('27b')) {
      return 35000; // Gros modèles Qwen 3 / Gemma 27B
    }
    if (lowerModelId.includes('qwen') && (lowerModelId.includes('8b') || lowerModelId.includes('7b') || lowerModelId.includes('14b'))) {
      return 35000; // Qwen 3 peut aller un peu au-delà de 30K
    }
    if (lowerModelId.includes('8b') || lowerModelId.includes('7b')) {
      return 8000; // Modèles moyens standards
    }

    // Valeur par défaut conservative
    return 8000;
  }, []);



  // Simple document panel toggle function - PRESERVE SELECTIONS
  const handleDocumentPanelToggle = useCallback(() => {
    logger.debug(LogCategory.UI, 'RAG Panel Toggle - Starting...');

    try {
      // Method 1: Try hook-based store
      if (documentStore?.toggleDocumentPanel) {
        logger.debug(LogCategory.UI, 'Using hook-based store');
        documentStore.toggleDocumentPanel();
        return;
      }

      // Method 2: Try direct store access
      const directStore = useDocumentStore.getState();
      logger.debug(LogCategory.UI, 'Direct store state available', { hasToggle: !!directStore?.toggleDocumentPanel });
      if (directStore?.toggleDocumentPanel) {
        logger.debug(LogCategory.UI, 'Using direct store access');
        directStore.toggleDocumentPanel();
        return;
      }

      // Method 3: Simple toggle WITHOUT resetting selections
      logger.debug(LogCategory.UI, 'Using simple toggle - PRESERVING SELECTIONS');
      const currentState = useDocumentStore.getState();
      const currentVisible = currentState?.isDocumentPanelVisible || false;

      useDocumentStore.setState({
        ...currentState, // PRESERVE ALL EXISTING STATE
        isDocumentPanelVisible: !currentVisible // ONLY TOGGLE VISIBILITY
      });
      logger.debug(LogCategory.UI, 'Panel toggled - selections preserved');

    } catch (error) {
      logger.error(LogCategory.UI, 'Toggle failed', error);
    }
  }, [documentStore]);

  // Debug log for document store (one-time only)
  useEffect(() => {
    logger.debug(LogCategory.DOCUMENTS, 'Testing document store access...');

    // Test 1: Hook-based store
    if (documentStore) {
      logger.debug(LogCategory.DOCUMENTS, 'Hook store available', {
        methodCount: Object.keys(documentStore).length,
        hasToggle: typeof documentStore.toggleDocumentPanel
      });
    } else {
      logger.warn(LogCategory.DOCUMENTS, 'Hook store is undefined');
    }

    // Test 2: Direct store access
    try {
      const directStore = useDocumentStore.getState();
      logger.debug(LogCategory.DOCUMENTS, 'Direct store available', {
        methodCount: Object.keys(directStore).length,
        hasToggle: typeof directStore.toggleDocumentPanel
      });
    } catch (error) {
      logger.error(LogCategory.DOCUMENTS, 'Direct store access failed', error);
    }

    // Test 3: Import verification
    logger.debug(LogCategory.DOCUMENTS, 'Store imports verified', {
      storeType: typeof useDocumentStore,
      getStateType: typeof useDocumentStore.getState
    });
  }, []); // Empty dependency array - run only once

  // Session configuration with new AI config structure
  const [sessionConfig, setSessionConfig] = useState<ClaraSessionConfig>({
    aiConfig: {
      models: {
        text: '',
        vision: '',
        code: ''
      },
      provider: '',
      parameters: {
        temperature: 0.7,
        maxTokens: 10000, // Limite réelle pour Qwen3-32B selon mémoire utilisateur
        topP: 1.0,
        topK: 40
      },
      features: {
        enableTools: false,             // **CHANGED**: Default to false for streaming mode
        enableRAG: true,                // **CHANGED**: Enable RAG for document chat
        enableStreaming: true,          // **CHANGED**: Default to streaming mode
        enableVision: true,
        autoModelSelection: true,       // **CHANGED**: Enable auto model selection for vision support
        enableMCP: false                // **CHANGED**: Default to false for streaming mode
      },
      mcp: {
        enableTools: true,
        enableResources: true,
        enabledServers: [],
        autoDiscoverTools: true,
        maxToolCalls: 5
      },
      autonomousAgent: {
        enabled: false,                 // **CHANGED**: Default to false for streaming mode
        maxRetries: 1,                  // 🚀 OPTIMISÉ: Réduit à 1 pour éviter les délais
        retryDelay: 0,                  // 🚀 OPTIMISÉ: Supprimé pour performance maximale
        enableSelfCorrection: true,
        enableToolGuidance: true,
        enableProgressTracking: true,
        maxToolCalls: 10,
        confidenceThreshold: 0.7,
        enableChainOfThought: true,
        enableErrorLearning: true
      }
    },
    contextWindow: 50 // Include last 50 messages in conversation history
  });

  // Cached provider health check to reduce latency
  const checkProviderHealthCached = useCallback(async (provider: ClaraProvider): Promise<boolean> => {
    const now = Date.now();
    const cached = providerHealthCache.get(provider.id);
    
    // Return cached result if still valid
    if (cached && (now - cached.timestamp < HEALTH_CHECK_CACHE_TIME)) {
      logger.debug(LogCategory.PROVIDERS, `Using cached health status for ${provider.name}`, { isHealthy: cached.isHealthy });
      return cached.isHealthy;
    }

    // Perform actual health check
    logger.group(LogCategory.PROVIDERS, `health-check-${provider.id}`, `Performing health check for ${provider.name}`);
    try {
      const isHealthy = await claraApiService.testProvider(provider);

      // Cache the result
      setProviderHealthCache(prev => {
        const newCache = new Map(prev);
        newCache.set(provider.id, { isHealthy, timestamp: now });
        return newCache;
      });

      logger.info(LogCategory.PROVIDERS, `Health check result for ${provider.name}`, { isHealthy });
      return isHealthy;
    } catch (error) {
      logger.warn(LogCategory.PROVIDERS, `Health check failed for ${provider.name}`, error);
      
      // Cache the failure
      setProviderHealthCache(prev => {
        const newCache = new Map(prev);
        newCache.set(provider.id, { isHealthy: false, timestamp: now });
        return newCache;
      });
      
      return false;
    }
  }, [providerHealthCache, HEALTH_CHECK_CACHE_TIME]);

  // Refresh providers, models, and MCP services
  const refreshProvidersAndServices = useCallback(async (force: boolean = false) => {
    const now = Date.now();
    const timeSinceLastRefresh = now - lastRefreshTime;
    const REFRESH_COOLDOWN = 30000; // 🚀 30 seconds cooldown pour réduire les appels
    
    // Avoid too frequent refreshes unless forced
    if (!force && timeSinceLastRefresh < REFRESH_COOLDOWN) {
      logger.debug(LogCategory.PROVIDERS, `Skipping refresh - cooldown active`, {
        lastRefreshSeconds: Math.round(timeSinceLastRefresh / 1000),
        cooldownSeconds: REFRESH_COOLDOWN / 1000
      });
      return;
    }

    if (isRefreshing) {
      logger.debug(LogCategory.PROVIDERS, 'Refresh already in progress, skipping...');
      return;
    }

    setIsRefreshing(true);
    setLastRefreshTime(now);

    try {
      logger.info(LogCategory.PROVIDERS, 'Refreshing providers, models, and services...');

      // Refresh MCP service
      try {
        logger.debug(LogCategory.PROVIDERS, 'Refreshing MCP services...');
        await claraMCPService.refresh();
        logger.debug(LogCategory.PROVIDERS, 'MCP services refreshed');
      } catch (mcpError) {
        logger.warn(LogCategory.PROVIDERS, 'MCP refresh failed', mcpError);
      }

      // Reload providers
      logger.debug(LogCategory.PROVIDERS, 'Refreshing providers...');
      const refreshedProviders = await claraApiService.getProviders();
      setProviders(refreshedProviders);
      logger.info(LogCategory.PROVIDERS, `Loaded ${refreshedProviders.length} providers`);

      // Clean up invalid provider configurations
      const validProviderIds = refreshedProviders.map(p => p.id);
      cleanInvalidProviderConfigs(validProviderIds);

      // Load models from ALL providers
      let allModels: ClaraModel[] = [];
      for (const provider of refreshedProviders) {
        try {
          const providerModels = await claraApiService.getModels(provider.id);
          allModels = [...allModels, ...providerModels];
          logger.group(LogCategory.PROVIDERS, `models-${provider.id}`, `Loaded ${providerModels.length} models from ${provider.name}`);
        } catch (error) {
          logger.warn(LogCategory.PROVIDERS, `Failed to load models from ${provider.name}`, error);
        }
      }

      setModels(allModels);
      logger.info(LogCategory.PROVIDERS, `Total models refreshed: ${allModels.length}`);

      // Update current provider if needed
      const currentProviderId = sessionConfig.aiConfig?.provider;
      if (currentProviderId) {
        const currentProvider = refreshedProviders.find(p => p.id === currentProviderId);
        if (currentProvider) {
          claraApiService.updateProvider(currentProvider);
          console.log(`🔧 Updated current provider: ${currentProvider.name}`);
        }
      }

      // Health check current provider
      if (sessionConfig.aiConfig?.provider) {
        const currentProvider = refreshedProviders.find(p => p.id === sessionConfig.aiConfig.provider);
        if (currentProvider) {
          try {
            const isHealthy = await claraApiService.testProvider(currentProvider);
            if (!isHealthy) {
              console.warn(`⚠️ Current provider ${currentProvider.name} health check failed`);
            }
          } catch (healthError) {
            console.warn(`⚠️ Health check failed for ${currentProvider.name}:`, healthError);
          }
        }
      }

      console.log('✅ Providers and services refresh complete');
      
    } catch (error) {
      console.error('❌ Failed to refresh providers and services:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [lastRefreshTime, isRefreshing, sessionConfig.aiConfig?.provider]);

  // Load user name from database
  useEffect(() => {
    const loadUserName = async () => {
      const personalInfo = await db.getPersonalInfo();
      if (personalInfo?.name) {
        const formattedName = personalInfo.name.charAt(0).toUpperCase() + personalInfo.name.slice(1).toLowerCase();
        setUserName(formattedName);
      }
    };
    loadUserName();
  }, []);

  // 🔄 Initialiser le service de mise à jour automatique
  useEffect(() => {
    // Démarrer la vérification automatique des mises à jour
    updateService.startAutoCheck();

    // Nettoyer à la fermeture
    return () => {
      updateService.stopAutoCheck();
    };
  }, []);

  // Load wallpaper from database
  useEffect(() => {
    const loadWallpaper = async () => {
      try {
        const wallpaper = await db.getWallpaper();
        if (wallpaper) {
          setWallpaperUrl(wallpaper);
        }
      } catch (error) {
        console.error('Error loading wallpaper:', error);
      }
    };
    loadWallpaper();
  }, []);

  // 🎯 SUPPRIMÉ : Plus de nettoyage ici, MessageBubble s'occupe de tout

  // 🎯 SUPPRIMÉ : Plus de migration nécessaire

  // 🎯 SUPPRIMÉ : Plus de migration globale nécessaire

  // Track Clara's visibility state for background service
  useEffect(() => {
    claraBackgroundService.setBackgroundMode(!isVisible);
  }, [isVisible]);

  // Auto-refresh when Clara becomes visible again
  useEffect(() => {
    if (isVisible && !isLoadingProviders) {
      // Trigger refresh when Clara becomes visible
      console.log('👁️ Clara became visible - checking for updates...');
      refreshProvidersAndServices(false); // Use cooldown to avoid spam
    }
  }, [isVisible, isLoadingProviders, refreshProvidersAndServices]);

  // Load available documents on component mount (one-time only)
  useEffect(() => {
    const loadDocuments = async () => {
      try {
        const store = useDocumentStore.getState();
        if (store?.loadAvailableDocuments) {
          console.log('📄 Loading available documents...');
          await store.loadAvailableDocuments();
        } else {
          console.warn('⚠️ Document store not available or loadAvailableDocuments method missing');
        }
      } catch (error) {
        console.error('❌ Failed to load documents:', error);
      }
    };

    // Load documents only once on mount
    loadDocuments();
  }, []); // Empty dependency array - run only once

  // Load chat sessions on component mount
  useEffect(() => {
    const loadInitialSessions = async () => {
      setIsLoadingSessions(true);
      try {
        logger.debug(LogCategory.SESSIONS, 'Starting lightning-fast session loading...');
        const startTime = performance.now();

        // Load sessions WITHOUT messages first for instant UI
        const recentSessions = await claraDB.getRecentClaraSessionsLight(20); // Load only 20 initially
        logger.performance(LogCategory.SESSIONS, 'Loaded sessions', startTime, { count: recentSessions.length });
        
        setSessions(recentSessions);
        setSessionPage(1);
        setHasMoreSessions(recentSessions.length === 20);
        
        // If no current session and we have existing sessions, load the most recent one
        if (!currentSession && recentSessions.length > 0) {
          const mostRecent = recentSessions[0];
          // Load messages for the most recent session only
          const sessionWithMessages = await claraDB.getClaraSession(mostRecent.id);
          if (sessionWithMessages) {
            setCurrentSession(sessionWithMessages);
            setMessages(sessionWithMessages.messages);

            // 🚀 CORRECTION CRITIQUE : Configurer le document store pour cette session
            const documentStore = useDocumentStore.getState();
            if (documentStore.setSession) {
              documentStore.setSession(sessionWithMessages.id);
              console.log('🔄 Document store configured for auto-loaded session:', sessionWithMessages.id);
            } else {
              console.warn('⚠️ Document store setSession method not available during auto-load');
            }

            // 🚀 ISOLATION : Initialiser le mode RAG pour cette session auto-chargée
            const ragMode = getRagModeForSession(sessionWithMessages.id, sessionWithMessages.config);
            setSessionConfig(prev => ({
              ...prev,
              ragMode
            }));

            logger.info(LogCategory.SESSIONS, `🚀 Auto-loaded session with RAG mode: ${ragMode} for session ${sessionWithMessages.id}`);

            console.log('📝 Auto-loaded most recent session:', sessionWithMessages.title, 'with', sessionWithMessages.messages.length, 'messages');
          }
        }
        
        // Background cleanup (non-blocking)
        setTimeout(async () => {
          try {
            const integrity = await claraDB.debugDataIntegrity();
            if (integrity.orphanedMessages > 0 || integrity.orphanedFiles > 0) {
              console.log('🧹 Cleaning up orphaned data in background...');
              await claraDB.cleanupOrphanedData();
            }
          } catch (error) {
            console.warn('Background cleanup failed:', error);
          }
        }, 1000);
        
      } catch (error) {
        console.error('Failed to load chat sessions:', error);
      } finally {
        setIsLoadingSessions(false);
      }
    };

    loadInitialSessions();
  }, []);

  // Load more sessions function for pagination
  const loadMoreSessions = useCallback(async () => {
    if (isLoadingMoreSessions || !hasMoreSessions) return;
    
    setIsLoadingMoreSessions(true);
    try {
      const moreSessions = await claraDB.getRecentClaraSessionsLight(20, sessionPage * 20);
      if (moreSessions.length > 0) {
        setSessions(prev => [...prev, ...moreSessions]);
        setSessionPage(prev => prev + 1);
        setHasMoreSessions(moreSessions.length === 20);
      } else {
        setHasMoreSessions(false);
      }
    } catch (error) {
      console.error('Failed to load more sessions:', error);
    } finally {
      setIsLoadingMoreSessions(false);
    }
  }, [sessionPage, isLoadingMoreSessions, hasMoreSessions]);

  // Load providers and models
  useEffect(() => {
    const loadProvidersAndModels = async () => {
      setIsLoadingProviders(true);
      try {
        // Initialize MCP service
        try {
          await claraMCPService.initialize();
          console.log('MCP service initialized successfully');
        } catch (mcpError) {
          console.warn('MCP service initialization failed:', mcpError);
        }

        // 🚀 CORRECTION: Nettoyer les doublons et providers indésirables
        console.log('🔧 Cleaning up provider duplicates...');

        const existingProviders = await db.getAllProviders();

        // Supprimer les doublons et OpenRouter
        const providersToKeep = new Map();
        const providersToDelete = [];

        for (const provider of existingProviders) {
          const key = provider.type;

          // Supprimer OpenRouter automatiquement
          if (provider.type === 'openrouter') {
            console.log(`🗑️ Removing unwanted provider: ${provider.name}`);
            providersToDelete.push(provider.id);
            continue;
          }

          // Garder seulement le premier de chaque type
          if (!providersToKeep.has(key)) {
            providersToKeep.set(key, provider);
          } else {
            console.log(`🗑️ Removing duplicate provider: ${provider.name} (${provider.type})`);
            providersToDelete.push(provider.id);
          }
        }

        // Supprimer les providers indésirables
        for (const providerId of providersToDelete) {
          await db.deleteProvider(providerId);
        }

        // 🚀 MIGRATION SUPPRIMÉE: Ne plus forcer localhost
        // Le serveur central (***********:1234) est maintenant la priorité absolue

        // Recréer seulement les providers manquants
        await db.initializeDefaultProviders();

        // Load providers
        const loadedProviders = await claraApiService.getProviders();
        setProviders(loadedProviders);
        console.log('🔧 Providers reloaded with correct configurations:', loadedProviders);

        // Clean up invalid provider configurations
        const validProviderIds = loadedProviders.map(p => p.id);
        cleanInvalidProviderConfigs(validProviderIds);

        // Load models from ALL providers to check availability
        let allModels: ClaraModel[] = [];
        for (const provider of loadedProviders) {
          try {
            const providerModels = await claraApiService.getModels(provider.id);
            allModels = [...allModels, ...providerModels];
            console.log(`Loaded ${providerModels.length} models from provider: ${provider.name}`);
          } catch (error) {
            console.warn(`Failed to load models from provider ${provider.name}:`, error);
          }
        }
        
        // Set all models for the modal check
        setModels(allModels);
        console.log(`Total models available across all providers: ${allModels.length}`);

        // 🚀 AFFICHER DES INSTRUCTIONS SI AUCUN MODÈLE N'EST DISPONIBLE
        if (allModels.length === 0) {
          console.log('🔧 No models available - showing setup instructions');
          addInfoNotification(
            "Configuration des modèles IA",
            "Aucun modèle IA n'est disponible. Veuillez :\n" +
            "1. Démarrer LM Studio (port 1234) avec un modèle chargé\n" +
            "2. Démarrer Ollama (port 11434) avec des modèles installés\n" +
            "3. Ou configurer OpenRouter avec votre clé API dans les paramètres",
            15000
          );
        }

        // Get primary provider and set it in config
        const primaryProvider = loadedProviders.find(p => p.isPrimary) || loadedProviders[0];
        console.log('🎯 Provider primaire détecté:', primaryProvider?.name, 'isPrimary:', primaryProvider?.isPrimary);
        // 🚀 SUPPRIMÉ: Log de debug - code propre

        if (primaryProvider) {
          // AUTO-START CLARA'S POCKET IF IT'S THE PRIMARY PROVIDER
          if (primaryProvider.type === 'claras-pocket' && window.llamaSwap) {
            try {
              console.log("🚀 Checking Clara's Core status on startup...");
              const status = await window.llamaSwap.getStatus?.();
              if (!status?.isRunning) {
                console.log("🔄 Clara's Core is not running, starting automatically...");
                addInfoNotification(
                  "Démarrage du moteur WeMa IA...",
                  'WeMa IA démarre son service d\'intelligence artificielle local. Cela peut prendre un moment.',
                  6000
                );
                
                const result = await window.llamaSwap.start();
                if (!result.success) {
                  addErrorNotification(
                    "Échec du démarrage du moteur WeMa IA",
                    result.error || 'Impossible de démarrer le service d\'IA local. Veuillez vérifier votre installation.',
                    10000
                  );
                  console.error("❌ Failed to start Clara's Core:", result.error);
                } else {
                  console.log("✅ Clara's Core started successfully");
                  addInfoNotification(
                    "Moteur WeMa IA prêt",
                    'Votre service d\'IA local fonctionne et est prêt à discuter !',
                    4000
                  );
                  // Wait a moment for service to be fully ready
                  await new Promise(res => setTimeout(res, 2000));
                }
              } else {
                console.log("✅ Clara's Core is already running");
                addInfoNotification(
                  "Clara's Core Online",
                  'Your local AI service is ready and waiting for your messages.',
                  3000
                );
              }
            } catch (err) {
              console.error("⚠️ Error checking/starting Clara's Core:", err);
              addErrorNotification(
                "Clara's Core Startup Error",
                err instanceof Error ? err.message : 'Could not communicate with the local AI service.',
                8000
              );
            }
          }

          // Update API service to use primary provider
          claraApiService.updateProvider(primaryProvider);

          // Get models specifically for the primary provider for configuration
          const primaryProviderModels = allModels.filter(m => m.provider === primaryProvider.id);

          // 🚀 FORCER LA SÉLECTION DU PROVIDER PRIMAIRE DANS L'INTERFACE
          console.log('🔄 Forçage de la sélection du provider primaire dans l\'interface...');

          // Try to load saved config for this provider first
          const savedConfig = loadProviderConfig(primaryProvider.id);
          if (savedConfig) {
            console.log('Loading saved config for provider:', primaryProvider.name, savedConfig);

            // 🚀 FORCER LE MODÈLE 14B PARTOUT
            const primaryProviderModels = allModels.filter(m => m.provider === primaryProvider.id);
            const forced14BModel = primaryProviderModels.find(m => {
              const modelId = m.id.toLowerCase();
              return (
                // LM Studio: qwen3-14b ou qwen-14b
                modelId === 'qwen3-14b' ||
                modelId === 'qwen-14b' ||
                // Ollama: qwen3-14b-optimized
                modelId.includes('qwen3-14b-optimized') ||
                modelId.includes('qwen-14b-optimized') ||
                // Recherche générale 14B
                (modelId.includes('qwen') && modelId.includes('14b'))
              );
            });

            console.log('🚀 FORÇAGE 14B sur config sauvegardée:', forced14BModel?.id || 'non trouvé');

            // Forcer la mise à jour avec le provider primaire ET le modèle 32B
            setSessionConfig(prev => ({
              ...prev,
              aiConfig: {
                ...savedConfig,
                provider: primaryProvider.id, // S'assurer que le provider est bien défini
                models: {
                  ...savedConfig.models,
                  text: forced14BModel?.id || savedConfig.models?.text || '' // Forcer 14B
                }
              }
            }));
          } else {
            console.log('No saved config found for provider:', primaryProvider.name, 'creating default config');
            // Auto-select first available models for this provider
            const textModel = primaryProviderModels.find(m => 
              m.provider === primaryProvider.id && 
              (m.type === 'text' || m.type === 'multimodal')
            );
            const visionModel = primaryProviderModels.find(m => 
              m.provider === primaryProvider.id && 
              m.supportsVision
            );
            const codeModel = primaryProviderModels.find(m => 
              m.provider === primaryProvider.id && 
              m.supportsCode
            );

            // 🚀 UTILISER LE MODÈLE 14B PAR DÉFAUT (plus rapide)
            const defaultTextModel = primaryProviderModels.find(m =>
              m.id.includes('14b') || m.id.includes('qwen3-14b')
            ) || textModel;

            const defaultConfig = {
              provider: primaryProvider.id,
              systemPrompt: getDefaultSystemPrompt(primaryProvider),
              models: {
                text: defaultTextModel?.id || '',
                vision: visionModel?.id || '',
                code: codeModel?.id || ''
              },
              parameters: {
                temperature: 0.7,
                maxTokens: 35000, // Limite réelle pour Qwen3-32B
                topP: 1.0,
                topK: 40
              },
              features: {
                enableTools: false,           // **CHANGED**: Default to false for streaming mode
                enableRAG: true,              // **CHANGED**: Enable RAG for document chat
                enableStreaming: true,        // **CHANGED**: Default to streaming mode
                enableVision: true,
                autoModelSelection: true,     // **CHANGED**: Enable auto model selection for vision support
                enableMCP: false              // **CHANGED**: Default to false for streaming mode
              },
              mcp: {
                enableTools: true,
                enableResources: true,
                enabledServers: [],
                autoDiscoverTools: true,
                maxToolCalls: 5
              },
              autonomousAgent: {
                enabled: false,               // **CHANGED**: Default to false for streaming mode
                maxRetries: 1,                // 🚀 OPTIMISÉ: Réduit à 1 pour éviter les délais
                retryDelay: 0,                // 🚀 OPTIMISÉ: Supprimé pour performance maximale
                enableSelfCorrection: true,
                enableToolGuidance: true,
                enableProgressTracking: true,
                maxToolCalls: 10,
                confidenceThreshold: 0.7,
                enableChainOfThought: true,
                enableErrorLearning: true
              },
              contextWindow: 50 // Include last 50 messages in conversation history
            };

            console.log('🚀 Configuration par défaut créée:', {
              provider: defaultConfig.provider,
              providerName: primaryProvider.name,
              textModel: defaultConfig.models.text
            });

            setSessionConfig(prev => ({
              ...prev,
              aiConfig: defaultConfig
            }));

            // Save the default config
            saveProviderConfig(primaryProvider.id, defaultConfig);

            console.log('✅ Provider primaire configuré et sauvegardé:', primaryProvider.name);
          }

          // 🚀 OPTIMISATION MULTI-UTILISATEURS : Pas de préchargement automatique au démarrage
          // Le préchargement se fera seulement quand l'utilisateur interagit avec la barre de chat
          console.log('🏠 Modèle configuré mais pas préchargé - économie de ressources multi-utilisateurs');

          // 🧠 INITIALISER LE GESTIONNAIRE DE CONTEXTE INTELLIGENT
          console.log('🧠 Initialisation du gestionnaire de contexte intelligent...');
          await import('../services/api/context/IntelligentContextManager');
          console.log('✅ Gestionnaire de contexte intelligent initialisé');
        }
      } catch (error) {
        console.error('Failed to load providers and models:', error);
      } finally {
        setIsLoadingProviders(false);
      }
    };

    loadProvidersAndModels();
  }, []);

  // Monitor models availability to show/hide no models modal
  useEffect(() => {
    if (!isLoadingProviders) {
      // 🚫 DÉSACTIVER LA POPUP BLOQUANTE - Laisser l'utilisateur configurer les providers
      // const hasModels = models.length > 0;
      // setShowNoModelsModal(!hasModels);
      setShowNoModelsModal(false); // Toujours masquer la popup

      if (models.length === 0) {
        console.log('No models available - but allowing user to configure providers');
      } else {
        console.log(`Found ${models.length} models - ready to chat`);
      }
    }
  }, [models, isLoadingProviders]);

  // Initialize TTS service
  useEffect(() => {
    const initializeTTS = async () => {
      try {
        console.log('🔊 Starting TTS service health monitoring...');
        // Force an initial health check
        const isHealthy = await claraTTSService.forceHealthCheck();
        console.log(`✅ TTS service health check complete: ${isHealthy ? 'healthy' : 'unhealthy'}`);
      } catch (error) {
        console.warn('⚠️ TTS service health check failed:', error);
        // TTS is optional, so we don't throw an error
      }
    };

    initializeTTS();
    
    // Cleanup TTS service on unmount
    return () => {
      claraTTSService.destroy();
    };
  }, []);

  // 🎯 FONCTION DE PRÉCHARGEMENT SIMPLE
  const preloadModel = useCallback(async (provider: ClaraProvider, allModels: ClaraModel[], force = false) => {
    try {
      if (provider.type !== 'lmstudio') return;

      const providerModels = allModels.filter(m => m.provider === provider.id);

      // 🚀 CORRECTION: Utiliser le modèle sélectionné dans la configuration
      let targetModel = null;

      // Chercher le modèle text sélectionné en priorité
      if (sessionConfig.aiConfig?.models?.text) {
        targetModel = providerModels.find(m => m.id === sessionConfig.aiConfig.models.text);
      }

      // Fallback sur le premier modèle text/multimodal disponible
      if (!targetModel) {
        targetModel = providerModels.find(m => m.type === 'text' || m.type === 'multimodal');
      }

      if (!targetModel) {
        console.log('🎯 No suitable model found for preload');
        return;
      }

      console.log(`🎯 ${force ? 'Force' : 'Smart'} preload for:`, targetModel.name, `(${targetModel.id})`);

      // 🎯 LOGIQUE SIMPLE
      const success = force
        ? await lmStudioPreloader.forcePreload(targetModel.id)
        : await lmStudioPreloader.preload(targetModel.id);

      if (success && !lmStudioPreloader.isCurrentlyPreloaded(targetModel.id)) {
        // Notification seulement si vraiment préchargé maintenant
        addInfoNotification('Modèle IA prêt', `${targetModel.name} chargé !`, 2000);
      }
    } catch (error) {
      console.warn('⚠️ Preload failed (non-critical):', error);
    }
  }, [sessionConfig.aiConfig?.models?.text]);

  // 🧠 FONCTION POUR AJOUTER UN MESSAGE INTERACTIF DE GESTION DU CONTEXTE
  const addContextManagementMessage = useCallback(async (
    contextSize: number,
    currentModel: 'primary' | 'fallback',
    action: 'switch_model' | 'compress' | 'new_conversation'
  ) => {
    const contextKb = Math.round(contextSize / 1000);

    let messageContent = '';
    let actionButtons: Array<{label: string, action: string, description: string}> = [];

    if (currentModel === 'primary' && action === 'switch_model') {
      // Contexte trop volumineux pour le 32B
      messageContent = `📊 **Contexte volumineux détecté** (${contextKb}K caractères)\n\n` +
                      `Le modèle actuel (32B haute performance) peut traiter votre demande mais ce sera plus lent avec autant de contexte.\n\n` +
                      `**Que souhaitez-vous faire ?**`;

      actionButtons = [
        {
          label: '⚡ Passer au modèle rapide (14B)',
          action: 'switch_to_14b',
          description: 'Plus rapide, gère plus de contexte, légèrement moins précis'
        },
        {
          label: '🗜️ Compresser la conversation',
          action: 'compress_context',
          description: 'Garder le modèle 32B mais résumer l\'historique'
        },
        {
          label: '🎯 Continuer avec le 32B',
          action: 'continue_32b',
          description: 'Garder la qualité maximale (plus lent)'
        }
      ];
    } else if (currentModel === 'fallback' && action === 'compress') {
      // Contexte trop volumineux même pour le 14B
      messageContent = `📊 **Contexte très volumineux** (${contextKb}K caractères)\n\n` +
                      `Même le modèle rapide (14B) aura des difficultés avec autant de contexte.\n\n` +
                      `**Que souhaitez-vous faire ?**`;

      actionButtons = [
        {
          label: '🗜️ Compresser la conversation',
          action: 'compress_context',
          description: 'Résumer l\'historique pour continuer'
        },
        {
          label: '🆕 Nouvelle conversation',
          action: 'new_conversation',
          description: 'Repartir à zéro (recommandé)'
        },
        {
          label: '🐌 Continuer quand même',
          action: 'continue_anyway',
          description: 'Peut être très lent'
        }
      ];
    }

    // Créer le message interactif
    const interactiveMessage: ClaraMessage = {
      id: generateId(),
      role: 'assistant',
      content: messageContent,
      timestamp: new Date(),
      metadata: {
        type: 'context_management',
        contextSize,
        currentModel,
        recommendedAction: action,
        actionButtons,
        isInteractive: true
      }
    };

    // Ajouter le message au chat
    setMessages(prev => [...prev, interactiveMessage]);

    return interactiveMessage.id;
  }, []);

  // 🧠 FONCTION POUR GÉRER LES ACTIONS DES BOUTONS INTERACTIFS
  const handleContextAction = useCallback(async (action: string, messageId: string) => {
    console.log(`🧠 Action contexte sélectionnée: ${action} pour message ${messageId}`);

    try {
      switch (action) {
        case 'switch_to_14b':
          // Passer au modèle 14B et précharger
          console.log('⚡ Passage au modèle 14B...');

          // Trouver le modèle 14B
          const model14B = models.find(m =>
            m.id.includes('14b') || m.id.includes('qwen3-14b')
          );

          if (model14B) {
            // Mettre à jour la configuration
            setSessionConfig(prev => ({
              ...prev,
              aiConfig: {
                ...prev.aiConfig!,
                models: {
                  ...prev.aiConfig!.models,
                  text: model14B.id
                }
              }
            }));

            // Précharger le modèle 14B
            const lmStudioProvider = providers.find(p => p.type === 'lmstudio' && p.isEnabled);
            if (lmStudioProvider) {
              console.log('🚀 Préchargement du modèle 14B...');
              await preloadModel(lmStudioProvider, models, true);
            }

            // Message de confirmation
            const confirmMessage: ClaraMessage = {
              id: generateId(),
              role: 'assistant',
              content: `✅ **Modèle changé avec succès !**\n\nJe suis maintenant sur le modèle rapide (14B). Vous pouvez continuer votre conversation avec plus de contexte et une réponse plus rapide.`,
              timestamp: new Date(),
              metadata: { type: 'system_info' }
            };
            setMessages(prev => [...prev, confirmMessage]);
          }
          break;

        case 'compress_context':
          // Compresser le contexte
          console.log('🗜️ Compression du contexte...');

          try {
            const { intelligentContextManager } = await import('../services/api/context/IntelligentContextManager');
            const analysis = intelligentContextManager.analyzeContext(messages);
            const result = await intelligentContextManager.executeRecommendedAction(analysis, messages);

            if (result.success && result.optimizedMessages) {
              setMessages(result.optimizedMessages);
              setMessagesForLLM(result.optimizedMessages);
              setHasCompressedVersion(true);

              // Message de confirmation
              const confirmMessage: ClaraMessage = {
                id: generateId(),
                role: 'assistant',
                content: `✅ **Conversation compressée !**\n\nJ'ai résumé intelligemment notre historique pour libérer de l'espace. Vous pouvez continuer la conversation normalement.`,
                timestamp: new Date(),
                metadata: { type: 'system_info' }
              };
              setMessages(prev => [...prev, confirmMessage]);
            }
          } catch (error) {
            console.error('❌ Erreur compression:', error);
            addInfoNotification('Erreur lors de la compression du contexte', 'error', 4000);
          }
          break;

        case 'new_conversation':
          // Nouvelle conversation
          console.log('🆕 Nouvelle conversation...');
          // Créer une nouvelle session (utiliser la fonction existante)
          window.location.reload(); // Simple reload pour nouvelle conversation
          break;

        case 'continue_32b':
        case 'continue_anyway':
          // Continuer avec le modèle actuel
          console.log('🎯 Continuation avec le modèle actuel...');

          const continueMessage: ClaraMessage = {
            id: generateId(),
            role: 'assistant',
            content: `✅ **Très bien !**\n\nJe continue avec le modèle actuel. Les réponses peuvent être un peu plus lentes avec autant de contexte, mais la qualité sera maintenue.`,
            timestamp: new Date(),
            metadata: { type: 'system_info' }
          };
          setMessages(prev => [...prev, continueMessage]);
          break;
      }

      // Marquer le message interactif comme traité
      setMessages(prev => prev.map(msg =>
        msg.id === messageId
          ? { ...msg, metadata: { ...msg.metadata, actionTaken: action, isProcessed: true } }
          : msg
      ));

    } catch (error) {
      console.error('❌ Erreur lors de l\'action contexte:', error);
      addInfoNotification('Erreur lors du traitement de votre choix', 'error', 4000);
    }
  }, [messages, models, providers, preloadModel]);

  // 🚀 OPTIMISATION MULTI-UTILISATEURS : Pas de préchargement automatique au changement de session
  // Le préchargement se fera seulement lors de l'interaction utilisateur
  useEffect(() => {
    const handleSessionChange = () => {
      if (currentSession) {
        console.log('🎯 Session changed - modèle configuré sans préchargement automatique');
        // Configuration des modèles sans préchargement pour économiser les ressources
      }
    };

    handleSessionChange();
  }, [currentSession?.id]); // Déclencher à chaque changement de session

  // 🚀 OPTIMISATION MULTI-UTILISATEURS : Pas de préchargement automatique à la navigation
  // Le préchargement se fera seulement lors de l'interaction utilisateur (focus/typing)
  useEffect(() => {
    const handleNavigationPreload = () => {
      console.log('🎯 Clara navigation detected - configuration des modèles sans préchargement');
      // Pas de préchargement automatique pour économiser les ressources serveur
    };

    // Écouter l'événement personnalisé (pour compatibilité)
    window.addEventListener('clara-navigation-preload', handleNavigationPreload as any);

    // Cleanup
    return () => {
      window.removeEventListener('clara-navigation-preload', handleNavigationPreload as any);
    };
  }, []);

  // 🔄 PRÉCHARGEMENT FORCÉ QUAND LE MODÈLE CHANGE
  useEffect(() => {
    const handleModelChange = async () => {
      if (!sessionConfig.aiConfig?.models?.text || providers.length === 0 || models.length === 0) {
        return;
      }

      // 🚀 CORRECTION: Trouver le provider actuel basé sur la configuration de session
      const currentProvider = providers.find(p => p.id === sessionConfig.aiConfig?.provider);

      if (currentProvider && currentProvider.type === 'lmstudio') {
        console.log('🔄 Model configuration changed - forcing preload...', {
          provider: currentProvider.name,
          model: sessionConfig.aiConfig?.models?.text
        });

        // Forcer le préchargement du nouveau modèle
        preloadModel(currentProvider, models, true).catch(error => {
          console.warn('⚠️ Model change preload failed (non-critical):', error);
        });
      } else if (currentProvider) {
        console.log('🔄 Model changed but not LM Studio provider:', currentProvider.type);
      } else {
        console.log('🔄 No current provider found for preload');
      }
    };

    handleModelChange();
  }, [sessionConfig.aiConfig?.models?.text, sessionConfig.aiConfig?.provider, providers, models, preloadModel]);

  // Helper function to suggest available vision models
  const getSuggestedVisionModels = useCallback(() => {
    if (!sessionConfig.aiConfig?.provider) return [];

    const currentProviderModels = models.filter(m =>
      m.provider === sessionConfig.aiConfig.provider && m.supportsVision
    );

    return currentProviderModels.slice(0, 3); // Return top 3 vision models
  }, [models, sessionConfig.aiConfig?.provider]);

  // 🚀 SUPPRIMÉ: Nettoyage des doublons RAG - prévention à la source maintenant

  // Create new session
  const createNewSession = useCallback(async (): Promise<ClaraChatSession> => {
    try {
      const session = await claraDB.createClaraSession('New Chat');
      setSessions(prev => [session, ...prev]);
      return session;
    } catch (error) {
      console.error('Failed to create new session:', error);
      // Fallback to in-memory session
      const session: ClaraChatSession = {
        id: generateId(),
        title: 'New Chat',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        config: sessionConfig
      };
      return session;
    }
  }, [sessionConfig]);

  // Handle sending a new message
  const handleSendMessage = useCallback(async (
    content: string,
    attachments?: ClaraFileAttachment[],
    toolUseConfig?: {
      forceSearchMode?: boolean;
    },
    onSearchProgress?: (sources: string[], action: string) => void,
    enhancedPrompt?: string // 🎯 NOUVEAU : Prompt enrichi pour le LLM
  ) => {
    if (!content.trim() && (!attachments || attachments.length === 0)) return;

    // 🎯 Prêt pour l'envoi de message

    // 🚀 OPTIMISATION MULTI-UTILISATEURS : Créer la session seulement au premier message
    let activeSession = currentSession;
    if (!activeSession) {
      console.log('🆕 Premier message détecté - création de la session...');
      activeSession = await createNewSession();
      setCurrentSession(activeSession);
      setMessages([]);

      // Configurer le document store pour la nouvelle session
      const documentStore = useDocumentStore.getState();
      if (documentStore.setSession) {
        documentStore.setSession(activeSession.id);
        console.log('🔄 Document store configured for new session:', activeSession.id);
      }

      logger.info(LogCategory.SESSIONS, `🚀 Session créée au premier message: ${activeSession.id}`);
    }

    if (!sessionConfig.aiConfig) return;

    // Enhanced content with RAG context if documents are selected
    let enhancedContent = content;
    let ragContext = '';
    let usedDocuments: any[] = [];

    // Debug: Check store synchronization
    const realTimeStore = useDocumentStore.getState();
    const realTimeSelectedDocs = realTimeStore?.selectedDocuments || [];
    const realTimeHasDocuments = realTimeSelectedDocs.length > 0;

    logger.debug(LogCategory.RAG, 'Store synchronization check', {
      hookHasDocuments: hasDocuments,
      storeDocCount: realTimeSelectedDocs.length,
      documents: realTimeSelectedDocs.map(d => ({ id: d.id, filename: d.filename }))
    });

    // 🚀 NOUVEAU SYSTÈME UNIFIÉ : Gérer documents sélectionnés ET documents récents de chat
    let documentsToUse = realTimeSelectedDocs;

    // 🔧 VÉRIFICATION DU CONTENU DES DOCUMENTS
    // Les documents du store devraient déjà avoir le contenu complet
    if (documentsToUse.length > 0) {
      console.log(`🔧 Vérification contenu documents: ${documentsToUse.map(d => `${d.filename} (${d.content?.length || 0} chars)`).join(', ')}`);

      // Si un document n'a pas de contenu, essayer de le récupérer depuis availableDocuments
      const documentsWithContent = documentsToUse.map(doc => {
        if (!doc.content || doc.content.length < 100) {
          // Chercher le document complet dans availableDocuments
          const fullDoc = realTimeStore?.availableDocuments?.find(availDoc => availDoc.id === doc.id);
          if (fullDoc && fullDoc.content) {
            console.log(`📄 Contenu récupéré depuis availableDocuments pour ${doc.filename}: ${fullDoc.content.length} chars`);
            return {
              ...doc,
              content: fullDoc.content
            };
          } else {
            console.warn(`⚠️ Pas de contenu trouvé pour ${doc.filename} (ni dans selectedDocuments ni dans availableDocuments)`);
            return doc;
          }
        }
        return doc;
      });

      documentsToUse = documentsWithContent;
      console.log(`🔧 Documents finaux avec contenu: ${documentsToUse.map(d => `${d.filename} (${d.content?.length || 0} chars)`).join(', ')}`);
    }

    // 🚀 CORRECTION: Ne PAS utiliser automatiquement les documents récents
    // Cela causait une contamination entre conversations
    // Les documents doivent être explicitement sélectionnés par l'utilisateur
    if (!realTimeHasDocuments) {
      logger.debug(LogCategory.RAG, 'No documents selected, RAG will be disabled for this query');
      documentsToUse = [];
    }

    // 🚨 GESTION INTELLIGENTE : Recherche + Documents avec limitation contexte
    const hasSearchMode = toolUseConfig?.forceSearchMode;
    const hasAnyDocuments = documentsToUse && documentsToUse.length > 0;

    if (hasSearchMode && hasAnyDocuments) {
      // 🚨 COMBINAISON DÉTECTÉE : Limiter les documents pour éviter surcharge contexte
      logger.warn(LogCategory.RAG, '🚨 COMBINAISON: Recherche internet + documents détectée. Limitation du contexte documents à 10K caractères.');

      // Calculer la taille totale des documents
      let totalDocumentSize = 0;
      const limitedDocuments = [];
      const maxDocumentContext = 10000; // 10K caractères maximum pour les documents

      for (const doc of documentsToUse) {
        // Utiliser la taille réelle du contenu du document
        const actualSize = (doc.content?.length || 0) + (doc.filename?.length || 0) + 100; // +100 pour les métadonnées

        if (totalDocumentSize + actualSize <= maxDocumentContext) {
          limitedDocuments.push(doc);
          totalDocumentSize += actualSize;
          logger.info(LogCategory.RAG, `Document ${doc.filename} inclus (${actualSize} chars, total: ${totalDocumentSize})`);
        } else {
          logger.warn(LogCategory.RAG, `Document ${doc.filename} exclu pour respecter limite contexte (${actualSize} chars dépasserait ${maxDocumentContext})`);
          break;
        }
      }

      documentsToUse = limitedDocuments;

      // Notifier l'utilisateur de la limitation
      if (limitedDocuments.length < documentsToUse.length) {
        const excludedCount = documentsToUse.length - limitedDocuments.length;
        const warningMessage: ClaraMessage = {
          id: generateId(),
          role: 'assistant',
          content: `⚠️ **Optimisation contexte** : Recherche internet + documents détectée. ${excludedCount} document(s) exclu(s) pour respecter la limite de 10K caractères et maintenir des performances optimales. Documents inclus : ${limitedDocuments.map(d => d.filename).join(', ')}.`,
          timestamp: new Date(),
          metadata: {
            type: 'info',
            reason: 'context_optimization',
            excludedDocuments: excludedCount,
            includedDocuments: limitedDocuments.length,
            totalContextSize: totalDocumentSize
          }
        };

        setMessages(prev => [...prev, warningMessage]);
      }

      // Mettre à jour documentsToUse avec les documents limités
      documentsToUse = limitedDocuments;
    }

    if (hasAnyDocuments) {
      logger.info(LogCategory.RAG, 'Documents selected, activating RAG', {
        count: documentsToUse.length,
        searchMode: hasSearchMode ? 'enabled' : 'disabled',
        contextLimited: hasSearchMode
      });

      // Update session config to enable RAG
      const currentConfig = sessionConfig.aiConfig || {};
      if (!currentConfig.features?.enableRAG) {
        console.log('🔧 RAG: Enabling RAG in session config');
        setSessionConfig(prev => ({
          ...prev,
          aiConfig: {
            ...currentConfig,
            features: {
              ...currentConfig.features,
              enableRAG: true
            }
          }
        }));
      }
    } else {
      // 🚀 DÉSACTIVER RAG: Si aucun document n'est sélectionné
      console.log('🔧 RAG: No documents selected, ensuring RAG is disabled');

      // 🚀 CORRECTION CRITIQUE: Vider le contexte RAG
      setRagContextForLLM('');

      const currentConfig = sessionConfig.aiConfig || {};
      if (currentConfig.features?.enableRAG) {
        console.log('🔧 RAG: Disabling RAG in session config');
        setSessionConfig(prev => ({
          ...prev,
          aiConfig: {
            ...currentConfig,
            features: {
              ...currentConfig.features,
              enableRAG: false
            }
          }
        }));
      }
    }

    if (documentsToUse && documentsToUse.length > 0) {
      try {
        logger.info(LogCategory.RAG, 'Construction directe du contexte documents');

        // 🚀 CONSTRUCTION DIRECTE UNIQUE : Plus de modes compliqués
        const selectedDocsContext = documentsToUse
          .map((doc, index) => `[Document ${index + 1}: ${doc.filename || 'Unknown'}]
${doc.content || 'No content available'}`)
          .join('\n\n---\n\n');

        ragContext = selectedDocsContext;

        // 🚀 CORRECTION CRITIQUE: Mettre à jour ragContextForLLM
        setRagContextForLLM(selectedDocsContext);

        logger.debug(LogCategory.RAG, 'Contexte documents construit', {
          documentsUsed: documentsToUse.length,
          contextLength: selectedDocsContext.length,
          source: realTimeHasDocuments ? 'selected' : 'recent_chat'
        });

        // Construction du contenu enrichi (commun aux deux modes)
        if (ragContext) {
          // Enhanced prompt with RAG context - différent selon la source
          if (realTimeHasDocuments) {
            // Documents explicitement sélectionnés
            enhancedContent = `You must answer based ONLY on the selected documents available in the conversation history. Do not use any external knowledge.

USER QUESTION: ${content}

INSTRUCTIONS:
- Answer ONLY based on the content from the selected documents in the conversation history
- If the selected documents don't contain the information needed, say "The selected documents don't contain information about this topic"
- Do not add information from your general knowledge
- Focus specifically on the content from these documents`;
          } else {
            // Documents récents de chat - approche plus flexible
            enhancedContent = `You have access to recent documents in this conversation history. Use them to provide context for your response when relevant.

USER QUESTION: ${content}

INSTRUCTIONS:
- Use the recent documents in the conversation history to provide context when relevant to the user's question
- If the documents contain relevant information, incorporate it naturally into your response
- If the documents don't contain relevant information, answer normally using your general knowledge
- You can reference the documents by name when appropriate`;
          }

          // Track used documents for metadata
          usedDocuments = documentsToUse.map(doc => ({
            id: doc.id,
            filename: doc.filename,
            collection: doc.collectionName
          }));
        }


      } catch (error) {
        console.error('❌ RAG search error:', error);
        // Continue with normal chat if RAG fails
      }
    }

    // 🚫 DÉSACTIVER LA VÉRIFICATION BLOQUANTE - Laisser l'utilisateur essayer
    // if (models.length === 0) {
    //   addErrorNotification(
    //     'No Models Available',
    //     'Please download and configure AI models before sending messages. Go to Settings → Model Manager to get started.',
    //     8000
    //   );
    //   return;
    // }

    // **NEW**: Check if current provider has any models selected
    const currentProviderModels = models.filter(m => m.provider === sessionConfig.aiConfig?.provider);
    const hasSelectedModel = sessionConfig.aiConfig?.models?.text || 
                            sessionConfig.aiConfig?.models?.vision || 
                            sessionConfig.aiConfig?.models?.code;
    
    if (currentProviderModels.length === 0 || !hasSelectedModel) {
      addErrorNotification(
        'Aucun modèle sélectionné',
        'Veuillez sélectionner au moins un modèle pour le fournisseur actuel dans les Options avancées, ou allez dans Paramètres → Gestionnaire de modèles pour télécharger des modèles.',
        8000
      );
      return;
    }

    // 🔍 ACTIVATION AUTOMATIQUE DES TOOLS POUR LA RECHERCHE INTERNET
    let enforcedConfig = sessionConfig.aiConfig;

    // Si la recherche internet est forcée, activer automatiquement les tools
    if (toolUseConfig?.forceSearchMode) {
      console.log('🔍 Mode recherche internet activé - activation automatique des tools');
      enforcedConfig = {
        ...sessionConfig.aiConfig,
        features: {
          ...sessionConfig.aiConfig.features,
          enableTools: true,  // ✅ ACTIVER LES TOOLS POUR LA RECHERCHE
          enableMCP: false    // Garder MCP désactivé pour éviter les conflits
        }
      };

      // 🎨 ÉMETTRE L'ÉVÉNEMENT DE DÉBUT DE RECHERCHE SEULEMENT SI VRAIMENT NÉCESSAIRE
      // Vérifier si le message contient des métadonnées de recherche activée
      const displayMetaMatch = content.match(/\[DISPLAY_META:(.*?)\]/);
      let shouldEmitSearchEvent = true; // Par défaut, émettre l'événement

      if (displayMetaMatch) {
        try {
          const metadata = JSON.parse(displayMetaMatch[1]);
          shouldEmitSearchEvent = metadata.hasSearchMode === true;
          console.log('🎨 Métadonnées détectées pour événement:', { hasSearchMode: metadata.hasSearchMode });
        } catch (error) {
          console.warn('Erreur parsing metadata pour événement:', error);
        }
      }

      if (shouldEmitSearchEvent) {
        console.log('🎨 Émission événement de début de recherche confirmée');
        const searchStartEvent = new CustomEvent('search-progress', {
          detail: { step: 'analyzing', sources: [], completed: false }
        });
        window.dispatchEvent(searchStartEvent);
      } else {
        console.log('🚫 Événement de recherche non émis - recherche désactivée dans les métadonnées');
      }
    } else {
      // 🚫 PAS DE RECHERCHE : S'assurer qu'aucun événement de recherche n'est émis
      console.log('🚫 Mode recherche désactivé - aucun événement de recherche');
    }

    // 🚀 NOUVEAU: Streaming hybride - Tools et streaming supportés simultanément
    // Plus besoin de désactiver les tools en mode streaming
    // Le ChatManager gère maintenant le streaming hybride
    console.log('🚀 Mode hybride: Streaming + Tools supportés simultanément');

    if (enforcedConfig.features?.enableStreaming) {
      console.log('📡 Mode streaming activé avec support des tools');

      // Garder la config actuelle sans forcer la désactivation des tools
      enforcedConfig = {
        ...enforcedConfig,
        features: {
          ...enforcedConfig.features,
          enableStreaming: true,
          // enableTools: Garder la valeur actuelle (ne pas forcer false)
          // enableMCP: Garder la valeur actuelle
        },
        autonomousAgent: {
          // Garder les paramètres autonomes actuels avec valeurs par défaut
          enabled: enforcedConfig.autonomousAgent?.enabled ?? false,
          maxRetries: enforcedConfig.autonomousAgent?.maxRetries ?? 3,
          retryDelay: enforcedConfig.autonomousAgent?.retryDelay ?? 1000,
          enableSelfCorrection: enforcedConfig.autonomousAgent?.enableSelfCorrection ?? true,
          enableToolGuidance: enforcedConfig.autonomousAgent?.enableToolGuidance ?? true,
          enableProgressTracking: enforcedConfig.autonomousAgent?.enableProgressTracking ?? true,
          maxToolCalls: enforcedConfig.autonomousAgent?.maxToolCalls ?? 10,
          confidenceThreshold: enforcedConfig.autonomousAgent?.confidenceThreshold ?? 0.7,
          enableChainOfThought: enforcedConfig.autonomousAgent?.enableChainOfThought ?? true,
          enableErrorLearning: enforcedConfig.autonomousAgent?.enableErrorLearning ?? true
        }
      };

      console.log('✅ Streaming hybride activé - Tools et streaming simultanés');
    } else {
      console.log('🛠️ Mode non-streaming - Tools disponibles');
    }

    // 🚀 Démarrer le tracking du temps de réponse pour Langfuse
    const requestStartTime = performance.now();

    // Check if this is a voice message with the prefix
    const voiceModePrefix = "Warning: You are in speech mode, make sure to reply in few lines:  \n";
    const isVoiceMessage = content.startsWith(voiceModePrefix);

    // 🔍 EXTRACTION DU MESSAGE ORIGINAL DEPUIS LES MÉTADONNÉES
    let displayContent = content;

    // Extraire le message original des métadonnées si présent
    const displayMetaMatch = content.match(/\[DISPLAY_META:(\{.*?\})\]/);
    if (displayMetaMatch) {
      try {
        const metadataStr = displayMetaMatch[1];
        // Vérifier que la chaîne JSON est complète et valide
        if (metadataStr && metadataStr.trim() && metadataStr.startsWith('{') && metadataStr.endsWith('}')) {
          const metadata = JSON.parse(metadataStr);
          if (metadata.originalMessage) {
            displayContent = metadata.originalMessage;
            console.log('🔍 Message original extrait des métadonnées pour l\'affichage');
          }
        } else {
          console.warn('🔍 Métadonnées malformées:', metadataStr);
        }
      } catch (error) {
        console.warn('🔍 Erreur extraction métadonnées:', error);
        // Fallback: extraire le message après DISPLAY_META
        const fallbackMatch = content.match(/\[DISPLAY_META:.*?\]\s*\n\s*(.*)/s);
        if (fallbackMatch && fallbackMatch[1]) {
          displayContent = fallbackMatch[1].trim();
        }
      }
    }

    // For display purposes, use the content without the voice prefix
    if (isVoiceMessage) {
      displayContent = displayContent.replace(voiceModePrefix, '');
    }
    
    // 🎯 CORRECTION : Utiliser le prompt enrichi (avec OCR) pour le LLM
    // Si enhancedPrompt est fourni, l'utiliser, sinon utiliser enhancedContent
    const aiContent = enhancedPrompt || enhancedContent;

    // 📄 CAPTURE DOCUMENTS BEFORE CLEARING SELECTION
    const documentStore = useDocumentStore.getState();
    const selectedDocuments = documentStore.selectedDocuments || [];

    // 🎯 CORRECTION : Ajouter le contenu temporaire au contexte persistant
    let persistentContent = displayContent;
    let tempDocumentContent = null;

    // Vérifier si enhancedPrompt contient du contenu temporaire (pattern OCR)
    if (enhancedPrompt && enhancedPrompt.includes('--- Content from ')) {
      const tempDocMatch = enhancedPrompt.match(/--- Content from (.+?) ---\n([\s\S]*?)\n--- End of/);
      if (tempDocMatch) {
        const docName = tempDocMatch[1];
        const docContent = tempDocMatch[2];
        persistentContent = `${displayContent}\n\n[DOCUMENT: ${docName}]`;
        tempDocumentContent = {
          name: docName,
          content: docContent
        };
        console.log('📄 Document temporaire ajouté au contexte persistant:', docName, `(${docContent.length} chars)`);
      }
    }

    // Create user message with display content (without voice prefix)
    const userMessage: ClaraMessage = {
      id: generateId(),
      role: 'user',
      content: persistentContent, // Include document reference for history
      timestamp: new Date(),
      attachments: attachments,
      metadata: {
        isVoiceMessage: isVoiceMessage, // Mark as voice message for potential styling
        usedDocuments: usedDocuments.length > 0 ? usedDocuments : undefined, // Track used documents
        ragMode: ragContext.length > 0, // Mark if RAG was used
        selectedDocuments: selectedDocuments.length > 0 ? selectedDocuments.map(doc => ({
          id: doc.id,
          filename: doc.filename,
          fileType: doc.fileType,
          collectionName: doc.collectionName
        })) : undefined // 📄 ATTACH DOCUMENTS TO MESSAGE
      }
    };

    // Add user message to state and get current conversation
    const currentMessages = [...messages, userMessage];
    setMessages(currentMessages);
    setIsLoading(true);

    // 🗑️ DÉSÉLECTION AUTOMATIQUE DES DOCUMENTS APRÈS AJOUT DU MESSAGE
    try {
      if (selectedDocuments.length > 0) {
        console.log(`📄 Désélection automatique de ${selectedDocuments.length} documents après envoi du message`);

        // Vider la sélection des documents
        documentStore.clearSelectedDocuments?.();

        console.log('✅ Documents désélectionnés automatiquement après envoi');
      }
    } catch (error) {
      console.warn('⚠️ Erreur désélection documents:', error);
    }

    // Track background activity
    if (!isVisible) {
      claraBackgroundService.incrementBackgroundActivity();
    }

    // Save user message to database (with display content only)
    try {
      await claraDB.addClaraMessage(activeSession.id, userMessage);

      // 🚀 PERSISTANCE: Sauvegarder les messages RAG si des documents sont utilisés
      if (documentsToUse && documentsToUse.length > 0) {
        console.log('📄 Checking RAG documents for persistence...');

        for (const doc of documentsToUse) {
          // 🔧 CORRECTION DUPLICATION RAG : Vérifier dans l'état local ET la DB
          const existingInLocal = messages.find(msg =>
            msg.metadata?.type === 'rag' &&
            msg.metadata?.documentId === doc.id &&
            msg.role === 'system'
          );

          const sessionFromDB = await claraDB.getClaraSession(activeSession.id);
          const existingInDB = sessionFromDB?.messages?.find(msg =>
            msg.metadata?.type === 'rag' &&
            msg.metadata?.documentId === doc.id &&
            msg.role === 'system'
          );

          if (!existingInLocal && !existingInDB) {
            const ragMessage: ClaraMessage = {
              id: `rag-${Date.now()}-${doc.id}`,
              role: 'system',
              content: `📄 Document RAG: ${doc.filename || 'Unknown'}\n\n${doc.content || 'No content available'}`,
              timestamp: new Date(),
              metadata: {
                type: 'rag',
                documentId: doc.id,
                documentName: doc.filename || 'Unknown',
                documentType: doc.fileType || 'unknown',
                ragMode: 'fast', // 🚀 CORRECTION: Mode rapide par défaut
                isRagDocument: true
              }
            };

            // Sauvegarder le message RAG dans la base de données
            await claraDB.addClaraMessage(activeSession.id, ragMessage);

            // Ajouter le message RAG à l'état local pour l'affichage immédiat
            setMessages(prev => [...prev, ragMessage]);
            console.log(`✅ Saved new RAG document: ${doc.filename}`);
          } else {
            console.log(`ℹ️ RAG document already exists: ${doc.filename}`);
          }
        }

        console.log(`✅ Processed ${documentsToUse.length} RAG documents`);
      }

      // 🎯 NOUVEAU : Sauvegarder le document temporaire comme message RAG
      if (tempDocumentContent) {
        const tempRAGMessage: ClaraMessage = {
          id: `rag-temp-${Date.now()}`,
          role: 'system',
          content: `📄 Document temporaire: ${tempDocumentContent.name}\n\n${tempDocumentContent.content}`,
          timestamp: new Date(),
          metadata: {
            type: 'rag',
            documentId: `temp_${Date.now()}`,
            documentName: tempDocumentContent.name,
            documentType: 'temporary',
            ragMode: 'fast',
            isRagDocument: true,
            isTemporary: true
          }
        };

        // Sauvegarder le message RAG dans la base de données
        await claraDB.addClaraMessage(activeSession.id, tempRAGMessage);

        // Ajouter le message RAG à l'état local pour l'affichage immédiat
        setMessages(prev => [...prev, tempRAGMessage]);
        console.log(`✅ Document temporaire sauvegardé comme message RAG: ${tempDocumentContent.name}`);
      }
    } catch (error) {
      console.error('Failed to save user message or RAG documents:', error);
    }

    // Create a temporary streaming message for the assistant
    const streamingMessageId = generateId();
    const streamingMessage: ClaraMessage = {
      id: streamingMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date(),
      metadata: {
        isStreaming: true,
        model: `${enforcedConfig.provider}:${enforcedConfig.models.text}`,
        temperature: enforcedConfig.parameters.temperature
      }
    };

    // Add the streaming message to state
    setMessages(prev => [...prev, streamingMessage]);

    // 🎯 TIMEOUT INTELLIGENT : Attendre 30s avant erreur (permet chargement modèle)
    // Pas d'indicateur prématuré - si le streaming commence, tout va bien
    // Si pas de réponse après 30s, alors vraie erreur

    // 🚀 TIMEOUT DE SÉCURITÉ : Éviter que l'interface reste bloquée (30s pour permettre chargement modèle)
    const streamingTimeout = setTimeout(() => {
      console.warn('⚠️ Timeout de streaming détecté après 30s - déblocage de l\'interface');
      setIsLoading(false);
      setMessages(prev => prev.map(msg =>
        msg.id === streamingMessageId
          ? {
              ...msg,
              content: msg.content || 'Timeout de génération - veuillez réessayer.',
              metadata: {
                ...msg.metadata,
                isStreaming: false,
                timeout: true
              }
            }
          : msg
      ));
    }, 120000); // 2 minutes de timeout

    try {
      // 🚀 NOUVELLE LOGIQUE : Utiliser messages compressés pour LLM, originaux pour affichage
      let fullConversationHistory: ClaraMessage[] = [];

      try {
        const sessionFromDB = await claraDB.getClaraSession(activeSession.id);
        if (sessionFromDB) {
          // 🔍 Vérifier si la session a des messages compressés séparés
          const hasCompressedForLLM = (sessionFromDB as any).compressedMessages &&
                                      (sessionFromDB as any).isCompressed;

          if (hasCompressedForLLM) {
            // ✅ Utiliser les messages compressés pour le LLM
            fullConversationHistory = (sessionFromDB as any).compressedMessages;
            console.log('🚀 Using compressed messages for LLM:', fullConversationHistory.length, 'messages (compressed)');

            // Mettre à jour l'état pour refléter la compression
            setMessagesForLLM(fullConversationHistory);
            setHasCompressedVersion(true);

            // Ajouter le nouveau message utilisateur
            if (!fullConversationHistory.some(m => m.id === userMessage.id)) {
              fullConversationHistory = [...fullConversationHistory, userMessage];
            }
          } else if (sessionFromDB.messages) {
            // 📚 Utiliser les messages normaux
            const lastMessageInDB = sessionFromDB.messages[sessionFromDB.messages.length - 1];
            const isUserMessageAlreadyInDB = lastMessageInDB &&
              lastMessageInDB.role === 'user' &&
              lastMessageInDB.content === userMessage.content &&
              Math.abs(lastMessageInDB.timestamp.getTime() - userMessage.timestamp.getTime()) < 5000;

            if (isUserMessageAlreadyInDB) {
              fullConversationHistory = sessionFromDB.messages;
              console.log('📚 Using DB history (user message already saved):', fullConversationHistory.length, 'messages');
            } else {
              fullConversationHistory = [...sessionFromDB.messages, userMessage];
              console.log('📚 Using DB history + new user message:', fullConversationHistory.length, 'messages');
            }
          } else {
            fullConversationHistory = currentMessages;
            console.log('⚠️ Using local state as fallback for conversation history');
          }
        } else {
          fullConversationHistory = currentMessages;
        }
      } catch (dbError) {
        console.error('Failed to load conversation history from DB:', dbError);
        fullConversationHistory = currentMessages;
      }

      // 🧠 GESTION INTELLIGENTE DU CONTEXTE
      // Utiliser la fonction utilitaire pour calculer la limite de tokens

      const currentModelId = enforcedConfig.models.text || 'unknown';
      const maxContextTokens = getModelContextLimit(currentModelId);
      const reservedTokens = Math.min(2000, Math.floor(maxContextTokens * 0.2)); // 20% réservé pour la réponse
      const availableTokens = maxContextTokens - reservedTokens;

      // Fonction pour estimer les tokens (amélioration de la précision)
      const estimateTokens = (text: string): number => {
        if (!text) return 0;

        // Estimation plus précise selon le contenu
        const hasCode = /```|function|class|import|export/.test(text);
        const hasFrench = /[àâäéèêëïîôöùûüÿç]/.test(text);
        const hasNumbers = /\d+/.test(text);

        let ratio = 4; // Base : 1 token ≈ 4 caractères

        if (hasCode) ratio = 3.5; // Code plus dense en tokens
        if (hasFrench) ratio = 4.2; // Français légèrement moins dense
        if (hasNumbers) ratio = 3.8; // Nombres plus denses

        return Math.ceil(text.length / ratio);
      };

      // Fonction pour optimiser l'historique de conversation
      const optimizeConversationHistory = (messages: ClaraMessage[]): ClaraMessage[] => {
        // 🚀 CORRECTION CRITIQUE : TOUJOURS GARDER LES DOCUMENTS RAG
        // Séparer les messages RAG des autres messages
        // 🎯 EXCEPTION : Exclure les documents temporaires si enhancedPrompt existe (éviter doublon)
        const ragMessages = messages.filter(msg =>
          msg.role === 'system' &&
          msg.metadata?.type === 'rag' &&
          !(enhancedPrompt && msg.metadata?.isTemporary) // Exclure temporaires si enhancedPrompt existe
        );

        // Filtrer les autres messages (exclure les messages système non-RAG)
        const filteredMessages = messages.filter(msg =>
          msg.role !== 'system' ||
          (msg.role === 'system' && msg.metadata?.type === 'rag')
        );

        console.log(`🔍 Optimisation: ${messages.length} messages total → ${ragMessages.length} documents RAG à préserver`);

        if (filteredMessages.length === 0) return [];

        // 🚀 FORCER L'INCLUSION DES DOCUMENTS RAG EN PREMIER
        const selectedMessages: ClaraMessage[] = [...ragMessages]; // ✅ TOUJOURS INCLURE TOUS LES DOCUMENTS RAG
        let totalTokens = ragMessages.reduce((sum, msg) => sum + estimateTokens(msg.content || ''), 0);

        console.log(`🚀 Documents RAG forcés: ${ragMessages.length} documents, ${totalTokens} tokens`);

        // Ensuite, ajouter les autres messages dans la limite disponible
        const nonRagMessages = filteredMessages.filter(msg =>
          !(msg.role === 'system' && msg.metadata?.type === 'rag')
        );

        // 🔧 CORRECTION : Toujours garder le dernier message non-RAG
        if (nonRagMessages.length > 0) {
          const lastMessage = nonRagMessages[nonRagMessages.length - 1];
          const otherMessages = nonRagMessages.slice(0, -1);

          totalTokens += estimateTokens(lastMessage?.content || '');
          selectedMessages.push(lastMessage);

          // Parcourir les messages de la fin vers le début
        for (let i = otherMessages.length - 1; i >= 0; i--) {
          const message = otherMessages[i];
          const messageTokens = estimateTokens(message.content);

          // Vérifier si on peut ajouter ce message
          if (totalTokens + messageTokens <= availableTokens) {
            totalTokens += messageTokens;
            selectedMessages.unshift(message);
          } else {
            // Si le message est trop gros, essayer de le résumer ou le tronquer
            if (message.role === 'assistant' && messageTokens > 1000) {
              // Résumer les longues réponses de l'assistant
              const summary = message.content.slice(0, 500) + '\n\n[...réponse tronquée pour économiser le contexte...]';
              const summaryTokens = estimateTokens(summary);

              if (totalTokens + summaryTokens <= availableTokens) {
                totalTokens += summaryTokens;
                selectedMessages.unshift({
                  ...message,
                  content: summary,
                  metadata: { ...message.metadata, truncated: true }
                });
              }
            } else if (message.role === 'user' && messageTokens > 2000) {
              // 🚫 NE PAS TRONQUER LES DOCUMENTS - Les garder intacts pour le LLM
              console.log(`🚫 Message utilisateur volumineux gardé intact: ${messageTokens} tokens`);
              // Forcer l'ajout du message complet même si ça dépasse
              totalTokens += messageTokens;
              selectedMessages.unshift({
                ...message,
                content: message.content, // ✅ GARDER LE CONTENU COMPLET
                metadata: { ...message.metadata, truncated: false } // ✅ PAS TRONQUÉ
              });
            }
            // Si même tronqué ça ne rentre pas, on s'arrête
            break;
          }
        }
        } // ✅ FERMETURE DU IF (nonRagMessages.length > 0)

        // 🔧 CORRECTION : Ne pas modifier l'ordre, les messages sont déjà dans le bon ordre
        // selectedMessages a été construit avec unshift(), donc l'ordre est déjà chronologique
        return selectedMessages;
      };

      // ⏱️ MESURE DE PERFORMANCE : Temps de traitement du contexte
      const contextOptimizationStart = performance.now();
      const conversationHistory = optimizeConversationHistory(fullConversationHistory);
      const contextOptimizationTime = performance.now() - contextOptimizationStart;

      const totalTokensUsed = conversationHistory.reduce((sum, msg) => sum + estimateTokens(msg.content), 0);

      console.log('🧠 Contexte intelligent optimisé:', {
        totalMessages: fullConversationHistory.length,
        selectedMessages: conversationHistory.length,
        estimatedTokens: totalTokensUsed,
        maxTokens: availableTokens,
        utilization: `${Math.round((totalTokensUsed / availableTokens) * 100)}%`,
        messageBreakdown: conversationHistory.reduce((acc, msg) => {
          acc[msg.role] = (acc[msg.role] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        truncatedMessages: conversationHistory.filter(msg => msg.metadata?.truncated).length,
        optimizationTime: `${contextOptimizationTime.toFixed(2)}ms`,
        model: currentModelId,
        contextLimit: maxContextTokens
      });

      // Get system prompt (provider-specific or fallback to default)
      const currentProvider = providers.find(p => p.id === enforcedConfig.provider);
      const baseSystemPrompt = enforcedConfig.systemPrompt ||
                          (currentProvider ? getDefaultSystemPrompt(currentProvider) :
                          'Tu es WeMa IA, un assistant intelligent et utile. IMPORTANT: Réponds TOUJOURS en français sauf si l\'utilisateur demande explicitement une autre langue. Sois direct, précis et évite les réflexions inutiles. Tu as accès à l\'historique complet de cette conversation.');

      // 🇫🇷 AJOUT CONTEXTE CONVERSATION pour que le LLM sache combien de messages il a
      const conversationContext = conversationHistory.length > 0
        ? `\n\nCONTEXTE DE CONVERSATION: Tu as accès à ${conversationHistory.length} messages précédents dans cette conversation. Utilise cet historique pour répondre de manière cohérente et contextuelle.`
        : '\n\nCONTEXTE DE CONVERSATION: Ceci est le début d\'une nouvelle conversation.';

      // 🚀 CORRECTION TRIPLE INJECTION RAG - SUPPRESSION COMPLÈTE
      // Les documents RAG sont UNIQUEMENT dans l'historique comme messages système
      // AUCUNE injection dans le system prompt pour éviter la duplication

      // Vérifier si des documents RAG sont présents dans l'historique (pour debug seulement)
      const ragMessagesInHistory = conversationHistory.filter(msg =>
        msg.role === 'system' && msg.metadata?.type === 'rag'
      );

      if (ragMessagesInHistory.length > 0) {
        console.log(`📚 ${ragMessagesInHistory.length} documents RAG trouvés dans l'historique (pas d'injection dans system prompt)`);
      }

      // 🚀 SYSTEM PROMPT PROPRE - Sans duplication RAG
      const systemPrompt = baseSystemPrompt + conversationContext;

      // 🚀 MODE RAPIDE SIMPLIFIÉ : Contexte RAG uniquement dans l'historique
      // Plus besoin de ragContext externe, tout est dans les messages
      setRagContextForLLM('');
      
      // 🚀 STREAMING SIMPLE: Laisser MessageBubble gérer le parsing et l'affichage
      const optimizedStreamingHandler = createOptimizedContentHandler((accumulatedContent: string) => {
        try {
          // 🎯 SIMPLE: Juste passer le contenu, MessageBubble s'occupe du parsing
          setMessages(prev => prev.map(msg =>
            msg.id === streamingMessageId
              ? {
                  ...msg,
                  content: accumulatedContent,
                  metadata: {
                    ...msg.metadata,
                    isStreaming: true
                  }
                }
              : msg
          ));
        } catch (error) {
          console.error('❌ Erreur dans le streaming handler:', error);
        }
      });

      // 🧠 VÉRIFICATION INTELLIGENTE DU CONTEXTE AVANT ENVOI
      try {
        // 🚀 SUPPRIMÉ: Nettoyage préventif - prévention à la source maintenant

        const { intelligentContextManager } = await import('../services/api/context/IntelligentContextManager');

        // Analyser le contexte avec le message actuel
        const fullContext = [...conversationHistory, userMessage];
        const ragContextStr = ragContextForLLM || '';

        // 🎯 DÉTECTION ROBUSTE DU MODÈLE ACTUEL
        const currentModelId = sessionConfig.aiConfig?.models?.text || '';
        const currentModel = (currentModelId.includes('32b') || currentModelId.includes('32B')) ? 'primary' : 'fallback';

        console.log(`🎯 Modèle détecté: ${currentModel} (ID: ${currentModelId})`);

        const contextAnalysis = intelligentContextManager.analyzeContext(
          fullContext,
          ragContextStr,
          currentModel
        );

        console.log('🧠 Analyse contexte avant envoi:', {
          totalChars: contextAnalysis.totalChars,
          currentModel,
          recommendedAction: contextAnalysis.recommendedAction,
          needsUserChoice: contextAnalysis.modelSwitchNeeded || contextAnalysis.compressionNeeded
        });

        // 🚀 BASCULEMENT AUTOMATIQUE: Si changement de modèle nécessaire, le faire automatiquement
        if (contextAnalysis.recommendedAction === 'switch_model' && contextAnalysis.modelSwitchNeeded) {
          console.log('🚀 Basculement automatique vers 14B - contexte trop volumineux pour 32B');

          // 🔍 RECHERCHE ROBUSTE DU MODÈLE 14B - COMPATIBLE LM STUDIO
          const model14B = models.find(m => {
            const modelId = m.id.toLowerCase();
            const modelName = m.name.toLowerCase();
            return (
              // LM Studio: qwen3-14b (sans -optimized)
              modelId === 'qwen3-14b' ||
              modelId === 'qwen-14b' ||
              // Ollama: qwen3-14b-optimized
              modelId.includes('qwen3-14b-optimized') ||
              modelId.includes('qwen-14b-optimized') ||
              // Recherche générale 14B
              (modelId.includes('qwen') && modelId.includes('14b')) ||
              (modelName.includes('qwen') && modelName.includes('14b'))
            );
          });

          if (model14B) {
            console.log(`⚡ Basculement automatique vers: ${model14B.id}`);

            // Changer le modèle dans la configuration IMMÉDIATEMENT
            setSessionConfig(prev => ({
              ...prev,
              aiConfig: {
                ...prev.aiConfig,
                models: {
                  ...prev.aiConfig.models,
                  text: model14B.id
                }
              }
            }));

            // 🔄 NOTIFICATION DE BASCULEMENT AUTOMATIQUE
            addInfoNotification(
              'Basculement automatique vers 14B',
              `Contexte volumineux détecté (${Math.round(contextAnalysis.totalChars / 1000)}K chars). Basculement automatique vers ${model14B.id} pour optimiser les performances.`,
              4000
            );

            // Créer une configuration mise à jour pour ce message
            const updatedSessionConfig = {
              ...sessionConfig,
              aiConfig: {
                ...sessionConfig.aiConfig,
                models: {
                  ...sessionConfig.aiConfig.models,
                  text: model14B.id
                }
              }
            };

            // Utiliser la configuration mise à jour avec le modèle 14B
            enforcedConfig = updatedSessionConfig.aiConfig;

            // 🔄 METTRE À JOUR LE MESSAGE DE STREAMING AVEC LE NOUVEAU MODÈLE
            setMessages(prev => prev.map(msg =>
              msg.id === streamingMessageId
                ? {
                    ...msg,
                    metadata: {
                      ...msg.metadata,
                      model: `${enforcedConfig.provider}:${enforcedConfig.models.text}`
                    }
                  }
                : msg
            ));

            console.log('✅ Modèle basculé vers 14B pour ce message');

            // 🚀 PRÉCHARGEMENT IMMÉDIAT du modèle 14B
            try {
              const currentProvider = providers.find(p => p.id === sessionConfig.aiConfig?.provider);
              if (currentProvider && currentProvider.type === 'lmstudio') {
                console.log(`🔄 Préchargement immédiat du modèle 14B: ${model14B.id}`);
                await lmStudioPreloader.forcePreload(model14B.id);
              }
            } catch (error) {
              console.warn('⚠️ Préchargement 14B échoué (non-critique):', error);
            }
          } else {
            console.warn('⚠️ Aucun modèle 14B trouvé pour le basculement automatique');
            console.log('🔍 Modèles disponibles:', models.map(m => ({ id: m.id, name: m.name })));
            // Continuer avec le modèle actuel
          }
        }

        // Si compression nécessaire pour le 14B, afficher le message interactif
        if (contextAnalysis.recommendedAction === 'compress' && contextAnalysis.compressionNeeded) {
          console.log('🧠 Contexte trop volumineux même pour 14B - affichage du message interactif');

          // Ajouter le message interactif et arrêter l'envoi
          await addContextManagementMessage(
            contextAnalysis.totalChars,
            currentModel,
            contextAnalysis.recommendedAction as 'switch_model' | 'compress' | 'new_conversation'
          );

          // Arrêter l'envoi du message - l'utilisateur doit choisir
          setIsLoading(false);
          // 🎯 SÉCURITÉ : Forcer la fermeture du ProcessingIndicator via événement
          window.dispatchEvent(new CustomEvent('force-close-processing-indicator'));
          return;
        }

        // Si pas d'action nécessaire, continuer normalement
        console.log('🧠 Contexte OK - envoi du message');

      } catch (contextError) {
        console.warn('⚠️ Erreur analyse contexte, envoi normal:', contextError);
      }

      // 🚀 CORRECTION CRITIQUE: Ajouter le contexte RAG au system prompt
      let finalSystemPrompt = systemPrompt;
      if (ragContext && ragContext.trim()) {
        finalSystemPrompt = `${systemPrompt}\n\n📄 DOCUMENTS DISPONIBLES:\n${ragContext}`;
        console.log(`🔧 RAG Context ajouté au system prompt: ${ragContext.length} chars`);
      }

      // Send message with streaming callback and conversation context
      // Use aiContent (with voice prefix) for AI processing
      // IMPORTANT: Use enforcedConfig to ensure streaming mode settings are applied
      const aiMessage = await claraApiService.sendChatMessage(
        aiContent, // Send full content including voice prefix to AI
        enforcedConfig, // Use enforced config instead of original sessionConfig.aiConfig
        attachments,
        finalSystemPrompt, // 🚀 CORRECTION: Utiliser le system prompt avec RAG
        conversationHistory, // Pass conversation context
        // 🚀 OPTIMISATION: Utiliser le handler optimisé pour réduire les re-renders
        optimizedStreamingHandler,
        activeSession.id, // 🚀 ISOLATION: Passer l'ID de session pour l'isolation
        // 🛠️ NOUVEAUX PARAMÈTRES TOOL USE
        toolUseConfig,
        onSearchProgress
      );
      
      // Replace the streaming message with the final message
      // 🎯 SIMPLE: Juste remplacer le message avec le contenu final
      const finalMessage = {
        ...aiMessage,
        id: streamingMessageId, // Keep the same ID
        metadata: {
          ...aiMessage.metadata,
          isStreaming: false, // 🎯 Arrêter le streaming
          usedDocuments: usedDocuments.length > 0 ? usedDocuments : undefined,
          ragMode: ragMessagesInHistory.length > 0
        }
      };

      setMessages(prev => prev.map(msg => {
        if (msg.id === streamingMessageId) {
          console.log('🎯 Finalisation message - isStreaming mis à false pour supprimer "I am thinking..."');
          return finalMessage;
        }
        return msg;
      }));

      // 🚀 NETTOYER LES TIMEOUTS
      clearTimeout(streamingTimeout);

      // Update latest AI response for auto TTS
      setLatestAIResponse(finalMessage.content);
      setAutoTTSTrigger({
        text: finalMessage.content,
        timestamp: Date.now()
      });

      // Save AI message to database
      try {
        await claraDB.addClaraMessage(activeSession.id, finalMessage);

        // 🎨 FERMER L'ANIMATION DE RECHERCHE quand la réponse est terminée
        if (isSearching) {
          // Récupérer les sources depuis les métadonnées de la réponse
          const searchSources = aiMessage.metadata?.searchMetadata?.sources || [];
          if (searchSources.length > 0) {
            console.log('🎨 Sources de recherche trouvées:', searchSources);
          }

          // 🚀 NOUVEAU : Émettre un événement de fermeture forcée
          setTimeout(() => {
            const forceCloseEvent = new CustomEvent('search-force-close');
            window.dispatchEvent(forceCloseEvent);
            handleSearchComplete();
            console.log('🎨 Animation de recherche fermée après réponse complète');
          }, 1500); // Délai réduit pour fermeture plus rapide
        }

        // 📄 CONTEXTE DE CONVERSATION ENRICHI
        // Les documents utilisés sont maintenant intégrés dans le contexte de la conversation
        // Ils restent attachés visuellement au message pour la traçabilité
        // Mais seront désélectionnés du chat pour permettre de nouvelles sélections
        if (documentsToUse && documentsToUse.length > 0) {
          console.log(`📄 Documents intégrés au contexte de conversation: ${documentsToUse.map(d => d.filename).join(', ')}`);
        }

        // 🗑️ DÉSÉLECTION AUTOMATIQUE APRÈS RÉPONSE COMPLÈTE
        try {
          const documentStore = useDocumentStore.getState();
          const selectedDocs = documentStore?.selectedDocuments || [];

          if (selectedDocs.length > 0) {
            console.log(`🗑️ Désélection automatique de ${selectedDocs.length} documents après réponse complète`);
            documentStore.clearSelectedDocuments();
            console.log('✅ Documents désélectionnés - interface nettoyée pour nouvelle sélection');
          }
        } catch (error) {
          console.warn('⚠️ Erreur désélection automatique documents:', error);
        }

        // 🚀 COMPRESSION POST-RÉPONSE SIMPLE (documents déjà en base)
        try {
          const { chatManager } = await import('../services/api/chat/ChatManager');
          const updatedHistory = [...conversationHistory, finalMessage];

          // Déclencher la compression (contexte RAG déjà en base de données)
          await chatManager.schedulePostResponseCompression(
            updatedHistory,
            finalMessage,
            undefined, // compressionAnalysis
            currentSession?.id
          );
        } catch (compressionError) {
          console.warn('⚠️ Erreur compression post-réponse:', compressionError);
        }

        // Enhanced notification for background operation
        if (!isVisible) {
          // More prominent notification when Clara is in background
          // Use display content for notifications (without voice prefix)
          addBackgroundCompletionNotification(
            'Clara Response Ready',
            `Clara has finished responding to: "${displayContent.slice(0, 40)}${displayContent.length > 40 ? '...' : ''}"`
          );
          // Track background notification creation
          claraBackgroundService.onBackgroundNotificationCreated();
        } else {
          // Standard notification when Clara is visible
          addCompletionNotification(
            'Chat Response Complete',
            isVoiceMessage ? 'Clara has finished responding to your voice message.' : 'Clara has finished responding to your message.',
            4000
          );
        }
      } catch (error) {
        console.error('Failed to save AI message:', error);
      }

      // Update session title if it's still "New Chat"
      if (activeSession.title === 'New Chat') {
        // Use display content for session title (without voice prefix)
        const newTitle = displayContent.slice(0, 50) + (displayContent.length > 50 ? '...' : '');
        const updatedSession = {
          ...activeSession,
          title: newTitle,
          messages: [...currentMessages, finalMessage],
          updatedAt: new Date()
        };

        setCurrentSession(updatedSession);
        
        // Update in database and sessions list
        try {
          await claraDB.updateClaraSession(activeSession.id, { title: newTitle });
          setSessions(prev => prev.map(s =>
            s.id === activeSession.id ? { ...s, title: newTitle } : s
          ));
        } catch (error) {
          console.error('Failed to update session title:', error);
        }
      }

    } catch (error) {
      console.error('Error generating AI response:', error);

      // 🚀 NETTOYER LES TIMEOUTS EN CAS D'ERREUR
      clearTimeout(streamingTimeout);

      // Check if this is an abort error (user stopped the stream)
      const isAbortError = error instanceof Error && (
        error.message.includes('aborted') ||
        error.message.includes('BodyStreamBuffer was aborted') ||
        error.message.includes('AbortError') ||
        error.name === 'AbortError'
      );
      
      if (isAbortError) {
        console.log('Stream was aborted by user, preserving streamed content');
        
        // Just mark the current streaming message as complete, preserving all streamed content
        setMessages(prev => prev.map(msg => 
          msg.id === streamingMessageId 
            ? { 
                ...msg, 
                metadata: {
                  ...msg.metadata,
                  isStreaming: false,
                  aborted: true
                }
              }
            : msg
        ));

        // Save the aborted message to database with its current content
        try {
          // Get the current message with streamed content from state
          setMessages(prev => {
            const currentMessage = prev.find(msg => msg.id === streamingMessageId);
            if (currentMessage && currentSession) {
              const abortedMessage = {
                ...currentMessage,
                content: currentMessage.content, // 🎯 GARDER le contenu tel quel
                metadata: {
                  ...currentMessage.metadata,
                  isStreaming: false,
                  aborted: true
                }
              };
              // Save to database asynchronously
              claraDB.addClaraMessage(currentSession.id, abortedMessage).catch(dbError => {
                console.error('Failed to save aborted message:', dbError);
              });
            }
            return prev; // Don't actually modify the state here, just access it
          });
        } catch (dbError) {
          console.error('Failed to save aborted message:', dbError);
        }
      } else {
        // Check for specific vision model error
        const isVisionError = error instanceof Error && (
          error.message.includes('image input is not supported') ||
          error.message.includes('vision not supported') ||
          error.message.includes('multimodal not supported') ||
          error.message.includes('images are not supported')
        );
        
        // Check if user sent images but has vision error
        const hasImages = attachments && attachments.some(att => att.type === 'image');
        
        if (isVisionError || (hasImages && error instanceof Error && error.message.includes('server'))) {
          console.log('Vision model error detected - providing helpful guidance');
          
          // Get suggested vision models for better error message
          const suggestedModels = getSuggestedVisionModels();
          const modelSuggestions = suggestedModels.length > 0 
            ? `\n\n**Available vision models for ${sessionConfig.aiConfig?.provider}:**\n${suggestedModels.map(m => `• ${m.name}`).join('\n')}`
            : '\n\n**Note:** No vision models found for the current provider. You may need to download vision models first.';
          
          const errorMessage: ClaraMessage = {
            id: streamingMessageId,
            role: 'assistant',
            content: `I see you've shared an image, but the current model doesn't support image processing.${modelSuggestions}

**To fix this:**
1. Open **Advanced Options** (click the ⚙️ gear icon)
2. Select a **Vision Model** from the dropdown${suggestedModels.length > 0 ? ` (try ${suggestedModels[0].name})` : ''}
3. Or download vision models from **Settings → Model Manager**

**Alternative:** Switch to **Tools Mode** which can automatically select appropriate models for different tasks.

Would you like me to help with text-only responses for now?`,
            timestamp: new Date(),
            metadata: {
              error: error instanceof Error ? error.message : 'Vision model not configured',
              isStreaming: false,
              isVisionError: true,
              suggestedModels: suggestedModels.map(m => m.id)
            }
          };
          
          setMessages(prev => prev.map(msg => 
            msg.id === streamingMessageId ? errorMessage : msg
          ));

          // Add specific error notification for vision issues
          addErrorNotification(
            'Vision Model Required',
            'Please configure a vision/multimodal model to process images.',
            8000
          );
        } else {
          // Only show generic error message for actual errors (not user aborts)
          const errorMessage: ClaraMessage = {
            id: streamingMessageId,
            role: 'assistant',
            content: 'WeMa IA a rencontré une erreur lors du traitement de votre demande. Veuillez réessayer.\n\nDétails : '+(error instanceof Error ? error.message : 'Erreur inconnue'),
            timestamp: new Date(),
            metadata: {
              error: error instanceof Error ? error.message : 'Failed to generate response',
              isStreaming: false
            }
          };
          
          setMessages(prev => prev.map(msg => 
            msg.id === streamingMessageId ? errorMessage : msg
          ));

          // Add error notification
          addErrorNotification(
            'Chat Error',
            'Failed to generate response. Please try again.',
            6000
          );
        }

        // Save error message to database
        try {
          // Get the current error message from state to save
          setMessages(prev => {
            const currentMessage = prev.find(msg => msg.id === streamingMessageId);
            if (currentMessage && currentSession) {
              // 🎯 GARDER le contenu tel quel
              const cleanedMessage = {
                ...currentMessage,
                content: currentMessage.content, // 🎯 GARDER le contenu tel quel
                metadata: {
                  ...currentMessage.metadata,
                  isStreaming: false
                }
              };
              claraDB.addClaraMessage(currentSession.id, cleanedMessage).catch(dbError => {
                console.error('Failed to save error message:', dbError);
              });
            }
            return prev; // Don't modify state, just access it
          });
        } catch (dbError) {
          console.error('Failed to save error message:', dbError);
        }
      }
    } finally {
      setIsLoading(false);

      // 🎯 SÉCURITÉ : Toujours forcer la fermeture du ProcessingIndicator
      window.dispatchEvent(new CustomEvent('force-close-processing-indicator'));

      // 🚀 SUPPRIMÉ: Nettoyage automatique - prévention à la source maintenant

      // 🗑️ SUPPRIMÉ : Désélection déplacée plus tôt dans le processus

      // Always decrement background activity when operation completes
      if (!isVisible) {
        claraBackgroundService.decrementBackgroundActivity();
      }

      // 🚀 Calculer et envoyer les métriques à Langfuse Monitor
      const responseTime = (performance.now() - requestStartTime) / 1000; // Convertir en secondes
      try {
        if ((window as any).updateLangfuseMetrics) {
          (window as any).updateLangfuseMetrics(responseTime);
        }
      } catch (error) {
        console.warn('⚠️ Failed to update Langfuse metrics:', error);
      }
    }
  }, [currentSession, messages, sessionConfig, isVisible, models]);

  // Handle session selection
  const handleSelectSession = useCallback(async (sessionId: string) => {
    if (currentSession?.id === sessionId) return;

    try {
      const session = await claraDB.getClaraSession(sessionId);
      if (session) {
        // 🚀 SUPPRIMÉ: Nettoyage avant chargement - prévention à la source maintenant
        setCurrentSession(session);
        setMessages(session.messages);

        // 🎯 SUPPRIMÉ : Plus de migration nécessaire

        // 🚀 CORRECTION : Vérifier si la session a des messages compressés
        const sessionWithCompression = session as any;
        if (sessionWithCompression.compressedMessages && sessionWithCompression.isCompressed) {
          setMessagesForLLM(sessionWithCompression.compressedMessages);
          setHasCompressedVersion(true);
          console.log('🚀 Session chargée avec compression existante:', sessionWithCompression.compressedMessages.length, 'messages compressés');
        } else {
          // 🔍 FALLBACK : Vérifier dans localStorage (ancien système)
          const compressionKey = `compressed-messages-${sessionId}`;
          const storedCompression = localStorage.getItem(compressionKey);
          if (storedCompression) {
            try {
              const compressionData = JSON.parse(storedCompression);
              if (compressionData.compressedMessages && compressionData.isCompressed) {
                setMessagesForLLM(compressionData.compressedMessages);
                setHasCompressedVersion(true);
                console.log('🚀 Session chargée avec compression depuis localStorage:', compressionData.compressedMessages.length, 'messages compressés');
              } else {
                setMessagesForLLM(session.messages);
                setHasCompressedVersion(false);
              }
            } catch (error) {
              console.warn('⚠️ Erreur lecture compression localStorage:', error);
              setMessagesForLLM(session.messages);
              setHasCompressedVersion(false);
            }
          } else {
            setMessagesForLLM(session.messages);
            setHasCompressedVersion(false);
          }
        }

        // 🚀 ISOLATION : Configurer le document store pour cette session
        const documentStore = useDocumentStore.getState();
        if (documentStore.setSession) {
          documentStore.setSession(sessionId);
          console.log('🔄 Document store configured for session:', sessionId);
        }

        // 🚀 ISOLATION : Initialiser le mode RAG pour cette session
        const ragMode = getRagModeForSession(sessionId, session.config);
        setSessionConfig(prev => ({
          ...prev,
          ragMode
        }));

        logger.info(LogCategory.SESSIONS, `🚀 Session loaded with RAG mode: ${ragMode} for session ${sessionId}`);
      }
    } catch (error) {
      console.error('Failed to load session:', error);
    }
  }, [currentSession]);

  // Handle new chat creation
  const handleNewChat = useCallback(async () => {
    const newSession = await createNewSession();
    setCurrentSession(newSession);
    setMessages([]);

    // 🚀 ISOLATION : Configurer le document store pour la nouvelle session
    const documentStore = useDocumentStore.getState();
    if (documentStore.setSession) {
      documentStore.setSession(newSession.id);
      console.log('🔄 Document store configured for new session:', newSession.id);
    }
  }, [createNewSession]);

  // Handle session actions
  const handleSessionAction = useCallback(async (sessionId: string, action: 'star' | 'archive' | 'delete') => {
    try {
      if (action === 'delete') {
        console.log('Deleting session:', sessionId);
        await claraDB.deleteClaraSession(sessionId);

        // 🚀 NETTOYAGE : Supprimer le mode RAG de cette session
        SessionRagModeService.cleanupSessionRagMode(sessionId);

        // Update sessions list immediately
        setSessions(prev => {
          const updated = prev.filter(s => s.id !== sessionId);
          console.log('Updated sessions after delete:', updated.map(s => ({ id: s.id, title: s.title })));
          return updated;
        });
        
        // If we deleted the current session, create a new one or select another
        if (currentSession?.id === sessionId) {
          console.log('Deleted current session, selecting new one...');
          const remainingSessions = sessions.filter(s => s.id !== sessionId);
          if (remainingSessions.length > 0) {
            // Select the most recent remaining session
            const nextSession = await claraDB.getClaraSession(remainingSessions[0].id);
            if (nextSession) {
              setCurrentSession(nextSession);
              setMessages(nextSession.messages);
              console.log('Selected next session:', nextSession.title);
            }
          } else {
            // No sessions left, create a new one
            await handleNewChat();
          }
        }
      } else {
        const updates = action === 'star' 
          ? { isStarred: !sessions.find(s => s.id === sessionId)?.isStarred }
          : { isArchived: !sessions.find(s => s.id === sessionId)?.isArchived };
        
        await claraDB.updateClaraSession(sessionId, updates);
        setSessions(prev => prev.map(s => 
          s.id === sessionId ? { ...s, ...updates } : s
        ));
      }
    } catch (error) {
      console.error(`Failed to ${action} session:`, error);
    }
  }, [sessions, currentSession, handleNewChat]);

  // Handle provider change
  const handleProviderChange = useCallback(async (providerId: string) => {
    try {
      const provider = providers.find(p => p.id === providerId);
      if (!provider) {
        console.error('Provider not found:', providerId);
        return;
      }

      setIsLoadingProviders(true);
      console.log('=== Switching to provider ===');
      console.log('Provider:', provider.name, '(ID:', providerId, ')');
      
      // POCKET PROVIDER AUTO-START LOGIC
      if (provider.type === 'claras-pocket' && window.llamaSwap) {
        try {
          console.log("🚀 Switching to Clara's Core - checking status...");
          // Check if running
          const status = await window.llamaSwap.getStatus?.();
          if (!status?.isRunning) {
            console.log("🔄 Clara's Core is not running, starting for provider switch...");
            addInfoNotification(
              "Starting Clara's Core...",
              'Clara is starting up her local AI service. Please wait a moment.',
              6000
            );
            const result = await window.llamaSwap.start();
            if (!result.success) {
              addErrorNotification(
                "Failed to Start Clara's Core",
                result.error || 'Could not start the local AI service. Please check your installation.',
                10000
              );
              console.error("❌ Failed to start Clara's Core for provider switch:", result.error);
              setIsLoadingProviders(false);
              return;
            }
            console.log("✅ Clara's Core started successfully for provider switch");
            addInfoNotification(
              "Clara's Core Ready",
              'Local AI service is now running and ready!',
              3000
            );
            // Wait a moment for service to be ready
            await new Promise(res => setTimeout(res, 2000));
          } else {
            console.log("✅ Clara's Core is already running for provider switch");
          }
        } catch (err) {
          console.error("⚠️ Error starting Clara's Core for provider switch:", err);
          addErrorNotification(
            "Clara's Core Startup Error",
            err instanceof Error ? err.message : 'Could not communicate with the local AI service.',
            8000
          );
          setIsLoadingProviders(false);
          return;
        }
      }
      // STEP 1: Health check the provider before proceeding (with caching)
      console.log('🏥 Testing provider health...');
      
      // Only show notification for non-cached health checks
      const cached = providerHealthCache.get(provider.id);
      const now = Date.now();
      const isCacheValid = cached && (now - cached.timestamp < HEALTH_CHECK_CACHE_TIME);
      
      if (!isCacheValid) {
        addInfoNotification(
          'Testing Provider',
          `Checking connection to ${provider.name}...`,
          2000
        );
      }

      const isHealthy = await checkProviderHealthCached(provider);
      if (!isHealthy) {
        console.error('❌ Provider health check failed for:', provider.name);
        
        // Show error notification with suggestion
        addErrorNotification(
          'Provider Connection Failed',
          `${provider.name} is not responding. Please check if the service is running or try a different provider.`,
          8000
        );
        
        // Don't proceed with provider switch if health check fails
        setIsLoadingProviders(false);
        return;
      }
      
      console.log('✅ Provider health check passed for:', provider.name);
      if (!isCacheValid) {
        addInfoNotification(
          'Provider Connected',
          `Successfully connected to ${provider.name}`,
          2000
        );
      }
      
      // STEP 2: Update API service to use selected provider
      claraApiService.updateProvider(provider);
      
      // STEP 3: Load models from ALL providers (not just selected one)
      const allModels = await claraApiService.getModels(); // Get all models
      console.log('Available models for', provider.name, ':', allModels.filter(m => m.provider === providerId).map(m => ({ id: m.id, name: m.name })));
      setModels(allModels);
      
      // STEP 4: Create models filtered by current provider for validation
      const providerModels = allModels.filter(m => m.provider === providerId);
      console.log('Filtered models for provider validation:', providerModels.map(m => m.id));
      
      // STEP 5: Try to load saved config for this provider
      const savedConfig = loadProviderConfig(providerId);
      
      if (savedConfig) {
        console.log('Found saved config for', provider.name);
        console.log('Saved models:', savedConfig.models);
        
        // STEP 6: Validate saved models against current provider's available models
        const validTextModel = providerModels.find(m => m.id === savedConfig.models.text);
        const validVisionModel = providerModels.find(m => m.id === savedConfig.models.vision);
        const validCodeModel = providerModels.find(m => m.id === savedConfig.models.code);
        
        console.log('Model validation:');
        console.log('- Text model valid:', !!validTextModel, validTextModel?.id);
        console.log('- Vision model valid:', !!validVisionModel, validVisionModel?.id);
        console.log('- Code model valid:', !!validCodeModel, validCodeModel?.id);
        
        // STEP 7: Create clean config with validated models
        const cleanConfig = {
          provider: providerId,
          systemPrompt: savedConfig.systemPrompt, // Preserve saved system prompt
          models: {
            text: validTextModel ? savedConfig.models.text : '',
            vision: validVisionModel ? savedConfig.models.vision : '',
            code: validCodeModel ? savedConfig.models.code : ''
          },
          parameters: {
            ...savedConfig.parameters
          },
          features: {
            ...savedConfig.features
          },
          mcp: savedConfig.mcp || {
            enableTools: true,
            enableResources: true,
            enabledServers: [],
            autoDiscoverTools: true,
            maxToolCalls: 5
          },
          autonomousAgent: savedConfig.autonomousAgent || {
            enabled: true,
            maxRetries: 1,                // 🚀 OPTIMISÉ: Réduit à 1 pour éviter les délais
            retryDelay: 0,                // 🚀 OPTIMISÉ: Supprimé pour performance maximale
            enableSelfCorrection: true,
            enableToolGuidance: true,
            enableProgressTracking: true,
            maxToolCalls: 10,
            confidenceThreshold: 0.7,
            enableChainOfThought: true,
            enableErrorLearning: true
          },
          contextWindow: savedConfig.contextWindow || 50
        };
        
        console.log('Applied clean config:', cleanConfig);
        setSessionConfig(prev => ({
          ...prev,
          aiConfig: cleanConfig
        }));
        
        // If any models were invalid, save the cleaned config
        if (!validTextModel || !validVisionModel || !validCodeModel) {
          console.log('Cleaning invalid models from saved config');
          saveProviderConfig(providerId, cleanConfig);
        }
        
      } else {
        console.log('No saved config found for', provider.name, '- creating default');
        
        // STEP 8: Create fresh default config for this provider
        let textModel, visionModel, codeModel;

        // 🚀 FILTRER LES MODÈLES DU PROVIDER ACTUEL
        const providerModels = models.filter(m => m.provider === providerId);

        // 🚀 MODÈLES PAR DÉFAUT POUR LM STUDIO - FORCER 14B PARTOUT
        if (provider.type === 'lmstudio') {
          // 🎯 PRIORITÉ ABSOLUE AU 14B - RECHERCHE TRÈS SPÉCIFIQUE COMPATIBLE LM STUDIO
          textModel = providerModels.find(m => {
            const modelId = m.id.toLowerCase();
            return (
              // LM Studio: qwen3-14b ou qwen-14b
              modelId === 'qwen3-14b' ||
              modelId === 'qwen-14b' ||
              // Ollama: qwen3-14b-optimized
              modelId.includes('qwen3-14b-optimized') ||
              modelId.includes('qwen-14b-optimized') ||
              // Recherche générale 14B
              (modelId.includes('qwen') && modelId.includes('14b'))
            );
          }) ||
          // 🎯 FALLBACK : Éviter explicitement les modèles 14B
          providerModels.find(m =>
            m.id.toLowerCase().includes('qwen') &&
            !m.id.toLowerCase().includes('14b') &&
            !m.id.toLowerCase().includes('8b') &&
            !m.id.toLowerCase().includes('7b')
          ) ||
          // 🎯 DERNIER RECOURS : Premier modèle text/multimodal
          providerModels.find(m => m.type === 'text' || m.type === 'multimodal');

          visionModel = providerModels.find(m => m.id.toLowerCase().includes('gemma') && m.id.toLowerCase().includes('27b')) ||
                       providerModels.find(m => m.id.toLowerCase().includes('gemma')) ||
                       providerModels.find(m => m.supportsVision);

          codeModel = providerModels.find(m => m.supportsCode);
        } else if (provider.type === 'ollama') {
          // 🚀 MODÈLES PAR DÉFAUT POUR OLLAMA
          // Priorité : magistral > qwen3 > gemma3 > premier disponible
          textModel = providerModels.find(m => m.id.toLowerCase().includes('magistral')) ||
                     providerModels.find(m => m.id.toLowerCase().includes('qwen3')) ||
                     providerModels.find(m => m.id.toLowerCase().includes('gemma3')) ||
                     providerModels.find(m => m.type === 'text' || m.type === 'multimodal');

          visionModel = providerModels.find(m => m.id.toLowerCase().includes('gemma3')) ||
                       providerModels.find(m => m.id.toLowerCase().includes('qwen3')) ||
                       providerModels.find(m => m.supportsVision);

          codeModel = providerModels.find(m => m.id.toLowerCase().includes('qwen3')) ||
                     providerModels.find(m => m.supportsCode);
        } else {
          // Sélection automatique pour les autres providers
          textModel = providerModels.find(m => m.type === 'text' || m.type === 'multimodal');
          visionModel = providerModels.find(m => m.supportsVision);
          codeModel = providerModels.find(m => m.supportsCode);
        }
        
        console.log('🎯 Auto-selected models for', provider.name, ':');
        console.log('- Text:', textModel?.id || 'none', textModel?.name || '');
        console.log('- Vision:', visionModel?.id || 'none', visionModel?.name || '');
        console.log('- Code:', codeModel?.id || 'none', codeModel?.name || '');

        // 🚀 LOG SPÉCIAL POUR LM STUDIO
        if (provider.type === 'lmstudio') {
          console.log('🎯 LM Studio configured as PRIMARY provider with preferred models:');
          console.log('  📝 Text model (Qwen 4B preferred):', textModel?.id || 'none');
          console.log('  👁️ Vision model (Gemma 4B preferred):', visionModel?.id || 'none');
        }
        
        // 🚀 FORCER LE MODÈLE 14B PAR DÉFAUT - LOGIQUE RENFORCÉE COMPATIBLE LM STUDIO
        const defaultTextModel = providerModels.find(m => {
          const modelId = m.id.toLowerCase();
          return (
            // LM Studio: qwen3-14b ou qwen-14b
            modelId === 'qwen3-14b' ||
            modelId === 'qwen-14b' ||
            // Ollama: qwen3-14b-optimized
            modelId.includes('qwen3-14b-optimized') ||
            modelId.includes('qwen-14b-optimized') ||
            // Recherche générale 14B
            (modelId.includes('qwen') && modelId.includes('14b'))
          );
        }) ||
        // 🎯 FALLBACK : Prendre n'importe quel modèle qwen disponible
        providerModels.find(m =>
          m.id.toLowerCase().includes('qwen')
        ) || textModel;

        console.log('🚀 MODÈLE PAR DÉFAUT FORCÉ:', defaultTextModel?.id || 'none', defaultTextModel?.name || '');

        const defaultConfig = {
          provider: providerId,
          systemPrompt: getDefaultSystemPrompt(provider),
          models: {
            text: defaultTextModel?.id || '',
            vision: visionModel?.id || '',
            code: codeModel?.id || ''
          },
          parameters: {
            temperature: 0.7,
            maxTokens: 35000, // Limite réelle pour Qwen3-32B
            topP: 1.0,
            topK: 40
          },
          features: {
            enableTools: false,           // **CHANGED**: Default to false for streaming mode
            enableRAG: true,              // **CHANGED**: Enable RAG for document chat
            enableStreaming: true,        // **CHANGED**: Default to streaming mode
            enableVision: true,
            autoModelSelection: false,    // **CHANGED**: Default to manual model selection
            enableMCP: false              // **CHANGED**: Default to false for streaming mode
          },
          mcp: {
            enableTools: true,
            enableResources: true,
            enabledServers: [],
            autoDiscoverTools: true,
            maxToolCalls: 5
          },
          autonomousAgent: {
            enabled: false,               // **CHANGED**: Default to false for streaming mode
            maxRetries: 1,                // 🚀 OPTIMISÉ: Réduit à 1 pour éviter les délais
            retryDelay: 0,                // 🚀 OPTIMISÉ: Supprimé pour performance maximale
            enableSelfCorrection: true,
            enableToolGuidance: true,
            enableProgressTracking: true,
            maxToolCalls: 10,
            confidenceThreshold: 0.7,
            enableChainOfThought: true,
            enableErrorLearning: true
          },
          contextWindow: 50 // Include last 50 messages in conversation history
        };
        
        console.log('Created default config:', defaultConfig);
        setSessionConfig(prev => ({
          ...prev,
          aiConfig: defaultConfig
        }));
        
        // Save the default config
        saveProviderConfig(providerId, defaultConfig);
      }
      
      console.log('=== Provider switch complete ===');
      
    } catch (error) {
      console.error('Failed to change provider:', error);
    } finally {
      setIsLoadingProviders(false);
    }
  }, [providers, checkProviderHealthCached, providerHealthCache, HEALTH_CHECK_CACHE_TIME]);

  // Clear health cache for a specific provider (useful when we know something changed)
  const clearProviderHealthCache = useCallback((providerId?: string) => {
    if (providerId) {
      setProviderHealthCache(prev => {
        const newCache = new Map(prev);
        newCache.delete(providerId);
        return newCache;
      });
      console.log(`🧹 Cleared health cache for provider: ${providerId}`);
    } else {
      setProviderHealthCache(new Map());
      console.log('🧹 Cleared all provider health cache');
    }
  }, []);

  // Handle model change
  const handleModelChange = useCallback((modelId: string, type: 'text' | 'vision' | 'code') => {
    // 🧠 INFORMER LE COMPRESSOR DU NOUVEAU MODÈLE
    if (type === 'text') {
      // Calculer la limite de contexte pour ce modèle
      const contextLimit = getModelContextLimit(modelId);
      // 🚀 SUPPRIMÉ : Plus besoin de mettre à jour le compressor (utilise PerfectCompressor maintenant)
      logger.info(LogCategory.SYSTEM, `🧠 Model context limit: ${modelId} (${contextLimit} tokens)`);
    }

    setSessionConfig(prev => {
      if (!prev.aiConfig?.provider) {
        console.error('No provider set when trying to change model');
        return prev;
      }

      // Validate that the selected model belongs to the current provider
      const selectedModel = models.find(m => m.id === modelId);
      if (selectedModel && selectedModel.provider !== prev.aiConfig.provider) {
        console.error('Model validation failed: Model', modelId, 'belongs to provider', selectedModel.provider, 'but current provider is', prev.aiConfig.provider);
        return prev; // Don't update if model is from wrong provider
      }

      console.log('Model change validation passed:', {
        modelId,
        type,
        provider: prev.aiConfig.provider,
        modelProvider: selectedModel?.provider
      });

      const updatedConfig = {
        ...prev,
        aiConfig: {
          ...prev.aiConfig,
          models: {
            ...prev.aiConfig.models,
            [type]: modelId
          },
          // 🎯 MISE À JOUR AUTOMATIQUE DE maxTokens selon le modèle
          parameters: type === 'text' ? {
            ...prev.aiConfig.parameters,
            maxTokens: getModelContextLimit(modelId)
          } : prev.aiConfig.parameters
        }
      };
      
      // Save the updated configuration for the current provider
      if (updatedConfig.aiConfig?.provider) {
        saveProviderConfig(updatedConfig.aiConfig.provider, updatedConfig.aiConfig);
        console.log('Saved model change for provider:', updatedConfig.aiConfig.provider, type, modelId);
      }
      
      return updatedConfig;
    });
  }, [models]);

  // Handle message interactions
  const handleCopyMessage = useCallback(async (content: string) => {
    const success = await copyToClipboard(content);
    if (success) {
      // Could show a toast notification here
      console.log('Message copied:', content);
    } else {
      console.error('Failed to copy message');
    }
  }, []);

  const handleRetryMessage = useCallback((messageId: string) => {
    console.log('Retrying message:', messageId);
    // Implementation for retrying failed messages
    const messageIndex = messages.findIndex(m => m.id === messageId);
    if (messageIndex > 0) {
      const previousMessage = messages[messageIndex - 1];
      if (previousMessage.role === 'user') {
        handleSendMessage(previousMessage.content, previousMessage.attachments);
      }
    }
  }, [messages, handleSendMessage]);

  const handleEditMessage = useCallback((messageId: string, newContent: string) => {
    console.log('Editing message:', messageId, newContent);
    // Implementation for editing messages
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { ...msg, content: newContent, timestamp: new Date() }
        : msg
    ));
  }, []);

  // Handle stopping generation
  const handleStop = useCallback(() => {
    console.log('🛑 Stopping generation and cleaning LM Studio...');

    // 1. Arrêter le service API (inclut le nettoyage LM Studio)
    claraApiService.stop();

    // 2. Mettre à jour l'état local
    setIsLoading(false);

    // 🎯 SÉCURITÉ : Forcer la fermeture du ProcessingIndicator via événement
    window.dispatchEvent(new CustomEvent('force-close-processing-indicator'));

    // 3. Notification utilisateur
    addCompletionNotification(
      'Génération arrêtée',
      'La génération a été arrêtée et LM Studio a été nettoyé.',
      3000
    );
  }, [addCompletionNotification]);

  // Simple preload - only if server is down
  const handlePreloadModel = useCallback(async () => {
    if (!sessionConfig.aiConfig) return;
    
    // Only preload for local services that might be down
    if (sessionConfig.aiConfig.provider === 'claras-pocket') {
      try {
        const status = await window.llamaSwap?.getStatus();
        if (!status?.isRunning) {
          console.log('🚀 Starting local server...');
          await claraApiService.preloadModel(sessionConfig.aiConfig);
        }
        // If server is running, no preload needed - it handles automatically
      } catch (error) {
        console.warn('⚠️ Simple preload check failed:', error);
      }
    }
    // For cloud providers (OpenAI, etc.), no preload needed
  }, [sessionConfig.aiConfig, messages]);

  // Handle session config changes
  const handleConfigChange = useCallback((newConfig: Partial<ClaraSessionConfig>) => {
    setSessionConfig(prev => {
      const updated = { ...prev, ...newConfig };
      
      // Only save provider-specific configuration if we have a valid provider
      if (updated.aiConfig?.provider) {
        // If provider is changing through this config change, don't save mixed config
        if (newConfig.aiConfig?.provider && newConfig.aiConfig.provider !== prev.aiConfig?.provider) {
          console.log('Provider changing through config, will be handled by provider change handler');
          return updated;
        }
        
        // Validate models belong to current provider before saving
        if (newConfig.aiConfig?.models) {
          const currentProvider = updated.aiConfig.provider;
          const models_ = newConfig.aiConfig.models;
          const textModel = models_.text ? models.find(m => m.id === models_.text) : null;
          const visionModel = models_.vision ? models.find(m => m.id === models_.vision) : null;
          const codeModel = models_.code ? models.find(m => m.id === models_.code) : null;
          
          if ((textModel && textModel.provider !== currentProvider) ||
              (visionModel && visionModel.provider !== currentProvider) ||
              (codeModel && codeModel.provider !== currentProvider)) {
            console.error('Config validation failed: Models from wrong provider in config change');
            return prev; // Don't update if models are from wrong provider
          }
        }
        
        saveProviderConfig(updated.aiConfig.provider, updated.aiConfig);
        console.log('Saved config change for provider:', updated.aiConfig.provider, newConfig);
      }
      
      return updated;
    });
  }, [models]);

  // Debug utility for testing provider configurations
  useEffect(() => {
    // Expose debug functions to window for testing
    (window as any).debugClaraProviders = () => {
      console.log('Current provider configurations:');
      console.log('Providers:', providers.map(p => ({ id: p.id, name: p.name, isPrimary: p.isPrimary })));
      console.log('Models:', models.map(m => ({ id: m.id, name: m.name, provider: m.provider })));
      console.log('Current session config:', sessionConfig);
      console.log('Current session:', currentSession?.id, currentSession?.title);
      
      // Debug provider configs from localStorage
      console.log('Provider configs debug - functions removed during optimization');
    };

    (window as any).clearProviderConfigs = () => {
      console.log('Clear provider configs - function removed during optimization');
    };

    // Add MCP debugging functions
    (window as any).debugMCP = async () => {
      console.log('=== MCP Debug Info ===');
      console.log('MCP Service Ready:', claraMCPService.isReady());
      console.log('Available Servers:', claraMCPService.getRunningServers());
      console.log('Available Tools:', claraMCPService.getAvailableTools());
      console.log('Session MCP Config:', sessionConfig.aiConfig?.mcp);
    };

    // Add notification testing functions
    (window as any).testNotifications = () => {
      console.log('🔔 Testing notification system...');
      addCompletionNotification('Test Completion', 'This is a test completion notification with chime!');
      setTimeout(() => {
        addErrorNotification('Test Error', 'This is a test error notification.');
      }, 2000);
      setTimeout(() => {
        addInfoNotification('Test Info', 'This is a test info notification.');
      }, 4000);
    };

    (window as any).testCompletionSound = () => {
      console.log('🔔 Testing completion chime...');
      notificationService.testCompletionChime();
    };

    (window as any).setupTestMCP = async () => {
      console.log('🔧 Setting up test MCP server...');
      const success = await claraMCPService.setupTestGitHubServer();
      if (success) {
        console.log('✅ Test MCP server setup complete');
        await claraMCPService.refresh();
        console.log('📊 Updated MCP status:', {
          servers: claraMCPService.getRunningServers().length,
          tools: claraMCPService.getAvailableTools().length
        });
      } else {
        console.log('❌ Test MCP server setup failed');
      }
    };

    // Add background service debugging functions
    (window as any).debugBackground = () => {
      console.log('🔄 Clara Background Service Status:');
      console.log(claraBackgroundService.getStatus());
      console.log('Current visibility:', isVisible);
    };

    (window as any).testBackgroundChat = async () => {
      console.log('🧪 Testing background chat...');
      if (isVisible) {
        console.log('⚠️ Clara is currently visible. Switch to another page to test background mode.');
        return;
      }
      
      // Simulate a background message
      await handleSendMessage('This is a test message sent while Clara is in background mode.');
    };

    (window as any).testBackgroundNotification = () => {
      console.log('🧪 Testing persistent background notification...');
      addBackgroundCompletionNotification(
        'Clara Response Ready',
        'This is a persistent notification that requires manual dismissal. It will not auto-hide.'
      );
      claraBackgroundService.onBackgroundNotificationCreated();
    };

    (window as any).testBackgroundService = () => {
      console.log('🧪 Testing Clara background service notification...');
      // Simulate Clara going to background mode
      claraBackgroundService.setBackgroundMode(true);
      
      // Simulate some background activity
      setTimeout(() => {
        claraBackgroundService.incrementBackgroundActivity();
        console.log('📊 Added background activity');
      }, 1000);
      
      setTimeout(() => {
        claraBackgroundService.decrementBackgroundActivity();
        console.log('📊 Removed background activity');
      }, 3000);
      
      // Return to foreground after 5 seconds
      setTimeout(() => {
        claraBackgroundService.setBackgroundMode(false);
        console.log('👁️ Returned to foreground');
      }, 5000);
    };

    // Add refresh functionality to debug utilities
    (window as any).refreshClaraServices = async (force = false) => {
      console.log('🔄 Manually refreshing Clara services...');
      await refreshProvidersAndServices(force);
    };

    (window as any).debugRefreshStatus = () => {
      console.log('🔄 Refresh Status:');
      console.log('- Is refreshing:', isRefreshing);
      console.log('- Last refresh time:', new Date(lastRefreshTime));
      console.log('- Time since last refresh:', Math.round((Date.now() - lastRefreshTime) / 1000), 'seconds');
      console.log('- Current visibility:', isVisible);
      console.log('- Total models:', models.length);
      console.log('- Total providers:', providers.length);
    };

    // Add health cache debugging functions
    (window as any).debugHealthCache = () => {
      console.log('🏥 Provider Health Cache Status:');
      const now = Date.now();
      Array.from(providerHealthCache.entries()).forEach(([providerId, cache]) => {
        const ageSeconds = Math.round((now - cache.timestamp) / 1000);
        const isValid = ageSeconds < (HEALTH_CHECK_CACHE_TIME / 1000);
        console.log(`- ${providerId}: ${cache.isHealthy ? '✅' : '❌'} (${ageSeconds}s ago, ${isValid ? 'valid' : 'expired'})`);
      });
      console.log(`Cache TTL: ${HEALTH_CHECK_CACHE_TIME / 1000} seconds`);
    };

    (window as any).clearHealthCache = (providerId?: string) => {
      clearProviderHealthCache(providerId);
    };

    (window as any).testHealthCachePerformance = async () => {
      const provider = providers[0];
      if (!provider) {
        console.log('No providers available for testing');
        return;
      }

      console.log('🏥 Testing health cache performance...');
      
      // First call (uncached)
      const start1 = performance.now();
      await checkProviderHealthCached(provider);
      const uncachedTime = performance.now() - start1;
      
      // Second call (cached)
      const start2 = performance.now();
      await checkProviderHealthCached(provider);
      const cachedTime = performance.now() - start2;
      
      console.log(`Uncached health check: ${uncachedTime.toFixed(2)}ms`);
      console.log(`Cached health check: ${cachedTime.toFixed(2)}ms`);
      console.log(`Performance improvement: ${((uncachedTime - cachedTime) / uncachedTime * 100).toFixed(1)}%`);
    };

    // Add provider-specific debugging functions
    (window as any).debugProblematicTools = (providerId?: string) => {
      console.log('=== Provider-Specific Problematic Tools Debug ===');
      if (providerId) {
        console.log(`Problematic tools for provider ${providerId}:`);
        const storageKey = `clara-problematic-tools-${providerId}`;
        const stored = JSON.parse(localStorage.getItem(storageKey) || '[]');
        console.log('Stored tools:', stored);
      } else {
        console.log('All provider-specific problematic tools:');
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          if (key && key.startsWith('clara-problematic-tools-')) {
            const stored = JSON.parse(localStorage.getItem(key) || '[]');
            console.log(`${key}:`, stored);
          }
        }
      }
    };

    // Simple debug for current config
    (window as any).debugClara = () => {
      console.log('Clara Status:', {
        provider: sessionConfig.aiConfig?.provider,
        hasModels: models.length > 0,
        isVisible: isVisible,
        currentSession: currentSession?.title
      });
    };

    return () => {
      delete (window as any).debugClaraProviders;
      delete (window as any).clearProviderConfigs;
      delete (window as any).debugMCP;
      delete (window as any).testNotifications;
      delete (window as any).testCompletionSound;
      delete (window as any).setupTestMCP;
      delete (window as any).debugBackground;
      delete (window as any).testBackgroundChat;
      delete (window as any).testBackgroundNotification;
      delete (window as any).testBackgroundService;
      delete (window as any).refreshClaraServices;
      delete (window as any).debugRefreshStatus;
      delete (window as any).debugHealthCache;
      delete (window as any).clearHealthCache;
      delete (window as any).testHealthCachePerformance;
      delete (window as any).debugProblematicTools;
      delete (window as any).debugClara;
    };
  }, [providers, models, sessionConfig, currentSession, isVisible, handleSendMessage, 
      providerHealthCache, HEALTH_CHECK_CACHE_TIME, checkProviderHealthCached, clearProviderHealthCache]);

  // 🚀 OPTIMISATION MULTI-UTILISATEURS : Ne pas créer de session au démarrage
  // La session sera créée seulement au premier message envoyé
  useEffect(() => {
    const initializeSession = async () => {
      // 🎯 NOUVEAU COMPORTEMENT : Seulement si on a des sessions existantes, charger la plus récente
      if (!isLoadingSessions && sessions.length > 0 && !currentSession) {
        // Charger la session la plus récente existante
        const mostRecentSession = sessions[0];
        const fullSession = await claraDB.getClaraSession(mostRecentSession.id);
        if (fullSession) {
          setCurrentSession(fullSession);
          setMessages(fullSession.messages);

          // 🎯 SUPPRIMÉ : Plus de migration nécessaire

          // Configurer le document store pour la session existante
          const documentStore = useDocumentStore.getState();
          if (documentStore.setSession) {
            documentStore.setSession(fullSession.id);
            console.log('🔄 Document store configured for existing session:', fullSession.id);
          }

          logger.info(LogCategory.SESSIONS, `📂 Loaded existing session: ${fullSession.title}`);
        }
      }

      // 🎯 SI AUCUNE SESSION : Rester en mode "page d'accueil" sans créer de session
      if (!isLoadingSessions && sessions.length === 0 && !currentSession) {
        console.log('🏠 Mode page d\'accueil - aucune session créée jusqu\'au premier message');
      }
    };

    initializeSession();
  }, [isLoadingSessions, sessions.length, currentSession]);

  // 🔄 Écouter les événements de compression automatique
  useEffect(() => {
    const handleCompressionComplete = async (event: CustomEvent) => {
      const { compressedMessages, sessionId } = event.detail;

      console.log(`📡 Événement compression-complete reçu pour session ${sessionId}`);

      // Vérifier que c'est pour la session courante
      if (currentSession && sessionId === currentSession.id) {
        console.log(`🔄 Compression terminée: ${messages.length} → ${compressedMessages.length} messages`);

        // 🚀 NOUVELLE APPROCHE : Garder les messages originaux pour l'affichage utilisateur
        // et sauvegarder les compressés séparément pour le LLM

        // Sauvegarder les messages compressés dans la base de données
        try {
          // 🚀 UTILISER LA BASE DE DONNÉES : Sauvegarder avec propriétés étendues
          await claraDB.updateClaraSession(currentSession.id, {
            compressedMessages: compressedMessages,
            isCompressed: true,
            compressionTimestamp: new Date(),
            originalMessageCount: messages.length
          } as any);

          console.log('✅ Session mise à jour avec compression séparée en base de données');

          // 👁️ AFFICHAGE : Garder les messages originaux pour l'utilisateur
          // Les messages compressés seront utilisés seulement pour le LLM

          // Marquer qu'une version compressée existe et mettre à jour les messages pour LLM
          setHasCompressedVersion(true);
          setMessagesForLLM(compressedMessages);

        console.log(`🔄 MessagesForLLM mis à jour: ${compressedMessages.length} messages, ${compressedMessages.reduce((sum: number, msg: any) => sum + msg.content.length, 0)} chars`);

          // Notification avec option de voir la compression
          addCompletionNotification(
            'Conversation optimisée',
            `Conversation compressée pour le LLM (${messages.length} → ${compressedMessages.length} messages). L'affichage reste complet.`,
            6000
          );

        } catch (error) {
          console.error('❌ Erreur sauvegarde compression automatique:', error);
        }
      } else {
        console.log(`ℹ️ Compression pour une autre session (${sessionId}), ignorée`);
      }
    };

    // Ajouter l'écouteur d'événement
    window.addEventListener('compression-complete', handleCompressionComplete as any);

    // Nettoyage
    return () => {
      window.removeEventListener('compression-complete', handleCompressionComplete as any);
    };
  }, [currentSession, messages, addCompletionNotification]);

  // 🗜️ Écouteur pour le remplacement de contexte via tool compression
  useEffect(() => {
    const handleContextReplacement = (event: CustomEvent) => {
      const { newMessages, sessionId, compressionInfo } = event.detail;

      console.log(`🗜️ Remplacement contexte reçu pour session ${sessionId}:`, compressionInfo);

      // Vérifier si c'est pour la session actuelle
      if (sessionId === currentSession || sessionId === 'current-session') {
        try {
          // Remplacer complètement les messages
          setMessages(newMessages);
          setMessagesForLLM(newMessages);
          setHasCompressedVersion(true);

          // Notification de succès
          addCompletionNotification(
            `✅ Contexte compressé via tool: ${compressionInfo.originalLength} → ${compressionInfo.compressedLength} caractères (${(compressionInfo.ratio * 100).toFixed(1)}%)`,
            'success',
            6000
          );

          console.log(`✅ Contexte remplacé: ${newMessages.length} messages`);

        } catch (error) {
          console.error('❌ Erreur remplacement contexte:', error);
          addCompletionNotification(
            '❌ Erreur lors du remplacement du contexte',
            'error',
            4000
          );
        }
      } else {
        console.log(`ℹ️ Remplacement contexte pour une autre session (${sessionId}), ignoré`);
      }
    };

    // Ajouter l'écouteur d'événement
    window.addEventListener('context-replacement', handleContextReplacement as EventListener);

    // Nettoyage
    return () => {
      window.removeEventListener('context-replacement', handleContextReplacement as EventListener);
    };
  }, [currentSession, addCompletionNotification]);

  // 🔄 Synchroniser messagesForLLM avec les messages actuels si pas de compression
  useEffect(() => {
    if (!hasCompressedVersion) {
      setMessagesForLLM(messages);
    }
  }, [messages, hasCompressedVersion]);

  return (
    <div className="flex h-screen w-full relative" data-clara-container>
      {/* Wallpaper */}
      {wallpaperUrl && (
        <div 
          className="absolute top-0 left-0 right-0 bottom-0 z-0"
          style={{
            backgroundImage: `url(${wallpaperUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            opacity: 0.1,
            filter: 'blur(1px)',
            pointerEvents: 'none'
          }}
        />
      )}

      {/* Content with relative z-index */}
      <div className="relative z-10 flex h-screen w-full">
        {/* App Sidebar (main navigation) on the left */}
        <Sidebar activePage="clara" onPageChange={onPageChange} />

        {/* Main: Chat Area */}
        <div className="flex-1 flex flex-col h-full bg-gradient-to-br from-white to-gray-50 dark:from-gray-900 dark:to-gray-800">
          {/* Header - Removed */}
          
          {/* Chat Window */}
          <ClaraChatWindow
            messages={messages}
            userName={userName}
            isLoading={isLoading}
            isInitializing={isLoadingSessions || isLoadingProviders}
            isSearching={isSearching} // 🎯 CORRECTION : Passer le vrai état de recherche
            onRetryMessage={handleRetryMessage}
            onCopyMessage={handleCopyMessage}
            onEditMessage={handleEditMessage}
            onContextAction={handleContextAction}
          />
          
          {/* Draggable Advanced Options Panel */}
          <DraggableAdvancedOptions
            isOpen={showAdvancedOptions}
            onClose={() => setShowAdvancedOptions(false)}
            aiConfig={sessionConfig.aiConfig}
            onConfigChange={(newConfig) => {
              const currentConfig = sessionConfig.aiConfig;
              const updatedConfig: ClaraAIConfig = {
                provider: newConfig.provider ?? currentConfig.provider,
                systemPrompt: newConfig.systemPrompt ?? currentConfig.systemPrompt,
                models: {
                  text: newConfig.models?.text ?? currentConfig.models.text,
                  vision: newConfig.models?.vision ?? currentConfig.models.vision,
                  code: newConfig.models?.code ?? currentConfig.models.code
                },
                parameters: {
                  temperature: newConfig.parameters?.temperature ?? currentConfig.parameters.temperature,
                  maxTokens: newConfig.parameters?.maxTokens ?? currentConfig.parameters.maxTokens,
                  topP: newConfig.parameters?.topP ?? currentConfig.parameters.topP,
                  topK: newConfig.parameters?.topK ?? currentConfig.parameters.topK
                },
                features: {
                  enableTools: newConfig.features?.enableTools ?? currentConfig.features.enableTools,
                  enableRAG: newConfig.features?.enableRAG ?? currentConfig.features.enableRAG,
                  enableStreaming: newConfig.features?.enableStreaming ?? currentConfig.features.enableStreaming,
                  enableVision: newConfig.features?.enableVision ?? currentConfig.features.enableVision,
                  autoModelSelection: newConfig.features?.autoModelSelection ?? currentConfig.features.autoModelSelection,
                  enableMCP: newConfig.features?.enableMCP ?? currentConfig.features.enableMCP
                },
                mcp: newConfig.mcp ? {
                  enableTools: newConfig.mcp.enableTools ?? currentConfig.mcp?.enableTools ?? true,
                  enableResources: newConfig.mcp.enableResources ?? currentConfig.mcp?.enableResources ?? true,
                  enabledServers: newConfig.mcp.enabledServers ?? currentConfig.mcp?.enabledServers ?? [],
                  autoDiscoverTools: newConfig.mcp.autoDiscoverTools ?? currentConfig.mcp?.autoDiscoverTools ?? true,
                  maxToolCalls: newConfig.mcp.maxToolCalls ?? currentConfig.mcp?.maxToolCalls ?? 5
                } : currentConfig.mcp,
                autonomousAgent: newConfig.autonomousAgent ? {
                  enabled: newConfig.autonomousAgent.enabled ?? currentConfig.autonomousAgent?.enabled ?? false,
                  maxRetries: newConfig.autonomousAgent.maxRetries ?? currentConfig.autonomousAgent?.maxRetries ?? 3,
                  retryDelay: newConfig.autonomousAgent.retryDelay ?? currentConfig.autonomousAgent?.retryDelay ?? 1000,
                  enableSelfCorrection: newConfig.autonomousAgent.enableSelfCorrection ?? currentConfig.autonomousAgent?.enableSelfCorrection ?? true,
                  enableToolGuidance: newConfig.autonomousAgent.enableToolGuidance ?? currentConfig.autonomousAgent?.enableToolGuidance ?? true,
                  enableProgressTracking: newConfig.autonomousAgent.enableProgressTracking ?? currentConfig.autonomousAgent?.enableProgressTracking ?? true,
                  maxToolCalls: newConfig.autonomousAgent.maxToolCalls ?? currentConfig.autonomousAgent?.maxToolCalls ?? 10,
                  confidenceThreshold: newConfig.autonomousAgent.confidenceThreshold ?? currentConfig.autonomousAgent?.confidenceThreshold ?? 0.7,
                  enableChainOfThought: newConfig.autonomousAgent.enableChainOfThought ?? currentConfig.autonomousAgent?.enableChainOfThought ?? true,
                  enableErrorLearning: newConfig.autonomousAgent.enableErrorLearning ?? currentConfig.autonomousAgent?.enableErrorLearning ?? true
                } : currentConfig.autonomousAgent,
                contextWindow: newConfig.contextWindow ?? currentConfig.contextWindow
              };
              handleConfigChange({ aiConfig: updatedConfig });
            }}
            providers={providers}
            models={models}
            onProviderChange={handleProviderChange}
            onModelChange={handleModelChange}
            conversationHistory={messages}
            sessionConfig={sessionConfig}
          />



          {/* RAG Status Indicator - Discrete (USE REAL DATA) */}
          {(() => {
            const realStore = useDocumentStore.getState();
            const realSelectedDocs = realStore?.selectedDocuments || [];
            const realHasDocuments = realSelectedDocs.length > 0;

            return realHasDocuments && !isDocumentPanelVisible && (
              <div className="px-6 mb-2">
                <div className="text-xs text-wema-600 dark:text-wema-400 bg-wema-50 dark:bg-wema-900/20 px-2 py-1 rounded-md inline-flex items-center gap-1">
                  <Database className="w-3 h-3" />
                  <span>{realSelectedDocs.length} document{realSelectedDocs.length !== 1 ? 's' : ''} ready for RAG</span>
                </div>
              </div>
            );
          })()}

          {/* Document Chat Integration */}
          {isDocumentPanelVisible && (
            <div className="mb-4">
              <DocumentChatIntegration
                onDocumentSelect={(doc) => {
                  console.log('🔍 ClaraAssistant onDocumentSelect called with:', doc.filename, doc.id);

                  try {
                    // Method 1: Try direct store access
                    const store = useDocumentStore.getState();
                    console.log('🔍 Store state:', store);
                    console.log('🔍 addDocument method:', typeof store?.addDocument);

                    if (store?.addDocument) {
                      console.log('📄 Adding document to store...');
                      store.addDocument(doc);
                      console.log('✅ Document added successfully');
                      return;
                    }

                    // Method 2: Manual state update if store methods not available
                    console.log('⚠️ Using manual document addition...');
                    const currentState = useDocumentStore.getState();
                    const isAlreadySelected = currentState?.selectedDocuments?.some(d => d.id === doc.id) || false;

                    if (!isAlreadySelected) {
                      const newSelectedDocuments = [...(currentState?.selectedDocuments || [])];

                      // Check max limit (5 documents)
                      if (newSelectedDocuments.length >= 5) {
                        newSelectedDocuments.shift(); // Remove oldest
                        console.log('📄 Removed oldest document due to limit');
                      }

                      newSelectedDocuments.push(doc);

                      // Save to localStorage for persistence
                      try {
                        localStorage.setItem('clara-rag-documents', JSON.stringify(newSelectedDocuments));
                      } catch (error) {
                        console.warn('Failed to save to localStorage:', error);
                      }

                      useDocumentStore.setState({
                        ...currentState,
                        selectedDocuments: newSelectedDocuments,
                        error: null
                      });

                      console.log('✅ Document added manually:', doc.filename);
                      console.log('📋 Total selected:', newSelectedDocuments.length);
                    } else {
                      console.log('📄 Document already selected:', doc.filename);
                    }

                  } catch (error) {
                    console.error('❌ Failed to add document:', error);
                  }
                }}
                selectedDocuments={selectedDocuments}
                onRemoveDocument={(docId) => {
                  console.log('🗑️ Removing document:', docId);
                  try {
                    // Method 1: Try direct store access
                    const store = useDocumentStore.getState();
                    if (store?.removeDocument) {
                      console.log('🗑️ Calling store.removeDocument with:', docId, 'type:', typeof docId);
                      store.removeDocument(docId); // Pass the ID directly without conversion
                      console.log('✅ Document removed via store method');
                      return;
                    }

                    // Method 2: Manual state update
                    console.log('⚠️ Using manual document removal...');
                    const currentState = useDocumentStore.getState();
                    const newSelectedDocuments = (currentState?.selectedDocuments || []).filter(d => d.id != docId); // Use != for flexible comparison
                    const removed = (currentState?.selectedDocuments || []).find(d => d.id == docId); // Use == for flexible comparison

                    // Save to localStorage for persistence
                    try {
                      localStorage.setItem('clara-rag-documents', JSON.stringify(newSelectedDocuments));
                    } catch (error) {
                      console.warn('Failed to save to localStorage:', error);
                    }

                    useDocumentStore.setState({
                      ...currentState,
                      selectedDocuments: newSelectedDocuments
                    });

                    if (removed) {
                      console.log('✅ Document removed manually:', removed.filename);
                    }

                  } catch (error) {
                    console.error('❌ Failed to remove document:', error);
                  }
                }}
                isVisible={isDocumentPanelVisible}
                onToggle={handleDocumentPanelToggle}
              />
            </div>
          )}

          {/* Chat Input */}
          <ClaraAssistantInput
            onSendMessage={handleSendMessage}
            isLoading={isLoading || isLoadingProviders}
            sessionConfig={sessionConfig}
            onConfigChange={handleConfigChange}
            providers={providers}
            models={models}
            onProviderChange={handleProviderChange}
            onModelChange={handleModelChange}
            onStop={handleStop}
            onNewChat={handleNewChat}
            autoTTSText={latestAIResponse}
            autoTTSTrigger={autoTTSTrigger}
            onPreloadModel={handlePreloadModel}
            showAdvancedOptionsPanel={showAdvancedOptions}
            onAdvancedOptionsToggle={setShowAdvancedOptions}
            currentSession={currentSession} // 🚀 ISOLATION: Passer la session courante
            messages={messages} // 🚀 ISOLATION: Passer les messages pour le Context Manager
            setMessages={setMessages} // 🚀 ISOLATION: Callback pour modifier les messages
            onDocumentPanelToggle={handleDocumentPanelToggle}
            isDocumentPanelVisible={isDocumentPanelVisible}
            selectedDocumentsCount={selectedDocuments.length}
            onDocumentSelect={(doc) => {
              console.log('✅ Document selected via compact RAG:', doc.filename);
              try {
                const store = useDocumentStore.getState();
                if (store?.addDocument) {
                  store.addDocument(doc);
                  console.log('✅ Document added to store via compact RAG');
                } else {
                  console.error('❌ Store addDocument method not available');
                }
              } catch (error) {
                console.error('❌ Failed to add document via compact RAG:', error);
              }
            }}
            onDocumentRemove={(docId) => {
              console.log('🗑️ Document removed from UI (but kept in database):', docId);
              try {
                const store = useDocumentStore.getState();
                if (store?.removeDocument) {
                  // ✅ SUPPRIMER DE L'INTERFACE SEULEMENT
                  store.removeDocument(docId);
                  console.log('✅ Document removed from UI - stays in database for LLM');
                } else {
                  console.error('❌ Store removeDocument method not available');
                }
              } catch (error) {
                console.error('❌ Failed to remove document from UI:', error);
              }
            }}
            onSearchStart={handleSearchStart}
            onSearchComplete={handleSearchComplete}
            // 🚀 SUPPRIMÉ: onSearchProgress - fonction supprimée
            // 🎯 SUPPRIMÉ : Plus de migration nécessaire
          />
        </div>

        {/* Clara Chat History Sidebar on the right */}
        <ClaraSidebar 
          sessions={sessions}
          currentSessionId={currentSession?.id}
          isLoading={isLoadingSessions}
          isLoadingMore={isLoadingMoreSessions}
          hasMoreSessions={hasMoreSessions}
          onSelectSession={handleSelectSession}
          onNewChat={handleNewChat}
          onSessionAction={handleSessionAction}
          onLoadMore={loadMoreSessions}
        />
      </div>

      {/* 🔍 SUPPRIMÉ: Search Progress Overlay - remplacé par animation intégrée dans le chat */}

      {/* 🧠 Context Viewer */}
      {showContextViewer && (
        <ContextViewer
          isVisible={showContextViewer}
          onClose={() => setShowContextViewer(false)}
          messages={messagesForLLM}
          ragContext={ragContextForLLM}
          currentSession={currentSession}
        />
      )}

      {/* No Models Available Modal */}
      {showNoModelsModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-8 m-4 max-w-md w-full mx-auto transform transition-all duration-300 ease-out scale-100 animate-in fade-in-0 zoom-in-95">
            {/* Icon */}
            <div className="flex justify-center mb-6">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
            </div>

            {/* Title */}
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-4">
              No AI Models Available
            </h2>

            {/* Message */}
            <p className="text-gray-600 dark:text-gray-300 text-center mb-6 leading-relaxed">
              You don't seem to have any AI models downloaded yet. To start chatting with Clara, 
              you'll need to download at least one model from the Model Manager.
            </p>

            {/* Action Buttons */}
            <div className="flex flex-col space-y-3">
              <button
                onClick={() => onPageChange('settings')}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center space-x-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                <span>Go to Model Manager</span>
              </button>
              
              <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
                This dialog will disappear once you have downloaded a model
              </p>
            </div>
          </div>
        </div>
      )}



      {/* 🧠 Context Viewer Modal */}
      <ContextViewer
        isOpen={showContextViewer}
        onClose={() => setShowContextViewer(false)}
        conversationHistory={messagesForLLM.length > 0 ? messagesForLLM : messages || []} // 🚀 CORRECTION: Utiliser les messages pour LLM
        onCompress={async () => {
          console.log('🔄 Compression manuelle déclenchée depuis Context Viewer');

          try {
            // 🚀 UTILISER LE COMPRESSEUR PARFAIT DÉJÀ IMPORTÉ

            if (!currentSession || !messages || messages.length === 0) {
              addErrorNotification(
                'Compression impossible',
                'Aucune conversation à compresser.',
                4000
              );
              return;
            }

            // 🚀 COMPRESSION MANUELLE - BYPASS DU SEUIL
            // Pour la compression manuelle, on force la compression même en dessous du seuil
            console.log('🔄 Compression manuelle forcée - bypass du seuil automatique');

            // ✅ COMPRESSION FORCÉE AVEC PERFECT COMPRESSOR
            console.log(`📊 Compression manuelle: ${messages.length} messages`);

            addInfoNotification(
              'Compression en cours...',
              'Optimisation intelligente de la conversation en cours.',
              3000
            );

            // Lancer la compression avec le nouveau compresseur
            const { messages: compressedMessages, stats } = await perfectCompressor.compressConversation(messages);

            // Mettre à jour les messages avec la version compressée
            setMessages(compressedMessages);

            // Mettre à jour la session en base
            try {
              await claraDB.updateClaraSession(currentSession.id, {
                messages: compressedMessages
              });

              addCompletionNotification(
                'Compression réussie',
                `Conversation compressée (${stats.compression_ratio ? (stats.compression_ratio * 100).toFixed(1) : 'N/A'}% de réduction)`,
                6000
              );

              console.log(`✅ Compression manuelle terminée:`, stats);
            } catch (dbError) {
              console.error('❌ Erreur sauvegarde compression:', dbError);
              addErrorNotification(
                'Erreur de sauvegarde',
                'La compression a réussi mais n\'a pas pu être sauvegardée.',
                5000
              );
            }

          } catch (error) {
            console.error('❌ Erreur compression manuelle:', error);
            addErrorNotification(
              'Erreur de compression',
              error instanceof Error ? error.message : 'Échec de la compression.',
              6000
            );
          }
        }}
        // 🚀 CONTEXTE COMPLET - EXACTEMENT CE QUE REÇOIT LE LLM
        ragContext={ragContextForLLM || (() => {
          // Fallback: Obtenir le contexte RAG depuis les documents sélectionnés
          if (selectedDocuments && selectedDocuments.length > 0) {
            return selectedDocuments.map(doc =>
              `📄 **${doc.filename}**:\n${doc.content}`
            ).join('\n\n');
          }
          return "";
        })()}
        systemPrompt={sessionConfig?.aiConfig?.systemPrompt || 'Tu es WeMa IA, un assistant intelligent et utile. IMPORTANT: Réponds TOUJOURS en français sauf demande explicite contraire.'}
        selectedDocuments={selectedDocuments || []} // Utiliser les vrais documents sélectionnés
      />
    </div>
  );
};

export default ClaraAssistant;
