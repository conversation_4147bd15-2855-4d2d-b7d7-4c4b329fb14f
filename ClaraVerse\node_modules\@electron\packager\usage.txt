Usage: electron-packager <sourcedir> <appname> [options...]

Required parameters

sourcedir          the base directory of the application source

  Examples:        electron-packager ./
                   electron-packager ./ --all

Optional parameters

appname            the name of the app, if it needs to be different from the "productName" or "name"
                   in the nearest package.json

Options

version            prints the version of Electron Packager and Node, plus the target platform and
                   arch, for bug reporting purposes, and exits immediately

* All platforms *

all                equivalent to --platform=all --arch=all
app-copyright      human-readable copyright line for the app
app-version        release version to set for the app
arch               all, or one or more of: ia32, x64, armv7l, arm64, mips64el, universal (comma-delimited if
                   multiple). Defaults to the host arch.
                   For info on arch/platform support see https://github.com/electron/packager/#supported-platforms
asar               whether to package the source code within your app into an archive. You can either
                   pass --asar by itself to use the default configuration, OR use dot notation to
                   configure a list of sub-properties, e.g. --asar.unpackDir=sub_dir - do not use
                   --asar and its sub-properties simultaneously.

                   Properties supported include:
                   - ordering: path to an ordering file for file packing
                   - unpack: unpacks the files to the app.asar.unpacked directory whose filenames
                     regex .match this string
                   - unpackDir: unpacks the dir to the app.asar.unpacked directory whose names glob
                     pattern or exactly match this string. It's relative to the <sourcedir>.
build-version      build version to set for the app
download           a list of sub-options to pass to @electron/get. They are specified via dot
                   notation, e.g., --download.cacheRoot=/tmp/cache
                   Properties supported:
                   - cacheRoot: directory of cached Electron downloads. For default value, see
                     @electron/get documentation
                   - mirrorOptions: alternate URL options for downloading Electron zips. See
                     @electron/get documentation for details
electron-version   the version of Electron that is being packaged, see
                   https://github.com/electron/electron/releases
electron-zip-dir   the local path to a directory containing Electron ZIP files
executable-name    the name of the executable file, sans file extension. Defaults to appname
extra-resource     a file to copy into the app's resources directory
icon               the local path to an icon file to use as the icon for the app.
                   Note: Format depends on platform.
ignore             do not copy files into app whose filenames RegExp.match this string. See also:
                   https://electron.github.io/packager/main/interfaces/Options.html#ignore
                   and --no-prune. Can be specified multiple times
no-deref-symlinks  make sure symlinks are not dereferenced within the app source
no-junk            do not ignore system junk files from the packaged app
no-prune           do not prune devDependencies from the packaged app
out                the dir to put the app into at the end. Defaults to current working dir
overwrite          if output directory for a platform already exists, replaces it rather than
                   skipping it
platform           all, or one or more of: darwin, linux, mas, win32 (comma-delimited if multiple).
                   Defaults to the host platform
prebuilt-asar      path to a prebuilt asar file (asar, ignore, no-prune, and no-deref-symlinks
                   options are incompatible with this option and will be ignored)
quiet              Do not print informational or warning messages
tmpdir             temp directory. Defaults to system temp directory, use --no-tmpdir to disable
                   use of a temporary directory.

* darwin/mas target platforms only *

app-bundle-id      bundle identifier to use in the app plist
app-category-type  the application category type
                   For example, `app-category-type=public.app-category.developer-tools` will set the
                   application category to 'Developer Tools'.
darwin-dark-mode-support
                   forces support for Mojave/10.14 dark mode in the packaged app
extend-info        a plist file to merge into the app plist
helper-bundle-id   bundle identifier to use in the app helper plist
osx-sign           (macOS host platform only) Whether to sign the macOS app packages. You can either
                   pass --osx-sign by itself to use the default configuration, or use dot notation
                   to configure a list of sub-properties, e.g. --osx-sign.identity="My Name"
                   For info on supported values see https://npm.im/@electron/osx-sign#opts---options
                   Properties supported include:
                   - identity: should contain the identity to be used when running `codesign`
                   - entitlements: the path to entitlements used in signing
                   - entitlements-inherit: the path to the 'child' entitlements
osx-notarize       (macOS host platform only, requires --osx-sign) Whether to notarize the macOS app
                   packages. You must use dot notation to configure a list of sub-properties, e.g.
                   --osx-notarize.appleId="<EMAIL>"
                   For info on supported values see https://npm.im/@electron/notarize#method-notarizeopts-promisevoid
                   Properties supported include:
                   - appleId: should contain your apple ID username / email
                   - appleIdPassword: should contain the password for the provided apple ID
                   - appleApiKey: should contain an App Store Connect API key
                   - appleApiIssuer: should contain the API key's issuer
osx-universal      (macOS host platform only, requires --arch=universal) Options to pass to `@electron/universal`
                   when packaging a Universal macOS binary. You must use dot notation to configure a list of sub-properties,
                   e.g. --osx-universal.mergeASARs="true"
                   For info on supported values see
                   https://electron.github.io/packager/main/types/OsxUniversalOptions.html
windows-sign       Whether to sign Windows binary files with a codesigning certificate. You can either
                   pass --windows-sign by itself to use the default configuration or use dot notation to configure
                   a list of sub-properties, e.g. --windows-sign.certificateFile="C:\cert.pfx".
                   For info on supported values see https://npm.im/@electron/windows-sign.
protocol           URL protocol scheme to register the app as an opener of.
                   For example, `--protocol=myapp` would register the app to open
                   URLs such as `myapp://path`. This argument requires a `--protocol-name`
                   argument to also be specified.
protocol-name      Descriptive name of URL protocol scheme specified via `--protocol`
usage-description  Human-readable descriptions of how the app uses certain macOS features. Displayed
                   in the App Store. A non-exhaustive list of properties supported:
                   - Camera
                   - Microphone

* win32 target platform only *

win32metadata      a list of sub-properties used to set the application metadata embedded into
                   the executable. They are specified via dot notation,
                   e.g. --win32metadata.CompanyName="Company Inc."
                   or --win32metadata.ProductName="Product"
                   Properties supported:
                   - CompanyName (default: author name from nearest package.json)
                   - FileDescription (default: appname)
                   - OriginalFilename (default: renamed exe)
                   - ProductName (default: appname)
                   - InternalName (default: appname)
                   - requested-execution-level (user, asInvoker, or requireAdministrator)
                   - application-manifest
