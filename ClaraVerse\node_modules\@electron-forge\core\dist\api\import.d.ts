export interface ImportOptions {
    /**
     * The path to the app to be imported
     */
    dir?: string;
    /**
     * Whether to use sensible defaults or prompt the user visually
     */
    interactive?: boolean;
    /**
     * An async function that returns true or false in order to confirm the start
     * of importing
     */
    confirmImport?: () => Promise<boolean>;
    /**
     * An async function that returns whether the import should continue if it
     * looks like a forge project already
     */
    shouldContinueOnExisting?: () => Promise<boolean>;
    /**
     * An async function that returns whether the given dependency should be removed
     */
    shouldRemoveDependency?: (dependency: string, explanation: string) => Promise<boolean>;
    /**
     * An async function that returns whether the given script should be overridden with a forge one
     */
    shouldUpdateScript?: (scriptName: string, newValue: string) => Promise<boolean>;
    /**
     * The path to the directory containing generated distributables
     */
    outDir?: string;
    /**
     * By default, For<PERSON> initializes a git repository in the project directory. Set this option to `true` to skip this step.
     */
    skipGit?: boolean;
}
declare const _default: (args_0: ImportOptions) => Promise<void>;
export default _default;
//# sourceMappingURL=import.d.ts.map