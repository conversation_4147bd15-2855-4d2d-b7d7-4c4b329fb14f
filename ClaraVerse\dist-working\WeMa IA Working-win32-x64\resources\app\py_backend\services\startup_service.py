"""
🚀 Service de démarrage automatique pour WeMa IA
Gère la vérification des services centraux (SearXNG, LM Studio)
"""

import os
import sys
import time
import asyncio
import logging
import subprocess
import platform
from pathlib import Path
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

class StartupService:
    """Service de démarrage automatique pour WeMa IA"""
    
    def __init__(self):
        self.is_windows = platform.system() == "Windows"
        self.project_root = Path(__file__).parent.parent.parent
        self.searxng_path = self.project_root / "searxng"
        self.startup_status = {
            "searxng_central": False,
            "lm_studio_central": False,
            "search_service": False
        }
    
    async def start_all_services(self) -> Dict[str, Any]:
        """Vérifier la disponibilité des services centraux"""
        logger.info("🚀 Vérification des services centraux WeMa IA...")

        results = {
            "success": True,
            "services": {},
            "errors": []
        }

        try:
            # 1. Vérifier SearXNG Central
            searxng_result = await self._check_searxng_central()
            results["services"]["searxng_central"] = searxng_result

            # 2. Vérifier LM Studio Central
            lm_studio_result = await self._check_lm_studio_central()
            results["services"]["lm_studio_central"] = lm_studio_result

            # 3. Vérifier le service de recherche
            search_result = await self._verify_search_service()
            results["services"]["search"] = search_result

        except Exception as e:
            logger.error(f"❌ Erreur lors de la vérification: {e}")
            results["success"] = False
            results["errors"].append(str(e))

        # Résumé
        self._log_startup_summary(results)
        return results
    
    async def _check_searxng_central(self) -> Dict[str, Any]:
        """Vérifier la disponibilité de SearXNG Central"""
        logger.info("🔍 Vérification de SearXNG Central...")

        try:
            import aiohttp
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get("http://10.4.123.77:8888/search?q=test&format=json") as response:
                    if response.status == 200:
                        logger.info("✅ SearXNG Central disponible")
                        self.startup_status["searxng_central"] = True
                        return {"status": "available", "message": "SearXNG Central opérationnel"}
                    else:
                        logger.warning(f"⚠️ SearXNG Central répond avec status {response.status}")
                        return {"status": "error", "message": f"Status HTTP {response.status}"}
        except Exception as e:
            logger.warning(f"⚠️ SearXNG Central non accessible: {e}")
            return {"status": "unavailable", "message": f"Erreur: {str(e)}"}
    
    async def _check_lm_studio_central(self) -> Dict[str, Any]:
        """Vérifier la disponibilité de LM Studio Central"""
        logger.info("🤖 Vérification de LM Studio Central...")

        try:
            import aiohttp
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=10)) as session:
                async with session.get("http://10.4.123.77:1234/v1/models") as response:
                    if response.status == 200:
                        data = await response.json()
                        models = data.get('data', [])
                        logger.info(f"✅ LM Studio Central disponible ({len(models)} modèles)")
                        self.startup_status["lm_studio_central"] = True
                        return {"status": "available", "message": f"LM Studio Central opérationnel ({len(models)} modèles)"}
                    else:
                        logger.warning(f"⚠️ LM Studio Central répond avec status {response.status}")
                        return {"status": "error", "message": f"Status HTTP {response.status}"}
        except Exception as e:
            logger.warning(f"⚠️ LM Studio Central non accessible: {e}")
            return {"status": "unavailable", "message": f"Erreur: {str(e)}"}
    
    async def _start_docker_unix(self) -> Dict[str, Any]:
        """Démarrer Docker sur Unix/Linux/Mac"""
        logger.info("🐳 Tentative de démarrage du service Docker...")
        
        try:
            # Essayer de démarrer le service Docker
            result = subprocess.run(
                ["sudo", "systemctl", "start", "docker"],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                # Attendre que Docker soit prêt
                await asyncio.sleep(5)
                
                # Vérifier que Docker fonctionne
                check_result = subprocess.run(
                    ["docker", "version"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if check_result.returncode == 0:
                    logger.info("✅ Docker démarré avec succès")
                    self.startup_status["docker"] = True
                    return {"status": "running", "message": "Docker démarré automatiquement"}
            
            logger.warning("⚠️ Impossible de démarrer Docker automatiquement")
            return {"status": "manual_required", "message": "Démarrage manuel de Docker requis"}
            
        except Exception as e:
            logger.error(f"❌ Erreur lors du démarrage de Docker: {e}")
            return {"status": "error", "message": f"Erreur: {str(e)}"}
    
    async def _ensure_searxng_running(self) -> Dict[str, Any]:
        """S'assurer que SearXNG est démarré"""
        logger.info("🔍 Vérification de SearXNG...")
        
        try:
            # Vérifier si SearXNG est déjà en cours d'exécution
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=wema-searxng", "--format", "{{.Status}}"],
                capture_output=True,
                text=True,
                timeout=10,
                cwd=self.searxng_path
            )
            
            if result.returncode == 0 and "Up" in result.stdout:
                logger.info("✅ SearXNG est déjà en cours d'exécution")
                self.startup_status["searxng"] = True
                return {"status": "running", "message": "SearXNG déjà actif"}
            
            # Démarrer SearXNG
            logger.info("🔍 Démarrage de SearXNG...")
            result = subprocess.run(
                ["docker-compose", "up", "-d"],
                capture_output=True,
                text=True,
                timeout=60,
                cwd=self.searxng_path
            )
            
            if result.returncode == 0:
                # Attendre que SearXNG soit prêt
                await asyncio.sleep(10)
                
                # Vérifier que SearXNG répond
                import aiohttp
                async with aiohttp.ClientSession() as session:
                    try:
                        async with session.get("http://localhost:8888", timeout=aiohttp.ClientTimeout(total=10)) as response:
                            if response.status == 200:
                                logger.info("✅ SearXNG démarré avec succès")
                                self.startup_status["searxng"] = True
                                return {"status": "running", "message": "SearXNG démarré automatiquement"}
                    except:
                        pass
                
                logger.warning("⚠️ SearXNG démarré mais pas encore prêt")
                return {"status": "starting", "message": "SearXNG en cours d'initialisation"}
            else:
                logger.error(f"❌ Erreur lors du démarrage de SearXNG: {result.stderr}")
                return {"status": "error", "message": f"Erreur Docker: {result.stderr}"}
                
        except Exception as e:
            logger.error(f"❌ Erreur lors du démarrage de SearXNG: {e}")
            return {"status": "error", "message": f"Erreur: {str(e)}"}
    
    async def _verify_search_service(self) -> Dict[str, Any]:
        """Vérifier que le service de recherche fonctionne"""
        logger.info("🔍 Vérification du service de recherche...")
        
        try:
            from services.internet_search_service import InternetSearchService
            
            async with InternetSearchService() as service:
                is_available = await service.check_availability()
                
            if is_available:
                logger.info("✅ Service de recherche internet opérationnel")
                self.startup_status["search_service"] = True
                return {"status": "operational", "message": "Recherche internet disponible"}
            else:
                logger.info("⚠️ Service de recherche en mode fallback")
                return {"status": "fallback", "message": "Mode fallback activé (résultats d'exemple)"}
                
        except Exception as e:
            logger.error(f"❌ Erreur lors de la vérification du service de recherche: {e}")
            return {"status": "error", "message": f"Erreur: {str(e)}"}
    
    def _log_startup_summary(self, results: Dict[str, Any]):
        """Afficher un résumé de la vérification des services"""
        logger.info("=" * 60)
        logger.info("🚀 RÉSUMÉ DE LA VÉRIFICATION DES SERVICES")
        logger.info("=" * 60)

        for service_name, service_result in results["services"].items():
            status = service_result.get("status", "unknown")
            message = service_result.get("message", "")

            if status == "available" or status == "operational":
                logger.info(f"✅ {service_name.upper().replace('_', ' ')}: {message}")
            elif status == "starting":
                logger.info(f"🔄 {service_name.upper().replace('_', ' ')}: {message}")
            elif status == "fallback":
                logger.info(f"⚠️ {service_name.upper().replace('_', ' ')}: {message}")
            else:
                logger.warning(f"❌ {service_name.upper().replace('_', ' ')}: {message}")

        if results["success"]:
            logger.info("🎉 WeMa IA prêt à l'utilisation !")
        else:
            logger.warning("⚠️ Vérification partielle - certains services peuvent être indisponibles")

        logger.info("=" * 60)
    
    def get_status(self) -> Dict[str, Any]:
        """Obtenir le statut des services"""
        return {
            "searxng_central": self.startup_status["searxng_central"],
            "lm_studio_central": self.startup_status["lm_studio_central"],
            "search_service": self.startup_status["search_service"]
        }

# Instance globale
startup_service = StartupService()
