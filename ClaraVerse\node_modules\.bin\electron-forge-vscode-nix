#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -x "$basedir/bash" ]; then
  exec "$basedir/bash"  "$basedir/../@electron-forge/cli/script/vscode.sh" "$@"
else 
  exec bash  "$basedir/../@electron-forge/cli/script/vscode.sh" "$@"
fi
