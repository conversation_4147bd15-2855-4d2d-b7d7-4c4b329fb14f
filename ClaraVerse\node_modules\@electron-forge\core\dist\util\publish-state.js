"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_crypto_1 = __importDefault(require("node:crypto"));
const node_path_1 = __importDefault(require("node:path"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const EXTENSION = '.forge.publish';
class PublishState {
    static async loadFromDirectory(directory, rootDir) {
        if (!(await fs_extra_1.default.pathExists(directory))) {
            throw new Error(`Attempted to load publish state from a missing directory: ${directory}`);
        }
        const publishes = [];
        for (const dirName of await fs_extra_1.default.readdir(directory)) {
            const subDir = node_path_1.default.resolve(directory, dirName);
            const states = [];
            if ((await fs_extra_1.default.stat(subDir)).isDirectory()) {
                const filePaths = (await fs_extra_1.default.readdir(subDir)).filter((fileName) => fileName.endsWith(EXTENSION)).map((fileName) => node_path_1.default.resolve(subDir, fileName));
                for (const filePath of filePaths) {
                    const state = new PublishState(filePath);
                    await state.load();
                    state.state.artifacts = state.state.artifacts.map((artifactPath) => node_path_1.default.resolve(rootDir, artifactPath));
                    states.push(state);
                }
            }
            publishes.push(states);
        }
        return publishes;
    }
    static async saveToDirectory(directory, artifacts, rootDir) {
        const id = node_crypto_1.default.createHash('SHA256').update(JSON.stringify(artifacts)).digest('hex');
        for (const artifact of artifacts) {
            artifact.artifacts = artifact.artifacts.map((artifactPath) => node_path_1.default.relative(rootDir, artifactPath));
            const publishState = new PublishState(node_path_1.default.resolve(directory, id, 'null'), false);
            publishState.state = artifact;
            await publishState.saveToDisk();
        }
    }
    constructor(filePath, hasHash = true) {
        this.state = {};
        this.dir = node_path_1.default.dirname(filePath);
        this.path = filePath;
        this.hasHash = hasHash;
    }
    generateHash() {
        const content = JSON.stringify(this.state || {});
        return node_crypto_1.default.createHash('SHA256').update(content).digest('hex');
    }
    async load() {
        this.state = await fs_extra_1.default.readJson(this.path);
    }
    async saveToDisk() {
        if (!this.hasHash) {
            this.path = node_path_1.default.resolve(this.dir, `${this.generateHash()}${EXTENSION}`);
            this.hasHash = true;
        }
        await fs_extra_1.default.mkdirs(node_path_1.default.dirname(this.path));
        await fs_extra_1.default.writeJson(this.path, this.state);
    }
}
exports.default = PublishState;
//# sourceMappingURL=data:application/json;base64,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