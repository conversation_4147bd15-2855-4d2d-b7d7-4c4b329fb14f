function g(s,t){var i;const e={totalMessages:t.length,systemPromptLength:s.length,ragMessages:{count:0,totalLength:0,duplicates:0},userMessages:0,assistantMessages:0,duplicatedContent:[],estimatedTokens:0,issues:[],recommendations:[]},a=new Set,l=new Map;for(const n of t){if(n.role==="user"&&e.userMessages++,n.role==="assistant"&&e.assistantMessages++,n.role==="system"&&((i=n.metadata)==null?void 0:i.type)==="rag"){e.ragMessages.count++,e.ragMessages.totalLength+=n.content.length;const u=n.content.substring(0,200);a.has(u)?e.ragMessages.duplicates++:a.add(u)}const o=`${n.role}:${n.content.substring(0,100)}`,c=l.get(o)||0;l.set(o,c+1),c>0&&e.duplicatedContent.push(o)}const r=s.length+t.reduce((n,o)=>n+o.content.length,0);return e.estimatedTokens=Math.ceil(r/4),e.ragMessages.duplicates>0&&e.issues.push(`🚨 ${e.ragMessages.duplicates} documents RAG dupliqués détectés`),e.duplicatedContent.length>0&&e.issues.push(`🚨 ${e.duplicatedContent.length} messages dupliqués détectés`),e.ragMessages.count>0&&s.includes("document")&&e.issues.push("🚨 Possible double injection RAG (system prompt + historique)"),e.estimatedTokens>3e4&&e.issues.push(`⚠️ Contexte très long: ${e.estimatedTokens} tokens estimés`),e.ragMessages.totalLength>e.totalMessages*1e3&&e.issues.push("⚠️ Documents RAG disproportionnés par rapport à la conversation"),e.ragMessages.duplicates>0&&e.recommendations.push("Nettoyer les documents RAG dupliqués"),e.duplicatedContent.length>0&&e.recommendations.push("Implémenter une déduplication plus efficace"),e.estimatedTokens>25e3&&e.recommendations.push("Activer la compression de contexte"),e.ragMessages.count===0&&e.userMessages>0&&e.recommendations.push("Vérifier si des documents RAG devraient être disponibles"),e}function d(s){console.group("🔍 DIAGNOSTIC DU CONTEXTE"),console.log("📊 Statistiques générales:"),console.log(`  - Messages total: ${s.totalMessages}`),console.log(`  - System prompt: ${s.systemPromptLength} chars`),console.log(`  - Tokens estimés: ${s.estimatedTokens}`),console.log("👥 Répartition des messages:"),console.log(`  - Utilisateur: ${s.userMessages}`),console.log(`  - Assistant: ${s.assistantMessages}`),console.log(`  - RAG: ${s.ragMessages.count} (${s.ragMessages.totalLength} chars)`),s.issues.length>0&&(console.warn("🚨 Problèmes détectés:"),s.issues.forEach(t=>console.warn(`  ${t}`))),s.recommendations.length>0&&(console.info("💡 Recommandations:"),s.recommendations.forEach(t=>console.info(`  ${t}`))),s.duplicatedContent.length>0&&(console.warn("🔄 Contenu dupliqué:"),s.duplicatedContent.slice(0,5).forEach(t=>console.warn(`  ${t.substring(0,80)}...`)),s.duplicatedContent.length>5&&console.warn(`  ... et ${s.duplicatedContent.length-5} autres`)),console.groupEnd()}function p(s,t){const e=g(s,t);return d(e),e}export{g as analyzeContext,p as diagnoseContext,d as logContextDiagnostic};
