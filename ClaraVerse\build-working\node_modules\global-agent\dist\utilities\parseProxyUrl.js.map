{"version": 3, "sources": ["../../src/utilities/parseProxyUrl.js"], "names": ["url", "urlT<PERSON>s", "query", "UnexpectedStateError", "hash", "protocol", "port", "Number", "parseInt", "authorization", "auth", "hostname"], "mappings": ";;;;;;;AAEA;;AAGA;;sBAIgBA,G,IAAgB;AAC9B,QAAMC,SAAS,GAAG,gBAASD,GAAT,CAAlB;;AAEA,MAAIC,SAAS,CAACC,KAAV,KAAoB,IAAxB,EAA8B;AAC5B,UAAM,IAAIC,4BAAJ,CAAyB,qFAAzB,CAAN;AACD;;AAED,MAAIF,SAAS,CAACG,IAAV,KAAmB,IAAvB,EAA6B;AAC3B,UAAM,IAAID,4BAAJ,CAAyB,oFAAzB,CAAN;AACD;;AAED,MAAIF,SAAS,CAACI,QAAV,KAAuB,OAA3B,EAAoC;AAClC,UAAM,IAAIF,4BAAJ,CAAyB,0FAAzB,CAAN;AACD;;AAED,MAAIG,IAAI,GAAG,EAAX;;AAEA,MAAIL,SAAS,CAACK,IAAd,EAAoB;AAClBA,IAAAA,IAAI,GAAGC,MAAM,CAACC,QAAP,CAAgBP,SAAS,CAACK,IAA1B,EAAgC,EAAhC,CAAP;AACD;;AAED,SAAO;AACLG,IAAAA,aAAa,EAAER,SAAS,CAACS,IAAV,IAAkB,IAD5B;AAELC,IAAAA,QAAQ,EAAEV,SAAS,CAACU,QAFf;AAGLL,IAAAA;AAHK,GAAP;AAKD,C", "sourcesContent": ["// @flow\n\nimport {\n  parse as parseUrl,\n} from 'url';\nimport {\n  UnexpectedStateError,\n} from '../errors';\n\nexport default (url: string) => {\n  const urlTokens = parseUrl(url);\n\n  if (urlTokens.query !== null) {\n    throw new UnexpectedStateError('Unsupported `GLOBAL_AGENT.HTTP_PROXY` configuration value: URL must not have query.');\n  }\n\n  if (urlTokens.hash !== null) {\n    throw new UnexpectedStateError('Unsupported `GLOBAL_AGENT.HTTP_PROXY` configuration value: URL must not have hash.');\n  }\n\n  if (urlTokens.protocol !== 'http:') {\n    throw new UnexpectedStateError('Unsupported `GLOBAL_AGENT.HTTP_PROXY` configuration value: URL protocol must be \"http:\".');\n  }\n\n  let port = 80;\n\n  if (urlTokens.port) {\n    port = Number.parseInt(urlTokens.port, 10);\n  }\n\n  return {\n    authorization: urlTokens.auth || null,\n    hostname: urlTokens.hostname,\n    port,\n  };\n};\n"], "file": "parseProxyUrl.js"}