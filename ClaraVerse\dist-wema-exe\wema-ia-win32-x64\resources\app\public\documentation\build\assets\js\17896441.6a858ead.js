(self.webpackChunkdocumentation=self.webpackChunkdocumentation||[]).push([[401],{2941:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>Tn});var s=n(6540),a=n(5500),i=n(9532),o=n(4848);const r=s.createContext(null);function c({children:e,content:t}){const n=function(e){return(0,s.useMemo)((()=>({metadata:e.metadata,frontMatter:e.frontMatter,assets:e.assets,contentTitle:e.contentTitle,toc:e.toc})),[e])}(t);return(0,o.jsx)(r.Provider,{value:n,children:e})}function l(){const e=(0,s.useContext)(r);if(null===e)throw new i.dV("DocProvider");return e}function d(){const{metadata:e,frontMatter:t,assets:n}=l();return(0,o.jsx)(a.be,{title:e.title,description:e.description,keywords:t.keywords,image:n.image??t.image})}var u=n(4164),m=n(4581),h=n(1312),f=n(8774);function p(e){const{permalink:t,title:n,subLabel:s,isNext:a}=e;return(0,o.jsxs)(f.A,{className:(0,u.A)("pagination-nav__link",a?"pagination-nav__link--next":"pagination-nav__link--prev"),to:t,children:[s&&(0,o.jsx)("div",{className:"pagination-nav__sublabel",children:s}),(0,o.jsx)("div",{className:"pagination-nav__label",children:n})]})}function x(e){const{className:t,previous:n,next:s}=e;return(0,o.jsxs)("nav",{className:(0,u.A)(t,"pagination-nav"),"aria-label":(0,h.T)({id:"theme.docs.paginator.navAriaLabel",message:"Docs pages",description:"The ARIA label for the docs pagination"}),children:[n&&(0,o.jsx)(p,{...n,subLabel:(0,o.jsx)(h.A,{id:"theme.docs.paginator.previous",description:"The label used to navigate to the previous doc",children:"Previous"})}),s&&(0,o.jsx)(p,{...s,subLabel:(0,o.jsx)(h.A,{id:"theme.docs.paginator.next",description:"The label used to navigate to the next doc",children:"Next"}),isNext:!0})]})}function g(){const{metadata:e}=l();return(0,o.jsx)(x,{className:"docusaurus-mt-lg",previous:e.previous,next:e.next})}var b=n(4586),j=n(4070),v=n(7559),N=n(3886),A=n(3025);const C={unreleased:function({siteTitle:e,versionMetadata:t}){return(0,o.jsx)(h.A,{id:"theme.docs.versions.unreleasedVersionLabel",description:"The label used to tell the user that he's browsing an unreleased doc version",values:{siteTitle:e,versionLabel:(0,o.jsx)("b",{children:t.label})},children:"This is unreleased documentation for {siteTitle} {versionLabel} version."})},unmaintained:function({siteTitle:e,versionMetadata:t}){return(0,o.jsx)(h.A,{id:"theme.docs.versions.unmaintainedVersionLabel",description:"The label used to tell the user that he's browsing an unmaintained doc version",values:{siteTitle:e,versionLabel:(0,o.jsx)("b",{children:t.label})},children:"This is documentation for {siteTitle} {versionLabel}, which is no longer actively maintained."})}};function y(e){const t=C[e.versionMetadata.banner];return(0,o.jsx)(t,{...e})}function L({versionLabel:e,to:t,onClick:n}){return(0,o.jsx)(h.A,{id:"theme.docs.versions.latestVersionSuggestionLabel",description:"The label used to tell the user to check the latest version",values:{versionLabel:e,latestVersionLink:(0,o.jsx)("b",{children:(0,o.jsx)(f.A,{to:t,onClick:n,children:(0,o.jsx)(h.A,{id:"theme.docs.versions.latestVersionLinkLabel",description:"The label used for the latest version suggestion link label",children:"latest version"})})})},children:"For up-to-date documentation, see the {latestVersionLink} ({versionLabel})."})}function k({className:e,versionMetadata:t}){const{siteConfig:{title:n}}=(0,b.A)(),{pluginId:s}=(0,j.vT)({failfast:!0}),{savePreferredVersionName:a}=(0,N.g1)(s),{latestDocSuggestion:i,latestVersionSuggestion:r}=(0,j.HW)(s),c=i??(l=r).docs.find((e=>e.id===l.mainDocId));var l;return(0,o.jsxs)("div",{className:(0,u.A)(e,v.G.docs.docVersionBanner,"alert alert--warning margin-bottom--md"),role:"alert",children:[(0,o.jsx)("div",{children:(0,o.jsx)(y,{siteTitle:n,versionMetadata:t})}),(0,o.jsx)("div",{className:"margin-top--md",children:(0,o.jsx)(L,{versionLabel:r.label,to:c.path,onClick:()=>a(r.name)})})]})}function w({className:e}){const t=(0,A.r)();return t.banner?(0,o.jsx)(k,{className:e,versionMetadata:t}):null}function _({className:e}){const t=(0,A.r)();return t.badge?(0,o.jsx)("span",{className:(0,u.A)(e,v.G.docs.docVersionBadge,"badge badge--secondary"),children:(0,o.jsx)(h.A,{id:"theme.docs.versionBadge.label",values:{versionLabel:t.label},children:"Version: {versionLabel}"})}):null}const T={tag:"tag_zVej",tagRegular:"tagRegular_sFm0",tagWithCount:"tagWithCount_h2kH"};function B({permalink:e,label:t,count:n,description:s}){return(0,o.jsxs)(f.A,{rel:"tag",href:e,title:s,className:(0,u.A)(T.tag,n?T.tagWithCount:T.tagRegular),children:[t,n&&(0,o.jsx)("span",{children:n})]})}const E={tags:"tags_jXut",tag:"tag_QGVx"};function H({tags:e}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("b",{children:(0,o.jsx)(h.A,{id:"theme.tags.tagsListLabel",description:"The label alongside a tag list",children:"Tags:"})}),(0,o.jsx)("ul",{className:(0,u.A)(E.tags,"padding--none","margin-left--sm"),children:e.map((e=>(0,o.jsx)("li",{className:E.tag,children:(0,o.jsx)(B,{...e})},e.permalink)))})]})}const M={iconEdit:"iconEdit_Z9Sw"};function I({className:e,...t}){return(0,o.jsx)("svg",{fill:"currentColor",height:"20",width:"20",viewBox:"0 0 40 40",className:(0,u.A)(M.iconEdit,e),"aria-hidden":"true",...t,children:(0,o.jsx)("g",{children:(0,o.jsx)("path",{d:"m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"})})})}function S({editUrl:e}){return(0,o.jsxs)(f.A,{to:e,className:v.G.common.editThisPage,children:[(0,o.jsx)(I,{}),(0,o.jsx)(h.A,{id:"theme.common.editThisPage",description:"The link label to edit the current page",children:"Edit this page"})]})}function V(e={}){const{i18n:{currentLocale:t}}=(0,b.A)(),n=function(){const{i18n:{currentLocale:e,localeConfigs:t}}=(0,b.A)();return t[e].calendar}();return new Intl.DateTimeFormat(t,{calendar:n,...e})}function U({lastUpdatedAt:e}){const t=new Date(e),n=V({day:"numeric",month:"short",year:"numeric",timeZone:"UTC"}).format(t);return(0,o.jsx)(h.A,{id:"theme.lastUpdated.atDate",description:"The words used to describe on which date a page has been last updated",values:{date:(0,o.jsx)("b",{children:(0,o.jsx)("time",{dateTime:t.toISOString(),itemProp:"dateModified",children:n})})},children:" on {date}"})}function R({lastUpdatedBy:e}){return(0,o.jsx)(h.A,{id:"theme.lastUpdated.byUser",description:"The words used to describe by who the page has been last updated",values:{user:(0,o.jsx)("b",{children:e})},children:" by {user}"})}function z({lastUpdatedAt:e,lastUpdatedBy:t}){return(0,o.jsxs)("span",{className:v.G.common.lastUpdated,children:[(0,o.jsx)(h.A,{id:"theme.lastUpdated.lastUpdatedAtBy",description:"The sentence used to display when a page has been last updated, and by who",values:{atDate:e?(0,o.jsx)(U,{lastUpdatedAt:e}):"",byUser:t?(0,o.jsx)(R,{lastUpdatedBy:t}):""},children:"Last updated{atDate}{byUser}"}),!1]})}const O={lastUpdated:"lastUpdated_JAkA"};function P({className:e,editUrl:t,lastUpdatedAt:n,lastUpdatedBy:s}){return(0,o.jsxs)("div",{className:(0,u.A)("row",e),children:[(0,o.jsx)("div",{className:"col",children:t&&(0,o.jsx)(S,{editUrl:t})}),(0,o.jsx)("div",{className:(0,u.A)("col",O.lastUpdated),children:(n||s)&&(0,o.jsx)(z,{lastUpdatedAt:n,lastUpdatedBy:s})})]})}function D(){const{metadata:e}=l(),{editUrl:t,lastUpdatedAt:n,lastUpdatedBy:s,tags:a}=e,i=a.length>0,r=!!(t||n||s);return i||r?(0,o.jsxs)("footer",{className:(0,u.A)(v.G.docs.docFooter,"docusaurus-mt-lg"),children:[i&&(0,o.jsx)("div",{className:(0,u.A)("row margin-top--sm",v.G.docs.docFooterTagsRow),children:(0,o.jsx)("div",{className:"col",children:(0,o.jsx)(H,{tags:a})})}),r&&(0,o.jsx)(P,{className:(0,u.A)("margin-top--sm",v.G.docs.docFooterEditMetaRow),editUrl:t,lastUpdatedAt:n,lastUpdatedBy:s})]}):null}var G=n(1422),$=n(6342);function F(e){const t=e.map((e=>({...e,parentIndex:-1,children:[]}))),n=Array(7).fill(-1);t.forEach(((e,t)=>{const s=n.slice(2,e.level);e.parentIndex=Math.max(...s),n[e.level]=t}));const s=[];return t.forEach((e=>{const{parentIndex:n,...a}=e;n>=0?t[n].children.push(a):s.push(a)})),s}function W({toc:e,minHeadingLevel:t,maxHeadingLevel:n}){return e.flatMap((e=>{const s=W({toc:e.children,minHeadingLevel:t,maxHeadingLevel:n});return function(e){return e.level>=t&&e.level<=n}(e)?[{...e,children:s}]:s}))}function q(e){const t=e.getBoundingClientRect();return t.top===t.bottom?q(e.parentNode):t}function J(e,{anchorTopOffset:t}){const n=e.find((e=>q(e).top>=t));if(n){return function(e){return e.top>0&&e.bottom<window.innerHeight/2}(q(n))?n:e[e.indexOf(n)-1]??null}return e[e.length-1]??null}function Z(){const e=(0,s.useRef)(0),{navbar:{hideOnScroll:t}}=(0,$.p)();return(0,s.useEffect)((()=>{e.current=t?0:document.querySelector(".navbar").clientHeight}),[t]),e}function Y(e){const t=(0,s.useRef)(void 0),n=Z();(0,s.useEffect)((()=>{if(!e)return()=>{};const{linkClassName:s,linkActiveClassName:a,minHeadingLevel:i,maxHeadingLevel:o}=e;function r(){const e=function(e){return Array.from(document.getElementsByClassName(e))}(s),r=function({minHeadingLevel:e,maxHeadingLevel:t}){const n=[];for(let s=e;s<=t;s+=1)n.push(`h${s}.anchor`);return Array.from(document.querySelectorAll(n.join()))}({minHeadingLevel:i,maxHeadingLevel:o}),c=J(r,{anchorTopOffset:n.current}),l=e.find((e=>c&&c.id===function(e){return decodeURIComponent(e.href.substring(e.href.indexOf("#")+1))}(e)));e.forEach((e=>{!function(e,n){n?(t.current&&t.current!==e&&t.current.classList.remove(a),e.classList.add(a),t.current=e):e.classList.remove(a)}(e,e===l)}))}return document.addEventListener("scroll",r),document.addEventListener("resize",r),r(),()=>{document.removeEventListener("scroll",r),document.removeEventListener("resize",r)}}),[e,n])}function Q({toc:e,className:t,linkClassName:n,isChild:s}){return e.length?(0,o.jsx)("ul",{className:s?void 0:t,children:e.map((e=>(0,o.jsxs)("li",{children:[(0,o.jsx)(f.A,{to:`#${e.id}`,className:n??void 0,dangerouslySetInnerHTML:{__html:e.value}}),(0,o.jsx)(Q,{isChild:!0,toc:e.children,className:t,linkClassName:n})]},e.id)))}):null}const X=s.memo(Q);function K({toc:e,className:t="table-of-contents table-of-contents__left-border",linkClassName:n="table-of-contents__link",linkActiveClassName:a,minHeadingLevel:i,maxHeadingLevel:r,...c}){const l=(0,$.p)(),d=i??l.tableOfContents.minHeadingLevel,u=r??l.tableOfContents.maxHeadingLevel,m=function({toc:e,minHeadingLevel:t,maxHeadingLevel:n}){return(0,s.useMemo)((()=>W({toc:F(e),minHeadingLevel:t,maxHeadingLevel:n})),[e,t,n])}({toc:e,minHeadingLevel:d,maxHeadingLevel:u});return Y((0,s.useMemo)((()=>{if(n&&a)return{linkClassName:n,linkActiveClassName:a,minHeadingLevel:d,maxHeadingLevel:u}}),[n,a,d,u])),(0,o.jsx)(X,{toc:m,className:t,linkClassName:n,...c})}const ee={tocCollapsibleButton:"tocCollapsibleButton_TO0P",tocCollapsibleButtonExpanded:"tocCollapsibleButtonExpanded_MG3E"};function te({collapsed:e,...t}){return(0,o.jsx)("button",{type:"button",...t,className:(0,u.A)("clean-btn",ee.tocCollapsibleButton,!e&&ee.tocCollapsibleButtonExpanded,t.className),children:(0,o.jsx)(h.A,{id:"theme.TOCCollapsible.toggleButtonLabel",description:"The label used by the button on the collapsible TOC component",children:"On this page"})})}const ne={tocCollapsible:"tocCollapsible_ETCw",tocCollapsibleContent:"tocCollapsibleContent_vkbj",tocCollapsibleExpanded:"tocCollapsibleExpanded_sAul"};function se({toc:e,className:t,minHeadingLevel:n,maxHeadingLevel:s}){const{collapsed:a,toggleCollapsed:i}=(0,G.u)({initialState:!0});return(0,o.jsxs)("div",{className:(0,u.A)(ne.tocCollapsible,!a&&ne.tocCollapsibleExpanded,t),children:[(0,o.jsx)(te,{collapsed:a,onClick:i}),(0,o.jsx)(G.N,{lazy:!0,className:ne.tocCollapsibleContent,collapsed:a,children:(0,o.jsx)(K,{toc:e,minHeadingLevel:n,maxHeadingLevel:s})})]})}const ae={tocMobile:"tocMobile_ITEo"};function ie(){const{toc:e,frontMatter:t}=l();return(0,o.jsx)(se,{toc:e,minHeadingLevel:t.toc_min_heading_level,maxHeadingLevel:t.toc_max_heading_level,className:(0,u.A)(v.G.docs.docTocMobile,ae.tocMobile)})}const oe={tableOfContents:"tableOfContents_bqdL",docItemContainer:"docItemContainer_F8PC"},re="table-of-contents__link toc-highlight",ce="table-of-contents__link--active";function le({className:e,...t}){return(0,o.jsx)("div",{className:(0,u.A)(oe.tableOfContents,"thin-scrollbar",e),children:(0,o.jsx)(K,{...t,linkClassName:re,linkActiveClassName:ce})})}function de(){const{toc:e,frontMatter:t}=l();return(0,o.jsx)(le,{toc:e,minHeadingLevel:t.toc_min_heading_level,maxHeadingLevel:t.toc_max_heading_level,className:v.G.docs.docTocDesktop})}var ue=n(1107),me=n(8453),he=n(5260),fe=n(2303),pe=n(5293);function xe(){const{prism:e}=(0,$.p)(),{colorMode:t}=(0,pe.G)(),n=e.theme,s=e.darkTheme||n;return"dark"===t?s:n}var ge=n(8426),be=n.n(ge);const je=/title=(?<quote>["'])(?<title>.*?)\1/,ve=/\{(?<range>[\d,-]+)\}/,Ne={js:{start:"\\/\\/",end:""},jsBlock:{start:"\\/\\*",end:"\\*\\/"},jsx:{start:"\\{\\s*\\/\\*",end:"\\*\\/\\s*\\}"},bash:{start:"#",end:""},html:{start:"\x3c!--",end:"--\x3e"}},Ae={...Ne,lua:{start:"--",end:""},wasm:{start:"\\;\\;",end:""},tex:{start:"%",end:""},vb:{start:"['\u2018\u2019]",end:""},vbnet:{start:"(?:_\\s*)?['\u2018\u2019]",end:""},rem:{start:"[Rr][Ee][Mm]\\b",end:""},f90:{start:"!",end:""},ml:{start:"\\(\\*",end:"\\*\\)"},cobol:{start:"\\*>",end:""}},Ce=Object.keys(Ne);function ye(e,t){const n=e.map((e=>{const{start:n,end:s}=Ae[e];return`(?:${n}\\s*(${t.flatMap((e=>[e.line,e.block?.start,e.block?.end].filter(Boolean))).join("|")})\\s*${s})`})).join("|");return new RegExp(`^\\s*(?:${n})\\s*$`)}function Le({showLineNumbers:e,metastring:t}){return"boolean"==typeof e?e?1:void 0:"number"==typeof e?e:function(e){const t=e?.split(" ").find((e=>e.startsWith("showLineNumbers")));if(t){if(t.startsWith("showLineNumbers=")){const e=t.replace("showLineNumbers=","");return parseInt(e,10)}return 1}}(t)}function ke(e,t){const{language:n,magicComments:s}=t;if(void 0===n)return{lineClassNames:{},code:e};const a=function(e,t){switch(e){case"js":case"javascript":case"ts":case"typescript":return ye(["js","jsBlock"],t);case"jsx":case"tsx":return ye(["js","jsBlock","jsx"],t);case"html":return ye(["js","jsBlock","html"],t);case"python":case"py":case"bash":return ye(["bash"],t);case"markdown":case"md":return ye(["html","jsx","bash"],t);case"tex":case"latex":case"matlab":return ye(["tex"],t);case"lua":case"haskell":return ye(["lua"],t);case"sql":return ye(["lua","jsBlock"],t);case"wasm":return ye(["wasm"],t);case"vb":case"vba":case"visual-basic":return ye(["vb","rem"],t);case"vbnet":return ye(["vbnet","rem"],t);case"batch":return ye(["rem"],t);case"basic":return ye(["rem","f90"],t);case"fsharp":return ye(["js","ml"],t);case"ocaml":case"sml":return ye(["ml"],t);case"fortran":return ye(["f90"],t);case"cobol":return ye(["cobol"],t);default:return ye(Ce,t)}}(n,s),i=e.split(/\r?\n/),o=Object.fromEntries(s.map((e=>[e.className,{start:0,range:""}]))),r=Object.fromEntries(s.filter((e=>e.line)).map((({className:e,line:t})=>[t,e]))),c=Object.fromEntries(s.filter((e=>e.block)).map((({className:e,block:t})=>[t.start,e]))),l=Object.fromEntries(s.filter((e=>e.block)).map((({className:e,block:t})=>[t.end,e])));for(let u=0;u<i.length;){const e=i[u].match(a);if(!e){u+=1;continue}const t=e.slice(1).find((e=>void 0!==e));r[t]?o[r[t]].range+=`${u},`:c[t]?o[c[t]].start=u:l[t]&&(o[l[t]].range+=`${o[l[t]].start}-${u-1},`),i.splice(u,1)}const d={};return Object.entries(o).forEach((([e,{range:t}])=>{be()(t).forEach((t=>{d[t]??=[],d[t].push(e)}))})),{code:i.join("\n"),lineClassNames:d}}function we(e,t){const n=e.replace(/\r?\n$/,"");return function(e,{metastring:t,magicComments:n}){if(t&&ve.test(t)){const s=t.match(ve).groups.range;if(0===n.length)throw new Error(`A highlight range has been given in code block's metastring (\`\`\` ${t}), but no magic comment config is available. Docusaurus applies the first magic comment entry's className for metastring ranges.`);const a=n[0].className,i=be()(s).filter((e=>e>0)).map((e=>[e-1,[a]]));return{lineClassNames:Object.fromEntries(i),code:e}}return null}(n,{...t})??ke(n,{...t})}function _e(e){const t=function(e){return t=e.language??function(e){if(!e)return;const t=e.split(" ").find((e=>e.startsWith("language-")));return t?.replace(/language-/,"")}(e.className)??e.defaultLanguage,t?.toLowerCase()??"text";var t}({language:e.language,defaultLanguage:e.defaultLanguage,className:e.className}),{lineClassNames:n,code:s}=we(e.code,{metastring:e.metastring,magicComments:e.magicComments,language:t}),a=function({className:e,language:t}){return(0,u.A)(e,t&&!e?.includes(`language-${t}`)&&`language-${t}`)}({className:e.className,language:t}),i=(o=e.metastring,(o?.match(je)?.groups.title??"")||e.title);var o;const r=Le({showLineNumbers:e.showLineNumbers,metastring:e.metastring});return{codeInput:e.code,code:s,className:a,language:t,title:i,lineNumbersStart:r,lineClassNames:n}}const Te=(0,s.createContext)(null);function Be({metadata:e,wordWrap:t,children:n}){const a=(0,s.useMemo)((()=>({metadata:e,wordWrap:t})),[e,t]);return(0,o.jsx)(Te.Provider,{value:a,children:n})}function Ee(){const e=(0,s.useContext)(Te);if(null===e)throw new i.dV("CodeBlockContextProvider");return e}const He="codeBlockContainer_Ckt0";function Me({as:e,...t}){const n=function(e){const t={color:"--prism-color",backgroundColor:"--prism-background-color"},n={};return Object.entries(e.plain).forEach((([e,s])=>{const a=t[e];a&&"string"==typeof s&&(n[a]=s)})),n}(xe());return(0,o.jsx)(e,{...t,style:n,className:(0,u.A)(t.className,He,v.G.common.codeBlock)})}const Ie="codeBlock_bY9V",Se="codeBlockStandalone_MEMb",Ve="codeBlockLines_e6Vv",Ue="codeBlockLinesWithNumbering_o6Pm";function Re({children:e,className:t}){return(0,o.jsx)(Me,{as:"pre",tabIndex:0,className:(0,u.A)(Se,"thin-scrollbar",t),children:(0,o.jsx)("code",{className:Ve,children:e})})}const ze={attributes:!0,characterData:!0,childList:!0,subtree:!0};function Oe(e,t){const[n,a]=(0,s.useState)(),o=(0,s.useCallback)((()=>{a(e.current?.closest("[role=tabpanel][hidden]"))}),[e,a]);(0,s.useEffect)((()=>{o()}),[o]),function(e,t,n=ze){const a=(0,i._q)(t),o=(0,i.Be)(n);(0,s.useEffect)((()=>{const t=new MutationObserver(a);return e&&t.observe(e,o),()=>t.disconnect()}),[e,a,o])}(n,(e=>{e.forEach((e=>{"attributes"===e.type&&"hidden"===e.attributeName&&(t(),o())}))}),{attributes:!0,characterData:!1,childList:!1,subtree:!1})}function Pe({children:e}){return e}var De=n(1765);function Ge({line:e,token:t,...n}){return(0,o.jsx)("span",{...n})}const $e="codeLine_lJS_",Fe="codeLineNumber_Tfdd",We="codeLineContent_feaV";function qe({line:e,classNames:t,showLineNumbers:n,getLineProps:s,getTokenProps:a}){const i=function(e){const t=1===e.length&&"\n"===e[0].content?e[0]:void 0;return t?[{...t,content:""}]:e}(e),r=s({line:i,className:(0,u.A)(t,n&&$e)}),c=i.map(((e,t)=>{const n=a({token:e});return(0,o.jsx)(Ge,{...n,line:i,token:e,children:n.children},t)}));return(0,o.jsxs)("span",{...r,children:[n?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{className:Fe}),(0,o.jsx)("span",{className:We,children:c})]}):c,(0,o.jsx)("br",{})]})}const Je=s.forwardRef(((e,t)=>(0,o.jsx)("pre",{ref:t,tabIndex:0,...e,className:(0,u.A)(e.className,Ie,"thin-scrollbar")})));function Ze(e){const{metadata:t}=Ee();return(0,o.jsx)("code",{...e,className:(0,u.A)(e.className,Ve,void 0!==t.lineNumbersStart&&Ue),style:{...e.style,counterReset:void 0===t.lineNumbersStart?void 0:"line-count "+(t.lineNumbersStart-1)}})}function Ye({className:e}){const{metadata:t,wordWrap:n}=Ee(),s=xe(),{code:a,language:i,lineNumbersStart:r,lineClassNames:c}=t;return(0,o.jsx)(De.f4,{theme:s,code:a,language:i,children:({className:t,style:s,tokens:a,getLineProps:i,getTokenProps:l})=>(0,o.jsx)(Je,{ref:n.codeBlockRef,className:(0,u.A)(e,t),style:s,children:(0,o.jsx)(Ze,{children:a.map(((e,t)=>(0,o.jsx)(qe,{line:e,getLineProps:i,getTokenProps:l,classNames:c[t],showLineNumbers:void 0!==r},t)))})})})}function Qe({children:e,fallback:t}){return(0,fe.A)()?(0,o.jsx)(o.Fragment,{children:e?.()}):t??null}function Xe({className:e,...t}){return(0,o.jsx)("button",{type:"button",...t,className:(0,u.A)("clean-btn",e)})}function Ke(e){return(0,o.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,o.jsx)("path",{fill:"currentColor",d:"M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"})})}function et(e){return(0,o.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,o.jsx)("path",{fill:"currentColor",d:"M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"})})}const tt={copyButtonCopied:"copyButtonCopied_Vdqa",copyButtonIcons:"copyButtonIcons_IEyt",copyButtonIcon:"copyButtonIcon_TrPX",copyButtonSuccessIcon:"copyButtonSuccessIcon_cVMy"};function nt(e){return e?(0,h.T)({id:"theme.CodeBlock.copied",message:"Copied",description:"The copied button label on code blocks"}):(0,h.T)({id:"theme.CodeBlock.copyButtonAriaLabel",message:"Copy code to clipboard",description:"The ARIA label for copy code blocks button"})}function st(){const{metadata:{code:e}}=Ee(),[t,n]=(0,s.useState)(!1),a=(0,s.useRef)(void 0),i=(0,s.useCallback)((()=>{!function(e,{target:t=document.body}={}){if("string"!=typeof e)throw new TypeError(`Expected parameter \`text\` to be a \`string\`, got \`${typeof e}\`.`);const n=document.createElement("textarea"),s=document.activeElement;n.value=e,n.setAttribute("readonly",""),n.style.contain="strict",n.style.position="absolute",n.style.left="-9999px",n.style.fontSize="12pt";const a=document.getSelection(),i=a.rangeCount>0&&a.getRangeAt(0);t.append(n),n.select(),n.selectionStart=0,n.selectionEnd=e.length;let o=!1;try{o=document.execCommand("copy")}catch{}n.remove(),i&&(a.removeAllRanges(),a.addRange(i)),s&&s.focus()}(e),n(!0),a.current=window.setTimeout((()=>{n(!1)}),1e3)}),[e]);return(0,s.useEffect)((()=>()=>window.clearTimeout(a.current)),[]),{copyCode:i,isCopied:t}}function at({className:e}){const{copyCode:t,isCopied:n}=st();return(0,o.jsx)(Xe,{"aria-label":nt(n),title:(0,h.T)({id:"theme.CodeBlock.copy",message:"Copy",description:"The copy button label on code blocks"}),className:(0,u.A)(e,tt.copyButton,n&&tt.copyButtonCopied),onClick:t,children:(0,o.jsxs)("span",{className:tt.copyButtonIcons,"aria-hidden":"true",children:[(0,o.jsx)(Ke,{className:tt.copyButtonIcon}),(0,o.jsx)(et,{className:tt.copyButtonSuccessIcon})]})})}function it(e){return(0,o.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,o.jsx)("path",{fill:"currentColor",d:"M4 19h6v-2H4v2zM20 5H4v2h16V5zm-3 6H4v2h13.25c1.1 0 2 .9 2 2s-.9 2-2 2H15v-2l-3 3l3 3v-2h2c2.21 0 4-1.79 4-4s-1.79-4-4-4z"})})}const ot="wordWrapButtonIcon_b1P5",rt="wordWrapButtonEnabled_uzNF";function ct({className:e}){const{wordWrap:t}=Ee();if(!(t.isEnabled||t.isCodeScrollable))return!1;const n=(0,h.T)({id:"theme.CodeBlock.wordWrapToggle",message:"Toggle word wrap",description:"The title attribute for toggle word wrapping button of code block lines"});return(0,o.jsx)(Xe,{onClick:()=>t.toggle(),className:(0,u.A)(e,t.isEnabled&&rt),"aria-label":n,title:n,children:(0,o.jsx)(it,{className:ot,"aria-hidden":"true"})})}const lt="buttonGroup_M5ko";function dt({className:e}){return(0,o.jsx)(Qe,{children:()=>(0,o.jsxs)("div",{className:(0,u.A)(e,lt),children:[(0,o.jsx)(ct,{}),(0,o.jsx)(at,{})]})})}const ut="codeBlockContent_QJqH",mt="codeBlockTitle_OeMC";function ht({className:e}){const{metadata:t}=Ee();return(0,o.jsxs)(Me,{as:"div",className:(0,u.A)(e,t.className),children:[t.title&&(0,o.jsx)("div",{className:mt,children:(0,o.jsx)(Pe,{children:t.title})}),(0,o.jsxs)("div",{className:ut,children:[(0,o.jsx)(Ye,{}),(0,o.jsx)(dt,{})]})]})}function ft(e){const t=function(e){const{prism:t}=(0,$.p)();return _e({code:e.children,className:e.className,metastring:e.metastring,magicComments:t.magicComments,defaultLanguage:t.defaultLanguage,language:e.language,title:e.title,showLineNumbers:e.showLineNumbers})}(e),n=function(){const[e,t]=(0,s.useState)(!1),[n,a]=(0,s.useState)(!1),i=(0,s.useRef)(null),o=(0,s.useCallback)((()=>{const n=i.current.querySelector("code");e?n.removeAttribute("style"):(n.style.whiteSpace="pre-wrap",n.style.overflowWrap="anywhere"),t((e=>!e))}),[i,e]),r=(0,s.useCallback)((()=>{const{scrollWidth:e,clientWidth:t}=i.current,n=e>t||i.current.querySelector("code").hasAttribute("style");a(n)}),[i]);return Oe(i,r),(0,s.useEffect)((()=>{r()}),[e,r]),(0,s.useEffect)((()=>(window.addEventListener("resize",r,{passive:!0}),()=>{window.removeEventListener("resize",r)})),[r]),{codeBlockRef:i,isEnabled:e,isCodeScrollable:n,toggle:o}}();return(0,o.jsx)(Be,{metadata:t,wordWrap:n,children:(0,o.jsx)(ht,{})})}function pt({children:e,...t}){const n=(0,fe.A)(),a=function(e){return s.Children.toArray(e).some((e=>(0,s.isValidElement)(e)))?e:Array.isArray(e)?e.join(""):e}(e),i="string"==typeof a?ft:Re;return(0,o.jsx)(i,{...t,children:a},String(n))}function xt(e){return(0,o.jsx)("code",{...e})}var gt=n(3427);const bt="details_lb9f",jt="isBrowser_bmU9",vt="collapsibleContent_i85q";function Nt(e){return!!e&&("SUMMARY"===e.tagName||Nt(e.parentElement))}function At(e,t){return!!e&&(e===t||At(e.parentElement,t))}function Ct({summary:e,children:t,...n}){(0,gt.A)().collectAnchor(n.id);const a=(0,fe.A)(),i=(0,s.useRef)(null),{collapsed:r,setCollapsed:c}=(0,G.u)({initialState:!n.open}),[l,d]=(0,s.useState)(n.open),m=s.isValidElement(e)?e:(0,o.jsx)("summary",{children:e??"Details"});return(0,o.jsxs)("details",{...n,ref:i,open:l,"data-collapsed":r,className:(0,u.A)(bt,a&&jt,n.className),onMouseDown:e=>{Nt(e.target)&&e.detail>1&&e.preventDefault()},onClick:e=>{e.stopPropagation();const t=e.target;Nt(t)&&At(t,i.current)&&(e.preventDefault(),r?(c(!1),d(!0)):c(!0))},children:[m,(0,o.jsx)(G.N,{lazy:!1,collapsed:r,onCollapseTransitionEnd:e=>{c(e),d(!e)},children:(0,o.jsx)("div",{className:vt,children:t})})]})}const yt="details_b_Ee";function Lt({...e}){return(0,o.jsx)(Ct,{...e,className:(0,u.A)("alert alert--info",yt,e.className)})}function kt(e){const t=s.Children.toArray(e.children),n=t.find((e=>s.isValidElement(e)&&"summary"===e.type)),a=(0,o.jsx)(o.Fragment,{children:t.filter((e=>e!==n))});return(0,o.jsx)(Lt,{...e,summary:n,children:a})}function wt(e){return(0,o.jsx)(ue.A,{...e})}const _t="containsTaskList_mC6p";function Tt(e){if(void 0!==e)return(0,u.A)(e,e?.includes("contains-task-list")&&_t)}const Bt="img_ev3q";function Et(e){const{mdxAdmonitionTitle:t,rest:n}=function(e){const t=s.Children.toArray(e),n=t.find((e=>s.isValidElement(e)&&"mdxAdmonitionTitle"===e.type)),a=t.filter((e=>e!==n)),i=n?.props.children;return{mdxAdmonitionTitle:i,rest:a.length>0?(0,o.jsx)(o.Fragment,{children:a}):null}}(e.children),a=e.title??t;return{...e,...a&&{title:a},children:n}}const Ht="admonition_xJq3",Mt="admonitionHeading_Gvgb",It="admonitionIcon_Rf37",St="admonitionContent_BuS1";function Vt({type:e,className:t,children:n}){return(0,o.jsx)("div",{className:(0,u.A)(v.G.common.admonition,v.G.common.admonitionType(e),Ht,t),children:n})}function Ut({icon:e,title:t}){return(0,o.jsxs)("div",{className:Mt,children:[(0,o.jsx)("span",{className:It,children:e}),t]})}function Rt({children:e}){return e?(0,o.jsx)("div",{className:St,children:e}):null}function zt(e){const{type:t,icon:n,title:s,children:a,className:i}=e;return(0,o.jsxs)(Vt,{type:t,className:i,children:[s||n?(0,o.jsx)(Ut,{title:s,icon:n}):null,(0,o.jsx)(Rt,{children:a})]})}function Ot(e){return(0,o.jsx)("svg",{viewBox:"0 0 14 16",...e,children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M6.3 5.69a.942.942 0 0 1-.28-.7c0-.28.09-.52.28-.7.19-.18.42-.28.7-.28.28 0 .52.09.7.28.18.***********.7 0 .28-.09.52-.28.7a1 1 0 0 1-.7.3c-.28 0-.52-.11-.7-.3zM8 7.99c-.02-.25-.11-.48-.31-.69-.2-.19-.42-.3-.69-.31H6c-.27.02-.48.13-.69.31-.2.2-.3.44-.31.69h1v3c.**********.*********.42.31.69.31h1c.27 0 .48-.11.69-.31.2-.19.3-.42.31-.69H8V7.98v.01zM7 2.3c-3.14 0-5.7 2.54-5.7 5.68 0 3.14 2.56 5.7 5.7 5.7s5.7-2.55 5.7-5.7c0-3.15-2.56-5.69-5.7-5.69v.01zM7 .98c3.86 0 7 3.14 7 7s-3.14 7-7 7-7-3.12-7-7 3.14-7 7-7z"})})}const Pt={icon:(0,o.jsx)(Ot,{}),title:(0,o.jsx)(h.A,{id:"theme.admonition.note",description:"The default label used for the Note admonition (:::note)",children:"note"})};function Dt(e){return(0,o.jsx)(zt,{...Pt,...e,className:(0,u.A)("alert alert--secondary",e.className),children:e.children})}function Gt(e){return(0,o.jsx)("svg",{viewBox:"0 0 12 16",...e,children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M6.5 0C3.48 0 1 2.19 1 5c0 .92.55 2.25 1 3 1.34 2.25 1.78 2.78 2 4v1h5v-1c.22-1.22.66-1.75 2-4 .45-.75 1-2.08 1-3 0-2.81-2.48-5-5.5-5zm3.64 7.48c-.25.44-.47.8-.67 1.11-.86 1.41-1.25 2.06-1.45 3.23-.02.05-.02.11-.02.17H5c0-.06 0-.13-.02-.17-.2-1.17-.59-1.83-1.45-3.23-.2-.31-.42-.67-.67-1.11C2.44 6.78 2 5.65 2 5c0-2.2 2.02-4 4.5-4 1.22 0 2.36.42 3.22 1.19C10.55 2.94 11 3.94 11 5c0 .66-.44 1.78-.86 2.48zM4 14h5c-.23 1.14-1.3 2-2.5 2s-2.27-.86-2.5-2z"})})}const $t={icon:(0,o.jsx)(Gt,{}),title:(0,o.jsx)(h.A,{id:"theme.admonition.tip",description:"The default label used for the Tip admonition (:::tip)",children:"tip"})};function Ft(e){return(0,o.jsx)(zt,{...$t,...e,className:(0,u.A)("alert alert--success",e.className),children:e.children})}function Wt(e){return(0,o.jsx)("svg",{viewBox:"0 0 14 16",...e,children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M7 2.3c3.14 0 5.7 2.56 5.7 5.7s-2.56 5.7-5.7 5.7A5.71 5.71 0 0 1 1.3 8c0-3.14 2.56-5.7 5.7-5.7zM7 1C3.14 1 0 4.14 0 8s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm1 3H6v5h2V4zm0 6H6v2h2v-2z"})})}const qt={icon:(0,o.jsx)(Wt,{}),title:(0,o.jsx)(h.A,{id:"theme.admonition.info",description:"The default label used for the Info admonition (:::info)",children:"info"})};function Jt(e){return(0,o.jsx)(zt,{...qt,...e,className:(0,u.A)("alert alert--info",e.className),children:e.children})}function Zt(e){return(0,o.jsx)("svg",{viewBox:"0 0 16 16",...e,children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M8.893 1.5c-.183-.31-.52-.5-.887-.5s-.703.19-.886.5L.138 13.499a.98.98 0 0 0 0 1.001c.193.31.53.501.886.501h13.964c.367 0 .704-.19.877-.5a1.03 1.03 0 0 0 .01-1.002L8.893 1.5zm.133 11.497H6.987v-2.003h2.039v2.003zm0-3.004H6.987V5.987h2.039v4.006z"})})}const Yt={icon:(0,o.jsx)(Zt,{}),title:(0,o.jsx)(h.A,{id:"theme.admonition.warning",description:"The default label used for the Warning admonition (:::warning)",children:"warning"})};function Qt(e){return(0,o.jsx)("svg",{viewBox:"0 0 12 16",...e,children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M5.05.31c.81 2.17.41 3.38-.52 4.31C3.55 5.67 1.98 6.45.9 7.98c-1.45 2.05-1.7 6.53 3.53 7.7-2.2-1.16-2.67-4.52-.3-6.61-.61 2.03.53 3.33 1.94 2.86 1.39-.47 2.3.53 2.27 1.67-.02.78-.31 1.44-1.13 1.81 3.42-.59 4.78-3.42 4.78-5.56 0-2.84-2.53-3.22-1.25-5.61-1.52.13-2.03 1.13-1.89 2.75.09 1.08-1.02 1.8-1.86 1.33-.67-.41-.66-1.19-.06-1.78C8.18 5.31 8.68 2.45 5.05.32L5.03.3l.02.01z"})})}const Xt={icon:(0,o.jsx)(Qt,{}),title:(0,o.jsx)(h.A,{id:"theme.admonition.danger",description:"The default label used for the Danger admonition (:::danger)",children:"danger"})};const Kt={icon:(0,o.jsx)(Zt,{}),title:(0,o.jsx)(h.A,{id:"theme.admonition.caution",description:"The default label used for the Caution admonition (:::caution)",children:"caution"})};const en={...{note:Dt,tip:Ft,info:Jt,warning:function(e){return(0,o.jsx)(zt,{...Yt,...e,className:(0,u.A)("alert alert--warning",e.className),children:e.children})},danger:function(e){return(0,o.jsx)(zt,{...Xt,...e,className:(0,u.A)("alert alert--danger",e.className),children:e.children})}},...{secondary:e=>(0,o.jsx)(Dt,{title:"secondary",...e}),important:e=>(0,o.jsx)(Jt,{title:"important",...e}),success:e=>(0,o.jsx)(Ft,{title:"success",...e}),caution:function(e){return(0,o.jsx)(zt,{...Kt,...e,className:(0,u.A)("alert alert--warning",e.className),children:e.children})}}};function tn(e){const t=Et(e),n=(s=t.type,en[s]||(console.warn(`No admonition component found for admonition type "${s}". Using Info as fallback.`),en.info));var s;return(0,o.jsx)(n,{...t})}var nn=n(418);const sn={Head:he.A,details:kt,Details:kt,code:function(e){return function(e){return void 0!==e.children&&s.Children.toArray(e.children).every((e=>"string"==typeof e&&!e.includes("\n")))}(e)?(0,o.jsx)(xt,{...e}):(0,o.jsx)(pt,{...e})},a:function(e){return(0,o.jsx)(f.A,{...e})},pre:function(e){return(0,o.jsx)(o.Fragment,{children:e.children})},ul:function(e){return(0,o.jsx)("ul",{...e,className:Tt(e.className)})},li:function(e){return(0,gt.A)().collectAnchor(e.id),(0,o.jsx)("li",{...e})},img:function(e){return(0,o.jsx)("img",{decoding:"async",loading:"lazy",...e,className:(t=e.className,(0,u.A)(t,Bt))});var t},h1:e=>(0,o.jsx)(wt,{as:"h1",...e}),h2:e=>(0,o.jsx)(wt,{as:"h2",...e}),h3:e=>(0,o.jsx)(wt,{as:"h3",...e}),h4:e=>(0,o.jsx)(wt,{as:"h4",...e}),h5:e=>(0,o.jsx)(wt,{as:"h5",...e}),h6:e=>(0,o.jsx)(wt,{as:"h6",...e}),admonition:tn,mermaid:nn.A};function an({children:e}){return(0,o.jsx)(me.x,{components:sn,children:e})}function on({children:e}){const t=function(){const{metadata:e,frontMatter:t,contentTitle:n}=l();return t.hide_title||void 0!==n?null:e.title}();return(0,o.jsxs)("div",{className:(0,u.A)(v.G.docs.docMarkdown,"markdown"),children:[t&&(0,o.jsx)("header",{children:(0,o.jsx)(ue.A,{as:"h1",children:t})}),(0,o.jsx)(an,{children:e})]})}var rn=n(4718),cn=n(9169),ln=n(6025);function dn(e){return(0,o.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,o.jsx)("path",{d:"M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z",fill:"currentColor"})})}const un={breadcrumbHomeIcon:"breadcrumbHomeIcon_YNFT"};function mn(){const e=(0,ln.Ay)("/");return(0,o.jsx)("li",{className:"breadcrumbs__item",children:(0,o.jsx)(f.A,{"aria-label":(0,h.T)({id:"theme.docs.breadcrumbs.home",message:"Home page",description:"The ARIA label for the home page in the breadcrumbs"}),className:"breadcrumbs__link",href:e,children:(0,o.jsx)(dn,{className:un.breadcrumbHomeIcon})})})}function hn(e){const t=function({breadcrumbs:e}){const{siteConfig:t}=(0,b.A)();return{"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:e.filter((e=>e.href)).map(((e,n)=>({"@type":"ListItem",position:n+1,name:e.label,item:`${t.url}${e.href}`})))}}({breadcrumbs:e.breadcrumbs});return(0,o.jsx)(he.A,{children:(0,o.jsx)("script",{type:"application/ld+json",children:JSON.stringify(t)})})}const fn={breadcrumbsContainer:"breadcrumbsContainer_Z_bl"};function pn({children:e,href:t,isLast:n}){const s="breadcrumbs__link";return n?(0,o.jsx)("span",{className:s,children:e}):t?(0,o.jsx)(f.A,{className:s,href:t,children:(0,o.jsx)("span",{children:e})}):(0,o.jsx)("span",{className:s,children:e})}function xn({children:e,active:t}){return(0,o.jsx)("li",{className:(0,u.A)("breadcrumbs__item",{"breadcrumbs__item--active":t}),children:e})}function gn(){const e=(0,rn.OF)(),t=(0,cn.Dt)();return e?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(hn,{breadcrumbs:e}),(0,o.jsx)("nav",{className:(0,u.A)(v.G.docs.docBreadcrumbs,fn.breadcrumbsContainer),"aria-label":(0,h.T)({id:"theme.docs.breadcrumbs.navAriaLabel",message:"Breadcrumbs",description:"The ARIA label for the breadcrumbs"}),children:(0,o.jsxs)("ul",{className:"breadcrumbs",children:[t&&(0,o.jsx)(mn,{}),e.map(((t,n)=>{const s=n===e.length-1,a="category"===t.type&&t.linkUnlisted?void 0:t.href;return(0,o.jsx)(xn,{active:s,children:(0,o.jsx)(pn,{href:a,isLast:s,children:t.label})},n)}))]})})]}):null}function bn(){return(0,o.jsx)(h.A,{id:"theme.contentVisibility.unlistedBanner.title",description:"The unlisted content banner title",children:"Unlisted page"})}function jn(){return(0,o.jsx)(h.A,{id:"theme.contentVisibility.unlistedBanner.message",description:"The unlisted content banner message",children:"This page is unlisted. Search engines will not index it, and only users having a direct link can access it."})}function vn(){return(0,o.jsx)(he.A,{children:(0,o.jsx)("meta",{name:"robots",content:"noindex, nofollow"})})}function Nn(){return(0,o.jsx)(h.A,{id:"theme.contentVisibility.draftBanner.title",description:"The draft content banner title",children:"Draft page"})}function An(){return(0,o.jsx)(h.A,{id:"theme.contentVisibility.draftBanner.message",description:"The draft content banner message",children:"This page is a draft. It will only be visible in dev and be excluded from the production build."})}function Cn({className:e}){return(0,o.jsx)(tn,{type:"caution",title:(0,o.jsx)(Nn,{}),className:(0,u.A)(e,v.G.common.draftBanner),children:(0,o.jsx)(An,{})})}function yn({className:e}){return(0,o.jsx)(tn,{type:"caution",title:(0,o.jsx)(bn,{}),className:(0,u.A)(e,v.G.common.unlistedBanner),children:(0,o.jsx)(jn,{})})}function Ln(e){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(vn,{}),(0,o.jsx)(yn,{...e})]})}function kn({metadata:e}){const{unlisted:t,frontMatter:n}=e;return(0,o.jsxs)(o.Fragment,{children:[(t||n.unlisted)&&(0,o.jsx)(Ln,{}),n.draft&&(0,o.jsx)(Cn,{})]})}const wn={docItemContainer:"docItemContainer_Djhp",docItemCol:"docItemCol_VOVn"};function _n({children:e}){const t=function(){const{frontMatter:e,toc:t}=l(),n=(0,m.l)(),s=e.hide_table_of_contents,a=!s&&t.length>0;return{hidden:s,mobile:a?(0,o.jsx)(ie,{}):void 0,desktop:!a||"desktop"!==n&&"ssr"!==n?void 0:(0,o.jsx)(de,{})}}(),{metadata:n}=l();return(0,o.jsxs)("div",{className:"row",children:[(0,o.jsxs)("div",{className:(0,u.A)("col",!t.hidden&&wn.docItemCol),children:[(0,o.jsx)(kn,{metadata:n}),(0,o.jsx)(w,{}),(0,o.jsxs)("div",{className:wn.docItemContainer,children:[(0,o.jsxs)("article",{children:[(0,o.jsx)(gn,{}),(0,o.jsx)(_,{}),t.mobile,(0,o.jsx)(on,{children:e}),(0,o.jsx)(D,{})]}),(0,o.jsx)(g,{})]})]}),t.desktop&&(0,o.jsx)("div",{className:"col col--3",children:t.desktop})]})}function Tn(e){const t=`docs-doc-id-${e.content.metadata.id}`,n=e.content;return(0,o.jsx)(c,{content:e.content,children:(0,o.jsxs)(a.e3,{className:t,children:[(0,o.jsx)(d,{}),(0,o.jsx)(_n,{children:(0,o.jsx)(n,{})})]})})}},8426:(e,t)=>{function n(e){let t,n=[];for(let s of e.split(",").map((e=>e.trim())))if(/^-?\d+$/.test(s))n.push(parseInt(s,10));else if(t=s.match(/^(-?\d+)(-|\.\.\.?|\u2025|\u2026|\u22EF)(-?\d+)$/)){let[e,s,a,i]=t;if(s&&i){s=parseInt(s),i=parseInt(i);const e=s<i?1:-1;"-"!==a&&".."!==a&&"\u2025"!==a||(i+=e);for(let t=s;t!==i;t+=e)n.push(t)}}return n}t.default=n,e.exports=n},8453:(e,t,n)=>{"use strict";n.d(t,{R:()=>o,x:()=>r});var s=n(6540);const a={},i=s.createContext(a);function o(e){const t=s.useContext(i);return s.useMemo((function(){return"function"==typeof e?e(t):{...t,...e}}),[t,e])}function r(e){let t;return t=e.disableParentContext?"function"==typeof e.components?e.components(a):e.components||a:o(e.components),s.createElement(i.Provider,{value:t},e.children)}}}]);