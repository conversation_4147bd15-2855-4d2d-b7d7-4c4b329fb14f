import{_ as e,l as o,M as i,e as n,N as p}from"./index-BRXgCuaM.js";import{p as g}from"./radar-MK3ICKWK-DDeSu9Uo.js";import"./vendor-BEryHLmj.js";import"./pdfjs-CcP0jMWS.js";import"./_baseUniq-D55UJ4ML.js";import"./_basePickBy-DbgQ2uk7.js";import"./clone-BU7fntfk.js";var v={parse:e(async r=>{const a=await g("info",r);o.debug(a)},"parse")},d={version:p.version},m=e(()=>d.version,"getVersion"),c={getVersion:m},l=e((r,a,s)=>{o.debug(`rendering info diagram
`+r);const t=i(a);n(t,100,400,!0),t.append("g").append("text").attr("x",100).attr("y",40).attr("class","version").attr("font-size",32).style("text-anchor","middle").text(`v${s}`)},"draw"),f={draw:l},D={parser:v,db:c,renderer:f};export{D as diagram};
